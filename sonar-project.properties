sonar.host.url=https://sonar2.mypaas.com.cn
sonar.login=****************************************
sonar.projectKey=cn.com.mypaas:bigdata-dimensional-modeling
sonar.projectName=bigdata-indicator-modeling
sonar.sources=./
sonar.exclusions=./vendor/**,./proto,./pkg/build_plan
sonar.language=go
sonar.sourceEncoding=UTF-8
sonar.projectVersion=1.1

sonar.issue.ignore.multicriteria=c1,c2,c3,c4,c5,c6,c7
# Unused parameters are misleading
sonar.issue.ignore.multicriteria.c1.ruleKey=go:S1172
sonar.issue.ignore.multicriteria.c1.resourceKey=**/*.go
# With default provided regular expression: ^[a-zA-Z0-9]+$
sonar.issue.ignore.multicriteria.c2.ruleKey=go:S100
sonar.issue.ignore.multicriteria.c2.resourceKey=**/*.go
# Cognitive Complexity
sonar.issue.ignore.multicriteria.c3.ruleKey=go:S3776
sonar.issue.ignore.multicriteria.c3.resourceKey=**/*.go
# Merging collapsible if statements increases the code's readability.
sonar.issue.ignore.multicriteria.c4.ruleKey=go:S1066
sonar.issue.ignore.multicriteria.c4.resourceKey=**/*.go
# A long parameter list
sonar.issue.ignore.multicriteria.c5.ruleKey=go:S107
sonar.issue.ignore.multicriteria.c5.resourceKey=**/api_dataset_reader.go
# Take the required action to fix the issue indicated by this "TODO" comment
sonar.issue.ignore.multicriteria.c6.ruleKey=go:S1135
sonar.issue.ignore.multicriteria.c6.resourceKey=**/*.go
# IP addresses should not be hardcoded
sonar.issue.ignore.multicriteria.c7.ruleKey=go:S1313
sonar.issue.ignore.multicriteria.c7.resourceKey=**/*.go

