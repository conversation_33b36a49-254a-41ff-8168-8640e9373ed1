package indicator_repository

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
)

type IndicatorModelAndFieldInfo struct {
	mysql.IndicatorField
	IndicatorUnit string `gorm:"column:indicator_unit" json:"indicator_unit" form:"indicator_unit"`
	ModelCode     string `gorm:"column:model_code" json:"model_code" form:"model_code"`
	ModelName     string `gorm:"column:model_name" json:"model_name" form:"model_name"`
	ModelNameEn   string `gorm:"column:model_name_en" json:"model_name_en" form:"model_name_en"`
	// ViewContent string `gorm:"column:view_content" json:"view_content" form:"view_content"`
}
