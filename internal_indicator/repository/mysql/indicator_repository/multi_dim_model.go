package indicator_repository

import (
	"context"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
	"gorm.io/gorm"
)

func (c *IndicatorRepository) GetMultiDimModelByCode(ctx context.Context, code string, environment string) (*mysql.MultiDimModel, error) {
	info := new(mysql.MultiDimModel)
	tableName := "dap_m_multi_dim_model"
	err := c.db.Table(tableName).Where("code = ? and environment = ?", code, environment).Take(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return info, nil
}
