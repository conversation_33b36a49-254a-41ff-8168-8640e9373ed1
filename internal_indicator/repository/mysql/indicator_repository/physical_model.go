package indicator_repository

import (
	"context"
	"github.com/tidwall/gjson"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"
	"gorm.io/gorm"
)

func (c *IndicatorRepository) GetPhysicalModelAndSubjectListWithoutAds(ctx context.Context, multiCode string, environment string, applyStatus int) ([]*mysql.PhysicalModelAndSubject, string, error) {
	list := make([]*mysql.PhysicalModelAndSubject, 0)

	physicalModelCodeList := make([]string, 0)
	physicalModelCodeMap := make(map[string]string)
	linkData := ""

	multiInfo := new(mysql.MultiDimModel)
	err := c.db.Where("code = ? and environment = ? and apply_status = ?", multiCode, string(global.Prod), applyStatus).Find(&multiInfo).Error
	if err != nil {
		return nil, linkData, err
	}
	if multiInfo.ID == "" {
		return list, linkData, nil
	}

	viewContent := gjson.Parse(multiInfo.ViewContent)
	nodeList := viewContent.Get("node").Array()
	for _, node := range nodeList {
		code := node.Get("code_id").String()
		id := node.Get("id").String()
		physicalModelCodeList = append(physicalModelCodeList, code)
		physicalModelCodeMap[code] = id
	}
	linkData = viewContent.Get("link").String()

	err = c.db.Table("dap_m_physical_model").
		Select("dap_m_physical_model.*,dap_m_subject.name as subject_name").
		Joins("left join dap_m_subject on dap_m_subject.id = dap_m_physical_model.subject_id").
		Where("dap_m_physical_model.code in ? and dap_m_physical_model.environment = ? and dap_m_physical_model.is_del = 0 and apply_status = ?", physicalModelCodeList, environment, applyStatus).Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, linkData, err
	}
	for index, _ := range list {
		linkID := physicalModelCodeMap[list[index].Code]
		list[index].LinkID = linkID
	}
	listMap := make(map[string]*mysql.PhysicalModelAndSubject, 0)
	for _, v := range list {
		listMap[v.Code] = v
	}

	orderList := make([]*mysql.PhysicalModelAndSubject, 0)
	for _, v := range physicalModelCodeList {
		if info, ok := listMap[v]; ok {
			orderList = append(orderList, info)
		}
	}
	return orderList, linkData, nil
}

func (c *IndicatorRepository) GetMultiModelAndSubjectList(ctx context.Context, environment string, applyStatus int) ([]*mysql.MultiModelAndSubject, error) {
	list := make([]*mysql.MultiModelAndSubject, 0)
	err := c.db.Table("dap_m_multi_dim_model").Select("dap_m_multi_dim_model.*,dap_m_subject.name as subject_name").Joins("left join dap_m_subject on dap_m_subject.id = dap_m_multi_dim_model.subject_id").Where("dap_m_multi_dim_model.environment = ? and apply_status = ?", environment, applyStatus).Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return list, nil
}

func (c *IndicatorRepository) GetPhysicalModelByCode(ctx context.Context, code string, environment string) (*mysql.PhysicalModel, error) {
	info := new(mysql.PhysicalModel)
	db := c.db.Where("code = ? and environment = ? and is_del = 0", code, environment)
	err := db.Find(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return info, nil
}

var physicalModelFieldSelectNames = utils.LoadSelectFieldNamesUnsafe(mysql.PhysicalModelField{}, "DeriveFieldContent")

func (c *IndicatorRepository) BatchGetPhysicalModelFieldListById(ctx context.Context, ids []string) ([]*mysql.PhysicalModelField, error) {
	fields := make([]*mysql.PhysicalModelField, 0)
	tableName := "dap_m_physical_model_field"
	err := c.db.Table(tableName).Select(physicalModelFieldSelectNames).Where("physical_model_id in ?", ids).Find(&fields).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return fields, nil
}

var physicalModelSelectNames = utils.LoadSelectFieldNamesUnsafe(mysql.PhysicalModel{}, "IndividualType")

func (c *IndicatorRepository) BatchGetPhysicalModelInfoByCode(ctx context.Context, codes []string, environment string) ([]*mysql.PhysicalModel, error) {
	tableName := "dap_m_physical_model"
	list := make([]*mysql.PhysicalModel, 0)
	err := c.db.Table(tableName).Select(physicalModelSelectNames).Where("code in ? and environment = ? and is_del = 0", codes, environment).Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return list, nil
}
