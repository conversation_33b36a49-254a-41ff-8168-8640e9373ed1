package indicator_repository

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/di_helper"

	"gorm.io/gorm"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
)

type IndicatorRepository struct {
	Project    string
	db         *gorm.DB
	localCache *cache.LocalCache
}

func NewIndicatorRepository(db *gorm.DB, projectCode string) *IndicatorRepository {
	return &IndicatorRepository{
		Project:    projectCode,
		db:         db,
		localCache: di_helper.LocalCache(),
	}
}

func (c *IndicatorRepository) GetIndicatorInfoByCode(ctx context.Context, code string, environment string) (*mysql.Indicator, error) {
	info := new(mysql.Indicator)
	db := c.db.Where("environment = ? and code = ?", environment, code)
	err := db.Find(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return info, err
	}
	return info, nil
}

func (c *IndicatorRepository) GetIndicatorListByCodes(ctx context.Context, codes []string, environment string) ([]*mysql.Indicator, error) {
	list := make([]*mysql.Indicator, 0)
	err := c.db.Select("view_content,code").Where("environment = ? and code in ?", environment, codes).Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return list, nil
}

func (c *IndicatorRepository) GetPhysicalModelInfoByCode(ctx context.Context, modelCode string, environment string) (*mysql.PhysicalModel, error) {
	indicator := new(mysql.Indicator)
	tx := c.db.Where("code = ? and environment = ?", modelCode, environment).Find(&indicator)
	if tx.Error != nil && tx.Error != gorm.ErrRecordNotFound {
		return nil, tx.Error
	}
	if indicator.PhysicalModelCode == "" {
		indicator.PhysicalModelCode = modelCode
	}

	info := new(mysql.PhysicalModel)
	err := c.db.Where("code = ? and environment = ?", indicator.PhysicalModelCode, environment).Find(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return info, nil
}

func (c *IndicatorRepository) GetIndicatorModelCodeByIndicatorCodes(ctx context.Context, indicatorCodes []string, environment string) ([]string, error) {
	codes := make([]string, 0)
	err := c.db.Raw("select distinct(dap_m_indicator.code) from dap_m_indicator left join dap_m_indicator_field on dap_m_indicator.id=dap_m_indicator_field.indicator_id where dap_m_indicator.environment = ? and dap_m_indicator_field.code in ?", environment, indicatorCodes).Find(&codes).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return codes, nil
}

func (c *IndicatorRepository) GetIndicatorModelAndFieldList(ctx context.Context, modelCodes []string, environment string) ([]*IndicatorModelAndFieldInfo, error) {
	list := make([]*IndicatorModelAndFieldInfo, 0)
	notFoundList := make([]*IndicatorModelAndFieldInfo, 0)
	indicatorModelAndFieldMap := make(map[string][]*IndicatorModelAndFieldInfo, 0)
	if len(modelCodes) == 0 {
		return list, nil
	}
	localCacheBaseKey := "IndicatorModelAndField"
	notFoundModelCode := make([]string, 0)
	for _, code := range modelCodes {
		cacheList := make([]*IndicatorModelAndFieldInfo, 0)
		value, found := c.localCache.Get(ctx, fmt.Sprintf("%s_%s_%s", localCacheBaseKey, c.Project, code))
		if found {
			err := json.Unmarshal(value.([]byte), &cacheList)
			if err != nil {
				return nil, err
			}
			list = append(list, cacheList...)
		} else {
			notFoundModelCode = append(notFoundModelCode, code)
		}
	}
	if len(notFoundModelCode) == 0 {
		return list, nil
	}

	db := c.db.Table("dap_m_indicator_field as idf").Select("idf.*, dap_m_indicator_dict_field.unit as indicator_unit,dap_m_indicator.name as model_name, dap_m_indicator.table_name as model_name_en,dap_m_indicator.code as model_code").
		Joins("left join dap_m_indicator on dap_m_indicator.id = idf.indicator_id").
		Joins("left join dap_m_indicator_dict_field on dap_m_indicator_dict_field.indicator_code = idf.code").
		Where("dap_m_indicator.apply_status = 1 and dap_m_indicator.environment = ?", environment)

	if len(notFoundModelCode) > 0 {
		db = db.Where("dap_m_indicator.code in ?", notFoundModelCode)
	}
	err := db.Debug().Find(&notFoundList).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	//设置本地缓存
	for _, info := range notFoundList {
		list = append(list, info)
		indicatorModelAndFieldMap[info.ModelCode] = append(indicatorModelAndFieldMap[info.ModelCode], info)
	}
	for _, v := range indicatorModelAndFieldMap {
		listBytes, err := json.Marshal(v)
		if err != nil {
			return nil, err
		}
		c.localCache.Set(ctx, fmt.Sprintf("%s_%s_%s", localCacheBaseKey, c.Project, v[0].ModelCode), listBytes, 5*time.Minute)
	}
	return list, nil
}

func (c *IndicatorRepository) GetIndicatorModelCodeByCodesAndKeyWords(ctx context.Context, codes []string, keywords []string, environment string) ([]string, error) {
	codesList := make([]string, 0)
	if len(codes) == 0 && len(keywords) == 0 {
		return codesList, nil
	}
	db := c.db.Table("dap_m_indicator").Select("distinct(dap_m_indicator.code)").Joins("left join dap_m_indicator_field idf on dap_m_indicator.id = idf.indicator_id").Where("dap_m_indicator.apply_status = 1 and dap_m_indicator.environment = ?", environment)
	if len(codes) > 0 {
		db = db.Where("idf.code  in ?", codes)
	}
	whereSqlList := make([]string, 0)
	for _, keyword := range keywords {
		rg, _ := regexp.Compile("%") //关键字如果有%,需要特殊处理下
		keyword := rg.ReplaceAllString(keyword, "\\%")
		keyword = fmt.Sprintf("%%%s%%", keyword)
		whereSql := fmt.Sprintf("idf.name_cn like '%s' or idf.name like '%s' or dap_m_indicator.name like '%s'", keyword, keyword, keyword)
		whereSqlList = append(whereSqlList, whereSql)
	}
	if len(whereSqlList) > 0 {
		db = db.Where(strings.Join(whereSqlList, " or "))
	}
	err := db.Find(&codesList).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	return codesList, nil
}

func (c *IndicatorRepository) GetIndicatorModelAndFieldInfoByCode(ctx context.Context, code string, environment string) (*IndicatorModelAndFieldInfo, error) {
	info := new(IndicatorModelAndFieldInfo)
	db := c.db.Table("dap_m_indicator_field as idf").Select("idf.*,dap_m_indicator.name as model_name, dap_m_indicator.table_name as model_name_en,dap_m_indicator.code as model_code").
		Joins("left join dap_m_indicator on dap_m_indicator.id = idf.indicator_id").Where("dap_m_indicator.apply_status = 1 and dap_m_indicator.environment = ? and idf.code = ?", environment, code)
	err := db.Find(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return info, nil
}

func (c *IndicatorRepository) GetIndicatorViewContentByCode(ctx context.Context, code string, environment string) (string, error) {
	info := ""
	err := c.db.Table("dap_m_indicator").Where("environment = ? and code = ?", environment, code).Find(&info).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return info, err
	}
	return info, nil
}

func (c *IndicatorRepository) GetIndicatorViewByCodeEnv(ctx context.Context, codes []string, environment string) ([]*mysql.Indicator, error) {
	infos := make([]*mysql.Indicator, 0)
	err := c.db.Table("dap_m_indicator").Where("environment = ? and code in(?)", environment, codes).Find(&infos).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return infos, err
	}
	return infos, nil
}

func (c *IndicatorRepository) BatchGetIndicator(ctx context.Context, codes []string, environment string, applyStatus int) ([]*mysql.Indicator, error) {
	infos := make([]*mysql.Indicator, 0)
	db := c.db
	if len(codes) > 0 {
		db = db.Where("code in (?)", codes)
	}
	err := db.Table("dap_m_indicator").Where("environment = ? and apply_status = ?", environment, applyStatus).Find(&infos).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return infos, err
	}
	return infos, nil
}

func (c *IndicatorRepository) GetIndicatorByTableName(ctx context.Context, tableName string, environment string) (mysql.Indicator, error) {
	indicator := mysql.Indicator{}
	db := c.db

	err := db.Table("dap_m_indicator").Where("table_name = ? and environment = ?", tableName, environment).Find(&indicator).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return indicator, err
	}

	return indicator, nil
}

func (c *IndicatorRepository) GetAllIndicatorFieldInfoByIndicatorId(ctx context.Context, id string) ([]mysql.IndicatorField, error) {
	infos := make([]mysql.IndicatorField, 0)
	db := c.db.Where("indicator_id =  ?", id)

	err := db.Find(&infos).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return infos, err
	}

	return infos, nil
}
