package indicator_repository

import (
	"context"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
	"gorm.io/gorm"
)

func (c *IndicatorRepository) GetSubjectListByIDs(ctx context.Context, subjectIDs []string) ([]*mysql.Subject, error) {
	list := make([]*mysql.Subject, 0)
	db := c.db
	if len(subjectIDs) != 0 {
		db = db.Where("id in ?", subjectIDs)
	}
	err := db.Table("dap_m_subject").Where("level = 1").Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return list, nil
}

func (c *IndicatorRepository) GetAllSubjectList(ctx context.Context) ([]*mysql.Subject, error) {
	list := make([]*mysql.Subject, 0)
	err := c.db.Table("dap_m_subject").Where("level = 1").Find(&list).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return list, nil
}
