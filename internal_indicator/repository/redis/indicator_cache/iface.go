package indicator_cache

import (
	"context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
)

type IndicatorModelCache interface {
	GetProdAdsViewByCode(ctx context.Context, code string) (info *mysql.Indicator, err error)
	SetProdAdsViewByCode(ctx context.Context, code string, info *mysql.Indicator)
	GetProjectProdMultiDimModelByCode(ctx context.Context, code string) (info *mysql.MultiDimModel, err error)
	SetProdMultiDimModelByCode(ctx context.Context, code string, info *mysql.MultiDimModel)
	GetProjectProdPhysicalModelByCode(ctx context.Context, code string) (info *mysql.PhysicalModel, fields []*mysql.PhysicalModelField, err error)
	SetProdPhysicalModelByCode(ctx context.Context, code string, info *mysql.PhysicalModel, fields []*mysql.PhysicalModelField)
}

type IndicatorCardCache interface {
	GetSubject(ctx context.Context, subjectIDs []string) (ans []*mysql.Subject, err error)
	SetSubject(ctx context.Context, subjectIDs []string, ans []*mysql.Subject)
}
