package indicator_cache

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
)

func (s *IndicatorCache) adsViewKey(project string, code string) string {
	return fmt.Sprintf("%s:ads_view:prod:%s", project, code)
}

func (s *IndicatorCache) GetProdAdsViewByCode(ctx context.Context, code string) (info *mysql.Indicator, err error) {
	return s.getProdAdsViewByCode(ctx, s.project, code)
}

func (s *IndicatorCache) getProdAdsViewByCode(ctx context.Context, project string, code string) (info *mysql.Indicator, err error) {
	result := s.r.Client.Get(ctx, s.adsViewKey(project, code))
	if result.Err() != nil {
		err = result.Err()
		global.Logger.Debugf("[IndicatorCache]GetProdAdsViewByCode[%s, %s] failed: %s", project, code, err.Error())
		return
	}
	b, err := result.Bytes()
	if err != nil {
		return
	}
	info = &mysql.Indicator{}
	err = json.Unmarshal(b, info)
	if err != nil {
		return
	}
	global.Logger.Debugf("[IndicatorCache]GetProdAdsViewByCode[%s, %s] succ", project, code)
	return
}

// SetProdAdsViewByCode 应用表视图缓存，在modeling中发布/下线时删除
func (s *IndicatorCache) SetProdAdsViewByCode(ctx context.Context, code string, info *mysql.Indicator) {
	var err error
	defer func() {
		if err != nil {
			global.Logger.Debugf("[IndicatorCache]SetProdPhysicalModelByCode[%s, %s] failed: %s", s.project, code, err.Error())
		}
	}()
	raw, err := json.Marshal(info)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, s.adsViewKey(s.project, code), raw, defaultExpire())
	if result.Err() != nil {
		global.Logger.Debugf("[IndicatorCache]SetProdAdsViewByCode[%s, %s] failed: %s", s.project, code, result.Err().Error())
		return
	}
	global.Logger.Debugf("[IndicatorCache]SetProdAdsViewByCode[%s, %s] succ", s.project, code)
	return
}
