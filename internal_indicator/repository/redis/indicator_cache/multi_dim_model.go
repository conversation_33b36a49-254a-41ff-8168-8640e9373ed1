package indicator_cache

import (
	"context"
	"encoding/json"
	"fmt"
	r "github.com/go-redis/redis/v8"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
)

func (s *IndicatorCache) multiDimModelKey(project string, code string) string {
	return fmt.Sprintf("%s:multi_model:prod:%s", project, code)
}

func (s *IndicatorCache) GetProjectProdMultiDimModelByCode(ctx context.Context, code string) (info *mysql.MultiDimModel, err error) {
	// 先尝试从本空间获取, 如果未获取到且有对应的标准空间, 尝试从标准空间获取
	info, err = s.getProdMultiDimModelByCode(ctx, s.project, code)
	if err != nil && err == r.Nil {
		return
	}
	if info != nil {
		return
	}
	return
}

func (s *IndicatorCache) getProdMultiDimModelByCode(ctx context.Context, project string, code string) (info *mysql.MultiDimModel, err error) {
	result := s.r.Client.Get(ctx, s.multiDimModelKey(project, code))
	if result.Err() != nil {
		err = result.Err()
		global.Logger.Debugf("[IndicatorCache]GetProdMultiDimModelByCode[%s, %s] failed: %s", project, code, err.Error())
		return
	}
	b, err := result.Bytes()
	if err != nil {
		return
	}
	info = &mysql.MultiDimModel{}
	err = json.Unmarshal(b, info)
	if err != nil {
		return
	}
	global.Logger.Debugf("[IndicatorCache]GetProdMultiDimModelByCode[%s, %s] succ", project, code)
	return
}

// SetProdMultiDimModelByCode 多维缓存，在modeling中发布/下线时删除
func (s *IndicatorCache) SetProdMultiDimModelByCode(ctx context.Context, code string, info *mysql.MultiDimModel) {
	var err error
	defer func() {
		if err != nil {
			global.Logger.Debugf("[IndicatorCache]SetProdPhysicalModelByCode[%s, %s] failed: %s", s.project, code, err.Error())
		}
	}()
	raw, err := json.Marshal(info)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, s.multiDimModelKey(s.project, code), raw, defaultExpire())
	if result.Err() != nil {
		global.Logger.Debugf("[IndicatorCache]SetProdMultiDimModelByCode[%s, %s] failed: %s", s.project, code, result.Err().Error())
		return
	}
	global.Logger.Debugf("[IndicatorCache]SetProdMultiDimModelByCode[%s, %s] succ", s.project, code)
	return
}

func (s *IndicatorCache) GetMultiModelAndSubjectList(ctx context.Context, environment string, applyStatus int) (ans []*mysql.MultiModelAndSubject, err error) {
	var key = global.MultiDimWithSubjectListCacheKey(s.project, environment, applyStatus)
	defer func() {
		if err != nil {
			global.Logger.Errorf("[IndicatorCache]GetPhysicalModelByCodes[%s] failed: %s",
				key, err.Error())
		}
	}()
	result := s.r.Client.Get(ctx, key)
	if result.Err() != nil {
		err = result.Err()
		return
	}
	var b []byte
	b, err = result.Bytes()
	if err != nil {
		return
	}
	ans = make([]*mysql.MultiModelAndSubject, 0)
	err = json.Unmarshal(b, &ans)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorCache) SetMultiModelAndSubjectList(ctx context.Context, environment string, applyStatus int, models []*mysql.MultiModelAndSubject) {
	var err error
	var key = global.MultiDimWithSubjectListCacheKey(s.project, environment, applyStatus)
	defer func() {
		if err != nil {
			global.Logger.Errorf("[RedisCache]SetMultiModelAndSubjectList[%s] failed: %s", key, err.Error())
		}
	}()
	var raw []byte
	raw, err = json.Marshal(models)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, key, raw, shortExpire())
	if result.Err() != nil {
		err = result.Err()
		return
	}
	return
}
