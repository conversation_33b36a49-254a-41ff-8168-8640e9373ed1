package indicator_cache

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
	"strings"
)

func (s *IndicatorCache) physicalModelKey(project string, code string) string {
	return fmt.Sprintf("%s:physical_model:prod:%s", project, code)
}

type physicalModelWithFields struct {
	Info   *mysql.PhysicalModel        `json:"info"`
	Expand *mysql.PhysicalModelExpand  `json:"expand"`
	Fields []*mysql.PhysicalModelField `json:"fields"`
}

func (s *IndicatorCache) GetProjectProdPhysicalModelByCode(ctx context.Context, code string) (info *mysql.PhysicalModel, fields []*mysql.PhysicalModelField, err error) {
	return s.getProdPhysicalModelByCode(ctx, s.project, code)
}

func (s *IndicatorCache) getProdPhysicalModelByCode(ctx context.Context, project string, code string) (info *mysql.PhysicalModel, fields []*mysql.PhysicalModelField, err error) {
	result := s.r.Client.Get(ctx, s.physicalModelKey(project, code))
	if result.Err() != nil {
		err = result.Err()
		global.Logger.Debugf("[IndicatorCache]GetProdPhysicalModelByCode[%s, %s] failed: %s", project, code, err.Error())
		return
	}
	b, err := result.Bytes()
	if err != nil {
		return
	}
	modelWithFields := &physicalModelWithFields{}
	err = json.Unmarshal(b, modelWithFields)
	if err != nil {
		return
	}
	info = modelWithFields.Info
	fields = modelWithFields.Fields
	global.Logger.Debugf("[IndicatorCache]GetProdPhysicalModelByCode[%s, %s] succ", project, code)
	return
}

// SetProdPhysicalModelByCode 物理模型缓存，在modeling中发布/下线时删除，应用表未缓存字段信息
func (s *IndicatorCache) SetProdPhysicalModelByCode(ctx context.Context, code string, info *mysql.PhysicalModel, fields []*mysql.PhysicalModelField) {
	var err error
	defer func() {
		if err != nil {
			global.Logger.Debugf("[IndicatorCache]SetProdPhysicalModelByCode[%s, %s] failed: %s", s.project, code, err.Error())
		}
	}()
	modelWithFields := &physicalModelWithFields{
		Info:   info,
		Expand: nil,
		Fields: fields,
	}
	raw, err := json.Marshal(modelWithFields)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, s.physicalModelKey(s.project, code), raw, defaultExpire())
	if result.Err() != nil {
		global.Logger.Debugf("[IndicatorCache]SetProdPhysicalModelByCode[%s, %s] failed: %s", s.project, code, result.Err().Error())
		return
	}
	global.Logger.Debugf("[IndicatorCache]SetProdPhysicalModelByCode[%s, %s] succ", s.project, code)
	return
}

func (s *IndicatorCache) GetPhysicalModelWithSubjectListOfMultiDim(ctx context.Context, multiCode string,
	environment string, applyStatus int) (ans []*mysql.PhysicalModelAndSubject, linkData string, err error) {
	defer func() {
		if err != nil {
			global.Logger.Errorf("[IndicatorCache]GetPhysicalModelWithSubjectListOfMultiDim[%s, %s, %s, %d] failed: %s",
				s.project, multiCode, environment, applyStatus, err.Error())
		}
	}()
	result := s.r.Client.Get(ctx, global.PhysicalModelWithSubjectListOfMultiDimCacheKey(s.project, multiCode, environment, applyStatus))
	if result.Err() != nil {
		err = result.Err()
		return
	}
	var b []byte
	b, err = result.Bytes()
	if err != nil {
		return
	}
	var content = PhysicalModelWithSubjectListOfMultiDim{}
	err = json.Unmarshal(b, &content)
	if err != nil {
		return
	}
	ans = content.Models
	linkData = content.LinkData
	return
}

func (s *IndicatorCache) SetPhysicalModelWithSubjectListOfMultiDim(ctx context.Context, multiCode string, environment string,
	applyStatus int, models []*mysql.PhysicalModelAndSubject, linkData string) {
	var err error
	var key = global.PhysicalModelWithSubjectListOfMultiDimCacheKey(s.project, multiCode, environment, applyStatus)
	defer func() {
		if err != nil {
			global.Logger.Errorf("[RedisCache]SetPhysicalModelWithSubjectListOfMultiDim[%s] failed: %s", key, err.Error())
		}
	}()
	var content = PhysicalModelWithSubjectListOfMultiDim{
		Models:   models,
		LinkData: linkData,
	}
	var raw []byte
	raw, err = json.Marshal(content)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, key, raw, shortExpire())
	if result.Err() != nil {
		err = result.Err()
		return
	}
	return
}

func (s *IndicatorCache) GetPhysicalModelByCodes(ctx context.Context, codes []string, environment string) (ans []*mysql.PhysicalModel, err error) {
	defer func() {
		if err != nil {
			global.Logger.Errorf("[IndicatorCache]GetPhysicalModelByCodes[%s, %s, %s] failed: %s",
				s.project, strings.Join(codes, ","), environment, err.Error())
		}
	}()
	result := s.r.Client.Get(ctx, global.PhysicalModelListByCodeCacheKey(s.project, codes, environment))
	if result.Err() != nil {
		err = result.Err()
		return
	}
	var b []byte
	b, err = result.Bytes()
	if err != nil {
		return
	}
	ans = make([]*mysql.PhysicalModel, 0)
	err = json.Unmarshal(b, &ans)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorCache) SetPhysicalModelByCodes(ctx context.Context, codes []string, environment string, models []*mysql.PhysicalModel) {
	var err error
	var key = global.PhysicalModelListByCodeCacheKey(s.project, codes, environment)
	defer func() {
		if err != nil {
			global.Logger.Errorf("[RedisCache]SetPhysicalModelByCodes[%s] failed: %s", key, err.Error())
		}
	}()
	var raw []byte
	raw, err = json.Marshal(models)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, key, raw, shortExpire())
	if result.Err() != nil {
		err = result.Err()
		return
	}
	return
}

func (s *IndicatorCache) GetPhysicalModelFieldsByIds(ctx context.Context, ids []string) (ans []*mysql.PhysicalModelField, err error) {
	defer func() {
		if err != nil {
			global.Logger.Errorf("[IndicatorCache]GetPhysicalModelFieldsByIds[%s, %s] failed: %s",
				s.project, strings.Join(ids, ","), err.Error())
		}
	}()
	result := s.r.Client.Get(ctx, global.PhysicalModelFieldsByIdsCacheKey(s.project, ids))
	if result.Err() != nil {
		err = result.Err()
		return
	}
	var b []byte
	b, err = result.Bytes()
	if err != nil {
		return
	}
	ans = make([]*mysql.PhysicalModelField, 0)
	err = json.Unmarshal(b, &ans)
	if err != nil {
		return
	}
	return
}

func (s *IndicatorCache) SetPhysicalModelFieldsByIds(ctx context.Context, ids []string, fields []*mysql.PhysicalModelField) {
	var err error
	var key = global.PhysicalModelFieldsByIdsCacheKey(s.project, ids)
	defer func() {
		if err != nil {
			global.Logger.Errorf("[RedisCache]SetPhysicalModelFieldsByIds[%s] failed: %s", key, err.Error())
		}
	}()
	var raw []byte
	raw, err = json.Marshal(fields)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, key, raw, shortExpire())
	if result.Err() != nil {
		err = result.Err()
		return
	}
	return
}
