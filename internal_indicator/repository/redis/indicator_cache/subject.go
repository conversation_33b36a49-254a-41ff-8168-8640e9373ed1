package indicator_cache

import (
	"context"
	"encoding/json"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
)

func (s *IndicatorCache) GetSubject(ctx context.Context, subjectIDs []string) (ans []*mysql.Subject, err error) {
	result := s.r.Client.Get(ctx, global.SubjectCacheKey(s.project, subjectIDs))
	if result.Err() != nil {
		err = result.Err()
		global.Logger.Debugf("[RedisCache]GetSubject[%s] failed: %s", global.SubjectCacheKey(s.project, subjectIDs), err.<PERSON>rror())
		return
	}
	var b []byte
	b, err = result.Bytes()
	if err != nil {
		global.Logger.Debugf("[RedisCache]GetSubject[%s] failed: %s", global.SubjectCacheKey(s.project, subjectIDs), err.<PERSON><PERSON><PERSON>())
		return
	}
	ans = make([]*mysql.Subject, 0)
	err = json.Unmarshal(b, &ans)
	if err != nil {
		global.Logger.Debugf("[RedisCache]GetSubject[%s] failed: %s", global.SubjectCacheKey(s.project, subjectIDs), err.Error())
		return
	}
	return
}

func (s *IndicatorCache) SetSubject(ctx context.Context, subjectIDs []string, info []*mysql.Subject) {
	var err error
	var key = global.SubjectCacheKey(s.project, subjectIDs)
	defer func() {
		if err != nil {
			global.Logger.Debugf("[RedisCache]SetSubject[%s] failed: %s", key, err.Error())
		}
	}()
	var raw []byte
	raw, err = json.Marshal(info)
	if err != nil {
		return
	}
	result := s.r.Client.SetNX(ctx, key, raw, shortExpire())
	if result.Err() != nil {
		global.Logger.Debugf("[RedisCache]SetSubject[%s] failed: %s", key, result.Err().Error())
		return
	}
	global.Logger.Debugf("[RedisCache]SetSubject [%s] succ", key)
	return
}
