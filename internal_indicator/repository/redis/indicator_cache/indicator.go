package indicator_cache

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/di_helper"
	"math/rand"
	"time"
)

var _ IndicatorModelCache = (*IndicatorCache)(nil)

type IndicatorCache struct {
	project string
	r       *cache.Redis
}

func NewIndicatorCache(project string) *IndicatorCache {
	return &IndicatorCache{
		r:       di_helper.Redis(),
		project: project,
	}
}

func defaultExpire() time.Duration {
	// rand 60-140
	random := time.Duration(60 + rand.Int63n(80))
	if global.AppConfig.DmpApi.DefaultCacheExpireHour > 0 {
		return global.AppConfig.DmpApi.DefaultCacheExpireHour * time.Hour * random / 100
	} else {
		return 15 * 24 * time.Hour * random / 100
	}
}

func shortExpire() time.Duration {
	// 统一缓存10~15分钟
	randomNumber := rand.Intn(6) + 10
	return time.Duration(randomNumber) * time.Minute
}
