package injection

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	multiDimModels "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/multi_dim_model"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
	"strings"
	"time"
)

var _ DependenceModel = (*DefaultDepModelImpl)(nil)

type DefaultDepModelImpl struct {
	logger              *logger.Logger
	modelCode           string
	multiDimViewContent *multiDimModels.NewMultiDimViewContent
	options             *modelOptions
	// context data
	DepMultiDimModel               bool
	physicalModel                  *mysql.PhysicalModel                   // 物理模型
	multiDimModel                  *mysql.MultiDimModel                   // 多维模型
	multiDimModelDepPhysicalModels []*mysql.PhysicalModel                 // 多维模型依赖模型
	physicalModelFieldsMap         map[string][]*mysql.PhysicalModelField // 物理模型字段
	MultiDimDepModelMap            map[string]string                      // 多维模型依赖模型
}

// NewDefaultDepModelImpl sql_build.DependenceModel的默认实现
func NewDefaultDepModelImpl(dependence *rpc_call.Dependence, multiDimViewContent *multiDimModels.NewMultiDimViewContent, options ...OptionFunc) *DefaultDepModelImpl {
	return &DefaultDepModelImpl{
		logger:              global.Logger,
		modelCode:           dependence.CodeId,
		multiDimViewContent: multiDimViewContent,
		DepMultiDimModel:    dependence.Category == baseProto.MultiDimCate,
		options:             newModelOptions(options...),
		MultiDimDepModelMap: make(map[string]string, 0),
	}
}

type OptionFunc func(s *modelOptions)

// WithLoadCache 加载发布态模型数据时使用缓存
func WithLoadCache(cache indicator_cache.IndicatorModelCache) OptionFunc {
	return func(option *modelOptions) {
		option.cache = cache
	}
}

type modelOptions struct {
	cache indicator_cache.IndicatorModelCache // 发布态模型缓存
}

func newModelOptions(withOptions ...OptionFunc) *modelOptions {
	// options
	options := &modelOptions{
		cache: nil,
	}
	for _, withOption := range withOptions {
		withOption(options)
	}
	return options
}

// PreLoad 预加载数据
func (s *DefaultDepModelImpl) PreLoad(ctx context.Context, repo *indicator_repository.IndicatorRepository, env string) (err error) {
	sctx, sp := trace_utils.StartSpanWithContext(ctx, "AnalyticDimImpl.PreLoad")
	sp.Name("method: AnalyticDimImpl.PreLoad")
	defer sp.End()

	if s.DepMultiDimModel {
		if s.multiDimViewContent == nil {
			// 获取多维模型信息
			s.multiDimModel, err = s.getMultiDimModelInfoByCode(sctx, repo, env)
			if err != nil {
				return errors.Wrapf(err, "获取应用表依赖多维模型<%s>失败", s.modelCode)
			}
			if s.multiDimModel.Code == "" {
				return fmt.Errorf("多维模型:%s 不存在", s.modelCode)
			}
			s.multiDimViewContent, err = loadMultiDimViewContentFromString(s.multiDimModel.ViewContent)
			if err != nil {
				err = errors.Wrapf(err, "解析多维模型内容失败")
				return
			}
		} else {
			var viewContentBytes []byte
			if viewContentBytes, err = json.Marshal(s.multiDimViewContent); err != nil {
				return
			}
			s.multiDimModel = &mysql.MultiDimModel{
				Code:        s.modelCode,
				ViewContent: string(viewContentBytes),
			}
		}
		// 获取多维模型依赖的物理模型信息
		depCodes := s.getMultiDimDepModelCodes(s.multiDimViewContent)
		s.multiDimModelDepPhysicalModels, s.physicalModelFieldsMap, err = s.batchGetPhysicalModelInfoByCode(sctx, depCodes, repo, env)
		if err != nil {
			return
		}
	} else {
		// 获取物理模型信息
		var models []*mysql.PhysicalModel
		models, s.physicalModelFieldsMap, err = s.batchGetPhysicalModelInfoByCode(sctx, []string{s.modelCode}, repo, env)
		if err != nil {
			err = errors.Wrapf(err, "获取应用表依赖模型<%s>失败", s.modelCode)
			return
		}
		if len(models) != 1 {
			err = errors.Errorf("获取应用表依赖模型<%s>失败", s.modelCode)
			return
		}
		s.physicalModel = models[0]
	}

	return
}

func (s *DefaultDepModelImpl) GetMeasureFields(ctx context.Context) ([]*proto.ModelField, error) {
	return nil, nil
}

func (s *DefaultDepModelImpl) GetAtomIndicatorFields(ctx context.Context) ([]*proto.ModelField, error) {
	var result = make([]*proto.ModelField, 0)
	if !s.DepMultiDimModel {
		for _, fields := range s.physicalModelFieldsMap {
			for _, field := range fields {
				if field.AssetType == "indicator" {
					result = append(result, &proto.ModelField{
						Id:        field.ID,
						Table:     s.physicalModel.PhysicalModelTableName,
						Field:     field.Name,
						FieldCn:   field.NameCn,
						FieldType: field.FieldType,
						Comment:   "",
					})
				}
			}
		}
		global.Logger.JsonTrace("[DefaultDepModel] GetAtomIndicatorFields: ", result)
		return result, nil
	}

	viewContent, _ := loadMultiDimViewContentFromString(s.multiDimModel.ViewContent)
	for _, node := range viewContent.Node {
		if fields, ok := s.physicalModelFieldsMap[node.CodeId]; ok {
			for _, field := range fields {
				if field.AssetType == "indicator" {
					result = append(result, &proto.ModelField{
						Table:     node.TableName,
						Field:     field.Name,
						FieldCn:   field.NameCn,
						FieldType: field.FieldType,
						Comment:   "",
					})
				}
			}
		}
	}
	global.Logger.JsonTrace("[DefaultDepModel] GetAtomIndicatorFields: ", result)
	return result, nil
}

func (s *DefaultDepModelImpl) GetAnalyzableDimensionFields(ctx context.Context) ([]*proto.ModelField, error) {
	var result = make([]*proto.ModelField, 0)
	if !s.DepMultiDimModel {
		for _, fields := range s.physicalModelFieldsMap {
			for _, field := range fields {
				if field.DimType != string(modelBase.IndicatorDimType) {
					result = append(result, &proto.ModelField{
						Id:        field.ID,
						Table:     s.physicalModel.PhysicalModelTableName,
						Field:     field.Name,
						FieldCn:   field.NameCn,
						FieldType: field.FieldType,
						Comment:   "",
					})
				}
			}
		}
		global.Logger.JsonTrace("[DefaultDepModel] GetAnalyzableDimensionFields: ", result)
		return result, nil
	}

	viewContent, _ := loadMultiDimViewContentFromString(s.multiDimModel.ViewContent)
	for _, node := range viewContent.Node {
		if fields, ok := s.physicalModelFieldsMap[node.CodeId]; ok {
			for _, field := range fields {
				if field.DimType != string(modelBase.IndicatorDimType) {
					result = append(result, &proto.ModelField{
						Id:        field.ID,
						Table:     node.TableName,
						Field:     field.Name,
						FieldCn:   field.NameCn,
						FieldType: field.FieldType,
						Comment:   "",
					})
				}
			}
		}
	}
	global.Logger.JsonTrace("[DefaultDepModel] GetAnalyzableDimensionFields: ", result)
	return result, nil
}

func (s *DefaultDepModelImpl) GetMultiDimDepModel() map[string]string {
	return s.MultiDimDepModelMap
}

func (s *DefaultDepModelImpl) GetModelRelation(ctx context.Context) (*common.ModelRelation, error) {
	if !s.DepMultiDimModel {
		result := &common.ModelRelation{
			Nodes: []*proto.ModelNode{{
				Code:  s.physicalModel.Code,
				Type:  proto.ModelNodeType(s.physicalModel.Category),
				Table: s.physicalModel.PhysicalModelTableName,
			}},
			Edges: nil,
		}
		global.Logger.JsonTrace("[DefaultDepModel] GetModelRelation Nodes: ", result.Nodes)
		return result, nil
	}
	var result = &common.ModelRelation{
		Nodes: make([]*proto.ModelNode, 0),
		Edges: make(map[proto.ModelNode][]*proto.ModelEdg),
	}
	viewContent, _ := loadMultiDimViewContentFromString(s.multiDimModel.ViewContent)
	var nodeMap = make(map[string]*proto.ModelNode)
	for _, node := range viewContent.Node {
		modelNode := &proto.ModelNode{
			Table: node.TableName,
			Code:  node.CodeId,
			Type:  proto.ModelNodeType(node.Category),
		}
		nodeMap[node.Id] = modelNode
		result.AddNode(modelNode)
	}
	for _, link := range viewContent.Link {
		vertex := nodeMap[link.LeftNodeId]
		edg := &proto.ModelEdg{
			RelationType:  proto.RelationType(link.JoinType),
			RelationNode:  *nodeMap[link.RightNodeId],
			RelationField: make([]proto.RelationField, 0),
			JoinDataType:  common.ConvertJoinFieldDataContrast(rpc_call.RelationBizType(link.JoinQuantityType)),
		}
		var isFirstField bool
		for _, joinField := range link.JoinFields {
			relation := proto.RelationField{
				Left: proto.ModelField{
					Table: vertex.Table,
					Field: joinField.LeftField.Name,
				},
				CompOpr: proto.CompOperateType(strings.ToLower(joinField.Operator)),
				Right: proto.ModelField{
					Table: joinField.RightValue.GetTableAliasOrName(),
					Field: joinField.RightValue.Name,
				},
			}
			if !isFirstField {
				relation.AssociatePrev = proto.NoAssociate
				isFirstField = true
			} else {
				relation.AssociatePrev = proto.AND
			}
			edg.RelationField = append(edg.RelationField, relation)
		}
		helper := NewConvertHelper()
		var err error
		edg.RelationCondition, err = helper.NewConditionToFilter("", link.JoinFactor, link.Combo)
		if err != nil {
			return nil, err
		}
		result.AddSingleEdge(vertex, edg)
	}
	global.Logger.JsonTrace("[DefaultDepModel] GetModelRelation Nodes: ", result.Nodes)
	for node, edges := range result.Edges {
		global.Logger.JsonTrace(fmt.Sprintf("[DefaultDepModel] GetModelRelation Edges<%s>: ", node.Table), edges)
	}
	return result, nil
}

func loadMultiDimViewContentFromString(raw string) (*multiDimModels.NewMultiDimViewContent, error) {
	var viewContent *multiDimModels.NewMultiDimViewContent
	err := json.Unmarshal([]byte(raw), &viewContent)
	return viewContent, err
}

func (s *DefaultDepModelImpl) getMultiDimDepModelCodes(viewContent *multiDimModels.NewMultiDimViewContent) (codes []string) {
	for _, node := range viewContent.Node {
		codes = append(codes, node.CodeId)
		s.MultiDimDepModelMap[node.TableName] = string(node.Category)
	}
	return codes
}

func (s *DefaultDepModelImpl) getMultiDimModelInfoByCode(
	ctx context.Context,
	repo *indicator_repository.IndicatorRepository,
	env string) (info *mysql.MultiDimModel, err error) {
	code := s.modelCode
	if env == string(global.Dev) || s.options.cache == nil {
		// 如果是开发环境或者未启用缓存，走db
		return repo.GetMultiDimModelByCode(ctx, code, env)
	}
	info, err = s.options.cache.GetProjectProdMultiDimModelByCode(ctx, code)
	if err == nil {
		return
	}
	info, err = repo.GetMultiDimModelByCode(ctx, code, env)
	if err != nil {
		return
	}
	s.options.cache.SetProdMultiDimModelByCode(ctx, code, info)
	return
}

func (s *DefaultDepModelImpl) batchGetPhysicalModelInfoByCode(ctx context.Context, codes []string,
	repo *indicator_repository.IndicatorRepository, env string) (depModels []*mysql.PhysicalModel, physicalModelFieldsMap map[string][]*mysql.PhysicalModelField, err error) {
	if env == string(global.Dev) || s.options.cache == nil {
		// 开发态和未启用缓存时不走缓存
		return s.batchGetPhysicalModelInfoByCodeWithDB(ctx, codes, repo, env)
	}

	// 尝试从缓存中获取模型数据
	depModels, physicalModelFieldsMap, err = s.batchGetPhysicalModelInfoByCodeWithCache(ctx, codes, s.options.cache)
	if err == nil {
		return
	}
	// 如果有任何模型取缓存失败简单处理，从数据库中重新获取
	depModels, physicalModelFieldsMap, err = s.batchGetPhysicalModelInfoByCodeWithDB(ctx, codes, repo, env)
	if err != nil {
		return
	}
	_ = s.batchSetStandardPhysicalModelInfoCache(ctx, s.options.cache, depModels, physicalModelFieldsMap)

	return
}

func (s *DefaultDepModelImpl) batchSetStandardPhysicalModelInfoCache(ctx context.Context, cache indicator_cache.IndicatorModelCache, models []*mysql.PhysicalModel, fields map[string][]*mysql.PhysicalModelField) error {
	for _, model := range models {
		modelFields := fields[model.Code]
		cache.SetProdPhysicalModelByCode(ctx, model.Code, model, modelFields)
	}
	return nil
}

func (s *DefaultDepModelImpl) batchGetPhysicalModelInfoByCodeWithCache(ctx context.Context, codes []string, cache indicator_cache.IndicatorModelCache) ([]*mysql.PhysicalModel, map[string][]*mysql.PhysicalModelField, error) {
	physicalModelFieldsMap := map[string][]*mysql.PhysicalModelField{}
	depModels := []*mysql.PhysicalModel{}
	var err error
	for _, code := range codes {
		var info *mysql.PhysicalModel
		var fields []*mysql.PhysicalModelField
		info, fields, err = cache.GetProjectProdPhysicalModelByCode(ctx, code)
		if err != nil {
			return nil, nil, err
		}
		depModels = append(depModels, info)
		physicalModelFieldsMap[info.Code] = fields
	}
	return depModels, physicalModelFieldsMap, nil
}

func (s *DefaultDepModelImpl) batchGetPhysicalModelInfoByCodeWithDB(ctx context.Context,
	codes []string,
	repo *indicator_repository.IndicatorRepository,
	env string) (models []*mysql.PhysicalModel, modelFieldsMap map[string][]*mysql.PhysicalModelField, err error) {
	modelFieldsMap = map[string][]*mysql.PhysicalModelField{}

	if cacheStore, found := ctx.Value(cache.CtxKeyInstance).(cache.LocalCacheI); found {
		type cacheData struct {
			models         []*mysql.PhysicalModel
			modelFieldsMap map[string][]*mysql.PhysicalModelField
		}
		cacheK := strings.Join([]string{"batchGetPhysicalModelInfoByCodeWithDB", strings.Join(codes, "_"), env}, ":")
		if v, got := cacheStore.Get(ctx, cacheK); got {
			s2 := v.(cacheData)
			models = s2.models
			modelFieldsMap = s2.modelFieldsMap
			return
		}
		defer func() {
			if err == nil {
				cacheStore.Set(ctx, cacheK, cacheData{
					models:         models,
					modelFieldsMap: modelFieldsMap,
				}, 5*time.Second)
			}
		}()
	}

	models, err = repo.BatchGetPhysicalModelInfoByCode(ctx, codes, env)
	if err != nil {
		err = errors.Wrapf(err, "获取多维模型<%s>依赖模型失败", s.modelCode)
		return
	}

	// 模型id映射
	modelMapWithID := map[string]*mysql.PhysicalModel{}
	modelMapWithCode := map[string]*mysql.PhysicalModel{}
	for _, model := range models {
		modelMapWithID[model.ID] = model
		modelMapWithCode[model.Code] = model
	}
	for _, code := range codes {
		if _, ok := modelMapWithCode[code]; !ok {
			err = errors.Errorf("获取模型<%s>失败, 模型不存在", code)
			return
		}
	}

	// 获取依赖模型的id列表
	modelIds := []string{}
	for _, model := range models {
		modelIds = append(modelIds, model.ID)
	}

	// 获取依赖模型的字段信息
	var fields []*mysql.PhysicalModelField
	fields, err = repo.BatchGetPhysicalModelFieldListById(ctx, modelIds)
	if err != nil {
		err = errors.Wrapf(err, "获取模型字段失败")
		return
	}

	for _, field := range fields {
		model := modelMapWithID[field.PhysicalModelId]
		modelFields, ok := modelFieldsMap[model.Code]
		if !ok {
			modelFields = make([]*mysql.PhysicalModelField, 0)
		}
		modelFields = append(modelFields, field)
		modelFieldsMap[model.Code] = modelFields
	}

	return
}
