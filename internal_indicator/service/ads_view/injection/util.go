package injection

import (
	"fmt"
	"regexp"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/moql"

	"github.com/samber/lo"
)

var customTableExprPattern = regexp.MustCompile(`(^|[^$])\{(.*?)}`)
var customFieldExprPattern = regexp.MustCompile(`\[(.*?)]`)
var variablePattern = regexp.MustCompile(`\$\{(.*?)}`)

// RegReplaceVariableExpr 替换表达式变量
func RegReplaceVariableExpr(propRaw string, varMap map[string]string) (newPropRaw string) {
	matches := RegGetVariableExpr(propRaw)
	newPropRaw = propRaw
	for _, matchItem := range matches {
		varValue, ok := varMap[matchItem]
		if !ok {
			continue
		}
		newPropRaw = strings.ReplaceAll(newPropRaw, fmt.Sprintf("${%s}", matchItem), varValue)
	}
	return
}

// RegGetVariableExpr 解析表达式获取变量
func RegGetVariableExpr(propRaw string) []string {
	var matchTableRes []string
	result := variablePattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchTableRes = append(matchTableRes, matchItem[1])
		}
	}
	return matchTableRes
}

// RegGetTableExpr 解析表达式获取引用表
func RegGetTableExpr(propRaw string) []string {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var matchTableRes []string
	result := customTableExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchTableRes = append(matchTableRes, matchItem[2])
		}
	}
	return matchTableRes
}

// RegGetFieldExpr 解析表达式获取引用字段
func RegGetFieldExpr(propRaw string) []string {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var matchFieldRes []string
	result := customFieldExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchFieldRes = append(matchFieldRes, matchItem[1])
		}
	}
	return matchFieldRes
}

// RegMatchAndReplaceTableAndField 解析表达式并替换为真实字段名
func RegMatchAndReplaceTableAndField(propRaw string, resourceType entities.ProjectResourceType) (newPropRaw string) {

	//todo 根据引擎判断替换双引号还反引号

	matchRes := RegGetTableExpr(propRaw)
	// 去除花括号，获取到真实表名
	newPropRaw = propRaw
	//todo 这里表名字段名的反引号和双引号需要根据查询引擎来
	for _, matchItem := range matchRes {
		patternItem := fmt.Sprintf(`\{(%s)\}`, matchItem)
		reItem := regexp.MustCompile(patternItem)
		if resourceType == entities.DamengSaaSResourceType || resourceType == entities.DamengResourceType {
			newPropRaw = reItem.ReplaceAllString(newPropRaw, fmt.Sprintf("\"%s\"", matchItem))
		} else {
			newPropRaw = reItem.ReplaceAllString(newPropRaw, fmt.Sprintf("`%s`", matchItem))
		}

	}
	matchRes = RegGetFieldExpr(propRaw)
	// 去除中括号，获取到真实字段名
	for _, matchItem := range matchRes {
		patternItem := fmt.Sprintf(`\[(%s)\]`, matchItem)
		reItem := regexp.MustCompile(patternItem)

		if resourceType == entities.DamengSaaSResourceType || resourceType == entities.DamengResourceType {
			newPropRaw = reItem.ReplaceAllString(newPropRaw, fmt.Sprintf("\"%s\"", matchItem))
		} else {
			newPropRaw = reItem.ReplaceAllString(newPropRaw, fmt.Sprintf("`%s`", matchItem))
		}
	}
	return
}

// 根据表名、字段名集合将表达式 转换成原始表达式
func RegReplaceTableAndFieldToRawExpress(prop string, propRaw string, dialect moql.MoqlDialect, varsValueMap map[string]string) (propRawExpress string) {
	tables := RegGetTableExpr(propRaw)
	fields := RegGetFieldExpr(propRaw)

	propRawExpress = prop
	tables = lo.Uniq(tables)
	for _, table := range tables {
		patternItem := ""

		//todo 后面多引擎要再抽离
		if dialect == moql.DmDialect {
			patternItem = fmt.Sprintf("\"%s\"", table)
		} else {
			patternItem = table
		}
		reItem := regexp.MustCompile(patternItem)
		propRawExpress = reItem.ReplaceAllString(propRawExpress, fmt.Sprintf("{%s}", table))

	}
	fields = lo.Uniq(fields)
	for _, field := range fields {
		patternItem := field
		//todo 后面多引擎要再抽离
		if dialect == moql.DmDialect {
			patternItem = fmt.Sprintf("\"%s\"", field)
		} else {
			patternItem = field
		}
		reItem := regexp.MustCompile(patternItem)
		propRawExpress = reItem.ReplaceAllString(propRawExpress, fmt.Sprintf("[%s]", field))
	}

	//将变量的默认值替换回表达式
	for k, v := range varsValueMap {
		//将默认替换成默认成表达式
		propRawExpress = strings.ReplaceAll(propRawExpress, strings.ToUpper(v), fmt.Sprintf("${%s}", k))
	}
	return
}

// RegGetVarValueExpr 解析表达式获取变量默认值
func RegGetVarValueExpr(propRaw string, varDefaultValue string) []string {
	var valuePattern = regexp.MustCompile(varDefaultValue)
	var matchTableRes []string
	result := valuePattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchTableRes = append(matchTableRes, matchItem[1])
		}
	}
	return matchTableRes
}
