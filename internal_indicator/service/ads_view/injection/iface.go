package injection

import (
	"context"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

// ================================公共接口==========================================================

// DependenceModel 依赖模型
type DependenceModel interface {
	GetModelRelation(ctx context.Context) (*common.ModelRelation, error)
	GetAnalyzableDimensionFields(ctx context.Context) ([]*proto.ModelField, error)
}

// QueryLimit 查询限制
type QueryLimit interface {
	GetLimit(ctx context.Context) (*proto.Limit, error)
}

type Variables interface {
	GetVariableContext(ctx context.Context) (*VariableContext, error)
}

// ================================指标模式类接口==========================================================

// IndicatorDefinition 指标定义
type IndicatorDefinition interface {
	GetDefinitions(ctx context.Context) ([]*proto.Definition, error)
}

// IndicatorAggregate 指标聚合
type IndicatorAggregate interface {
	GetAggregate(ctx context.Context) (*proto.Aggregate, error)
}

// GlobalFilter 全局过滤器: key是表名, value是过滤条件
type GlobalFilter interface {
	GetGlobalFilter(ctx context.Context) (map[string][]*proto.Filter, error)
}

// IndicatorFilter 指标过滤
type IndicatorFilter interface {
	GetFilter(ctx context.Context) ([]*proto.Filter, error)
}

// IndicatorSort 指标排序
type IndicatorSort interface {
	GetSort(ctx context.Context) ([]*proto.Sort, error)
}

// DimensionsToBeAnalyzed 指标待分析维度
type DimensionsToBeAnalyzed interface {
	GetDimensionsToBeAnalyzed(ctx context.Context) ([]*proto.Field, error)
}

// ================================明细模式类接口==========================================================

// DetailDefinition 明细定义
type DetailDefinition interface {
	GetAdditionalField(ctx context.Context) ([]*proto.Field, error)
}

// DetailFilter 明细过滤
type DetailFilter interface {
	GetDetailFilter(ctx context.Context) ([]*proto.Filter, error)
}

// DetailSort 明细排序
type DetailSort interface {
	GetDetailSort(ctx context.Context) ([]*proto.Sort, error)
}

type DetailModelRelation interface {
	GetDetailModelRelation(ctx context.Context) ([]*proto.DetailModelRelationInfo, error)
}
