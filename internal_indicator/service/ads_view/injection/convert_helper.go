package injection

import (
	"fmt"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	cBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

type ConvertHelperI interface {
	// IndicatorToDefinition ViewContent中的指标Prop结构转换为sql_build中的Definition结构
	IndicatorToDefinition(alias string, indicator *rpc_call.Prop, resourceType entities.ProjectResourceType) (definition *proto.Definition, err error)
	// ConditionToFilter ViewContent中的Condition结构转换为sql_build中的Filter结构
	ConditionToFilter(depTableName string, condition *proto.Condition) (*proto.Filter, error)
	NewConditionToFilter(depTableName string, conditions []global.Condition, combo string) ([]proto.Filter, error)
}

var _ ConvertHelperI = (*ConvertHelper)(nil)

type DynamicTimeType string

const (
	Now          DynamicTimeType = "1"
	Today        DynamicTimeType = "2"
	CurrentMonth DynamicTimeType = "3"
	CurrentYear  DynamicTimeType = "4"
)

type TimeValueType int32

const (
	DynamicTime  TimeValueType = 1
	ConstantTime TimeValueType = 2
	TableField   TimeValueType = 3
	TimeVariable TimeValueType = 4
)

type ConvertHelper struct {
	useCast      bool // 指标是否增加cast进行类型转换
	withBrackets bool // 指标字段和表名是否带括号
	varReplacer  VariableReplacerI
	resourceType cBase.ProjectResourceType
}

func NewConvertHelper(withOptions ...ConvertOptionFunc) *ConvertHelper {
	helper := &ConvertHelper{
		useCast:      false,
		withBrackets: false,
		varReplacer:  nil,
	}
	helper.setOptions(withOptions...)
	return helper
}

type ConvertOptionFunc func(s *ConvertHelper)

// WithBrackets 生成的指标字段和表名是否带括号(生成指标sql片段后用于识别表名和字段), 默认不带括号
func WithBrackets() ConvertOptionFunc {
	return func(helper *ConvertHelper) {
		helper.withBrackets = true
	}
}

// WithUseCast 指标在生成sql时是否添加cast类型转换函数, 默认不进行类型转换
func WithUseCast() ConvertOptionFunc {
	return func(helper *ConvertHelper) {
		helper.useCast = true
	}
}

type VariableContext struct {
	EmptyVarSelectAll bool
	VarDefs           []rpc_call.Variable
	VarVals           []*indicator_query.VariableValue
}

// WithVarValues 指定变量定义
func WithVarValues(varCtx *VariableContext) ConvertOptionFunc {
	return func(helper *ConvertHelper) {
		if varCtx != nil {
			helper.varReplacer = NewVariableReplacer(varCtx.EmptyVarSelectAll, varCtx.VarDefs, varCtx.VarVals)
		}
	}
}

// WithProjectResourceType
func WithProjectResourceType(resourceType cBase.ProjectResourceType) ConvertOptionFunc {
	return func(helper *ConvertHelper) {
		helper.resourceType = resourceType
	}
}

func (s *ConvertHelper) setOptions(withOptions ...ConvertOptionFunc) {
	// options
	for _, withOption := range withOptions {
		withOption(s)
	}
}

// IndicatorToDefinition 数芯Indicator转换为应用表指标Definition
func (s *ConvertHelper) IndicatorToDefinition(alias string, indicator *rpc_call.Prop, resourceType entities.ProjectResourceType) (definition *proto.Definition, err error) {
	var filterList []proto.Filter
	if filterList, err = s.NewConditionToFilter("", indicator.Conditions, indicator.Combo); err != nil {
		return
	}
	filters := lo.Map(filterList, func(item proto.Filter, _ int) *proto.Filter {
		return &item
	})
	var name = indicator.Alias
	if name == "" {
		name = indicator.NameCn
	}
	definition = &proto.Definition{
		OriginPropMeta:  indicator,
		Name:            name,                                                          // 指标英文名
		NameCn:          indicator.NameCn,                                              // 指标中文名
		Alias:           alias,                                                         // 别称
		ReferenceFields: s.propFieldsToDefinitionReferenceFields(indicator.PropFields), // 引用的字段
		CalcRatio:       indicator.CalcRatio,                                           // 是否求占比
	}
	if indicator.Mode == rpc_call.SimpleProp {
		// 简单模式
		definition.FormulaType = proto.DefaultFormulaType
		definition.Function = s.specToFunc(rpc_call.AdsPropSpecifier(indicator.Specifier))
		definition.Table = s.genTableName(indicator.ObjName)
		definition.Field = s.genFieldName(indicator.PropName)
		definition.Filters = filters
	} else {
		// 高级模式
		definition.Table = proto.DefaultAdsViewPlaceholder
		definition.Field = name
		definition.FormulaType = proto.CustomFormulaType
		definition.SqlExpress = RegMatchAndReplaceTableAndField(indicator.PropRaw, resourceType)
		if s.varReplacer != nil {
			// 替换变量
			definition.SqlExpress, err = s.varReplacer.ReplaceExprVariables(definition.SqlExpress)
			if err != nil {
				return
			}
		}
	}

	if s.useCast {
		definition.CastType = proto.CastType{
			Type:      indicator.ConvertFieldType,
			Length:    indicator.ConvertFieldTypeParams.Length,
			Precision: indicator.ConvertFieldTypeParams.Scale,
		}
	}
	return
}

func (s *ConvertHelper) propModeToFormulaType(mode rpc_call.AdsPropMode) proto.FormulaType {
	switch mode {
	case rpc_call.SimpleProp:
		return proto.DefaultFormulaType
	case rpc_call.AdvanceProp:
		return proto.CustomFormulaType
	default:
		return ""
	}
}

func (s *ConvertHelper) propFieldsToDefinitionReferenceFields(propFields []rpc_call.PropField) []*proto.Field {
	var referenceFields []*proto.Field
	for _, propField := range propFields {
		referenceFields = append(referenceFields, &proto.Field{
			ModelField: proto.ModelField{
				Table: propField.TableName,
				Field: propField.FieldName,
			},
		})
	}
	return referenceFields
}

func (s *ConvertHelper) specToFunc(specifier rpc_call.AdsPropSpecifier) proto.CalcFunction {
	switch specifier {
	case rpc_call.Sum:
		return proto.SumFunc
	case rpc_call.Count:
		return proto.Count
	case rpc_call.Avg:
		return proto.Avg
	case rpc_call.Min:
		return proto.Min
	case rpc_call.Max:
		return proto.Max
	case rpc_call.CountDistinct:
		return proto.CountDistinct
	}
	return ""
}

// ConditionToFilter 数芯Condition转换为应用表Filter
//
//	depTableName: 单表表名, 某些场景下单表条件中的字段可能不包含表名, 兼容这种情况
func (s *ConvertHelper) ConditionToFilter(depTableName string, condition *proto.Condition) (*proto.Filter, error) {
	newFilter := new(proto.Filter)
	newFilter.Associate = s.viewContentConditionLogicalRelationToSqlBuildAssociate(condition.LogicalRelation)
	if len(condition.Conditions) > 0 {
		for idx := range condition.Conditions {
			filter, err := s.ConditionToFilter(depTableName, &condition.Conditions[idx])
			if err != nil {
				return nil, err
			}
			newFilter.SubFilter = append(newFilter.SubFilter, filter)
		}
		return newFilter, nil
	}
	var err error
	switch condition.Operator {
	case rpc_call.NoConditionOperator, rpc_call.EQ, rpc_call.GT, rpc_call.LT, rpc_call.GTE, rpc_call.LTE, rpc_call.NEQ:
		if variable, ok := s.getVariable(condition.Right.Value); ok && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(variable) {
			return s.new1eq1Filter(newFilter.Associate), nil
		}
		newFilter.Field, err = s.leftToSqlBuildFilterField(depTableName, condition.Left)
		if err != nil {
			return nil, err
		}
		newFilter.Operate = s.calOperatorToSqlBuildOperate(condition.Operator)
		newFilter.Value, err = s.rightToSqlBuildFilterSingleValue(condition.Left, condition.Right)
		if err != nil {
			return nil, err
		}
		// 判断字段是否是时间字段,是的话字段和值要特殊处理一下
		if s.isTimeField(condition.Left.FieldType) {
			if condition.Right.TimeValueType == int32(TimeVariable) && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(condition.Right.Value.(string)) {
				return s.new1eq1Filter(newFilter.Associate), nil
			}
			newFilter, err = s.timeFieldFilterFormat(condition.Right, newFilter)
			if err != nil {
				return nil, err
			}
		}
		return newFilter, nil
	case rpc_call.IN, rpc_call.NOT_IN:
		if variable, ok := s.getVariable(condition.Right.Value); ok && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(variable) {
			return s.new1eq1Filter(newFilter.Associate), nil
		}
		newFilter.Field, err = s.leftToSqlBuildFilterField(depTableName, condition.Left)
		if err != nil {
			return nil, err
		}
		newFilter.Operate = s.calOperatorToSqlBuildOperate(condition.Operator)
		newFilter.Value, err = s.rightToSqlBuildFilterInValue(condition.Right)
		if err != nil {
			return nil, err
		}
		return newFilter, nil
	case rpc_call.LIKE:
		if variable, ok := s.getVariable(condition.Right.Value); ok && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(variable) {
			return s.new1eq1Filter(newFilter.Associate), nil
		}
		newFilter.Field, err = s.leftToSqlBuildFilterField(depTableName, condition.Left)
		if err != nil {
			return nil, err
		}
		newFilter.Operate = s.calOperatorToSqlBuildOperate(condition.Operator)
		newFilter.Value, err = s.rightToSqlBuildFilterLikeValue(condition.Right)
		if err != nil {
			return nil, err
		}
		return newFilter, nil
	case rpc_call.IS_NULL:
		newFilter.Field, err = s.leftToSqlBuildFilterField(depTableName, condition.Left)
		if err != nil {
			return nil, err
		}
		newFilter.Operate = proto.IsEmpty
		return newFilter, nil
	case rpc_call.IS_NOT_NULL:
		newFilter.Field, err = s.leftToSqlBuildFilterField(depTableName, condition.Left)
		if err != nil {
			return nil, err
		}
		newFilter.Operate = proto.IsNotEmpty
		return newFilter, nil
	case rpc_call.NOT_LIKE, rpc_call.NOT_BETWEEN:
		return nil, errors.Errorf("暂不支持的操作符<%s>", condition.Operator)
	case rpc_call.BETWEEN:
		var field *proto.Field
		field, err = s.leftToSqlBuildFilterField(depTableName, condition.Left)
		if err != nil {
			return nil, err
		}
		var val1, val2 string
		val1, val2, err = s.parseRightBetweenValue(condition.Right)
		if err != nil {
			return nil, err
		}
		// 判断是否是变量
		leftVar, leftIsVar := s.getVariable(val1)
		rightVar, rightIsVar := s.getVariable(val2)
		if leftIsVar && rightIsVar && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(leftVar) &&
			s.varReplacer.ShouldFilterIgnoreWithVar(rightVar) {
			return s.new1eq1Filter(newFilter.Associate), nil
		}
		subFilter1 := new(proto.Filter)
		subFilter1.Associate = proto.NoAssociate
		subFilter1.Field = field
		subFilter1.Operate = proto.Gte
		if s.varReplacer != nil && leftIsVar {
			// 变量处理
			leftVarVal, err := s.varReplacer.GetVariableValue(leftVar)
			if err != nil {
				return nil, err
			}
			subFilter1.Value = proto.ValueContent{
				Type:  proto.ValueType_ExpressValue,
				Value: leftVarVal,
			}
		} else {
			subFilter1.Value = proto.ValueContent{
				Type:  proto.ValueType_StringValue,
				Value: val1,
			}
		}
		subFilter2 := new(proto.Filter)
		subFilter2.Field = field
		subFilter2.Associate = proto.AND
		subFilter2.Operate = proto.Lte
		if s.varReplacer != nil && rightIsVar {
			// 变量处理
			rightVarVal, err := s.varReplacer.GetVariableValue(leftVar)
			if err != nil {
				return nil, err
			}
			subFilter1.Value = proto.ValueContent{
				Type:  proto.ValueType_ExpressValue,
				Value: rightVarVal,
			}
		} else {
			subFilter2.Value = proto.ValueContent{
				Type:  proto.ValueType_StringValue,
				Value: val2,
			}
		}
		newFilter.SubFilter = []*proto.Filter{subFilter1, subFilter2}
		return newFilter, nil
	// 日期条件 - 动态参数
	case rpc_call.FROM_TODAY, rpc_call.FROM_YESTERDAY, rpc_call.FROM_WEEK, rpc_call.FROM_MONTH,
		rpc_call.FROM_YEAR, rpc_call.FROM_QUARTER:
		return s.genRelativeStaticDatetimeParamsCondition(newFilter, depTableName, condition)
	// 日期条件 - 区间变量
	case rpc_call.FROM_RANGE_VARIABLE:
		return s.genDynamicDateTimeRangeCondition(newFilter, depTableName, condition)
	default:
		return nil, errors.Errorf("不支持的操作符<%s>", condition.Operator)
	}
}

func (s *ConvertHelper) getVariable(val interface{}) (string, bool) {
	v, ok := val.(string)
	if !ok {
		return "", false
	}
	variables := RegGetVariableExpr(v)
	if len(variables) != 1 {
		return "", false
	}
	return variables[0], true
}

func (s *ConvertHelper) new1eq1Filter(ass proto.AssociateType) *proto.Filter {
	return &proto.Filter{
		LeftExpType: proto.ValueExp,
		LeftValue:   &proto.ValueContent{Type: proto.ValueType_IntegerValue, Value: "1"},
		Operate:     proto.Equal,
		Value:       proto.ValueContent{Type: proto.ValueType_IntegerValue, Value: "1"},
		Associate:   ass,
	}
}

func (s *ConvertHelper) genDynamicDateTimeRangeCondition(filter *proto.Filter, depTableName string,
	condition *proto.Condition) (newFilter *proto.Filter, err error) {
	newFilter = filter
	// 设置过滤左字段
	field, err := s.leftToSqlBuildFilterField(depTableName, condition.Left)
	if err != nil {
		return
	}
	// 解析右边的变量值
	valueRaw := condition.Right.Value
	if valueRaw == nil {
		err = errors.New("不支持的时间区间变量值<nil>")
		return
	}
	// 判断右值是不是一个变量
	value, ok := valueRaw.(string)
	if !ok {
		err = errors.New("右值变量名称不是字符串类型")
		return
	}
	// 先判断该变量值是否该被忽略
	if s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(value) {
		newFilter = s.new1eq1Filter(newFilter.Associate)
		return
	}
	if s.varReplacer == nil {
		return
	}
	object := value
	if s.varReplacer != nil {
		object, err = s.varReplacer.GetVariableValue(value)
		if err != nil {
			return
		}
	}
	rangeValues := strings.Split(object, "and")
	if len(rangeValues) != 2 {
		err = errors.New("区间变量的默认值或传参值不是一个区间类型")
		return
	}
	dateBetweenSubFilter := new(proto.Filter)
	dateBetweenSubFilter.Associate = proto.NoAssociate
	dateBetweenSubFilter.Field = field
	dateBetweenSubFilter.Operate = proto.Between
	dateBetweenSubFilter.Value = proto.ValueContent{
		Type: proto.ValueType_ExpressArray,
		ValueArray: []string{
			strings.TrimSpace(rangeValues[0]),
			strings.TrimSpace(rangeValues[1]),
		},
	}
	newFilter.SubFilter = []*proto.Filter{dateBetweenSubFilter}
	return
}

func (s *ConvertHelper) genRelativeStaticDatetimeParamsCondition(filter *proto.Filter, depTableName string,
	condition *proto.Condition) (newFilter *proto.Filter, err error) {
	newFilter = filter
	field, err := s.leftToSqlBuildFilterField(depTableName, condition.Left)
	if err != nil {
		return nil, err
	}

	field.TimeFormat.TimeFormatType = proto.TimeFormatTypeSecond
	if condition.Operator == rpc_call.FROM_TODAY || condition.Operator == rpc_call.FROM_YESTERDAY {
		field.TimeFormat.TimeFormatType = proto.TimeFormatTypeExtractDay
	}

	startTimeFormat, endTimeFormat, err := s.getRelativeConditionTimeFormat(condition)
	if err != nil {
		return
	}

	subFilter1 := new(proto.Filter)
	subFilter1.Associate = proto.NoAssociate
	subFilter1.Field = field
	subFilter1.Operate = proto.Gte
	subFilter1.Value = proto.ValueContent{
		Type:        proto.ValueType_CustomSymbol,
		SymbolValue: proto.CustomSymbolCategory_CurrentTime,
		TimeFormat:  startTimeFormat,
	}
	subFilter2 := new(proto.Filter)
	subFilter2.Field = field
	subFilter2.Associate = proto.AND
	subFilter2.Operate = proto.Lt
	subFilter2.Value = proto.ValueContent{
		Type:        proto.ValueType_CustomSymbol,
		SymbolValue: proto.CustomSymbolCategory_CurrentTime,
		TimeFormat:  endTimeFormat,
	}
	if err != nil {
		return
	}
	newFilter.SubFilter = []*proto.Filter{subFilter1, subFilter2}
	return
}

func (s *ConvertHelper) getRelativeConditionTimeFormat(condition *proto.Condition) (startTimeFormat, endTimeFormat *proto.TimeFormat, err error) {
	valueRaw := condition.Right.Value
	if valueRaw == nil {
		err = errors.New("不支持的相对时间参数值<nil>")
		return
	}
	value, ok := valueRaw.(float64)
	if !ok {
		err = errors.Errorf("不支持的相对时间参数值<%v>", valueRaw)
		return
	}

	switch condition.Operator {
	case rpc_call.FROM_TODAY:
		startTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeDay,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeDay, Value: -int64(value)},
		}
		endTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeDay,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeDay, Value: 1},
		}
		return
	case rpc_call.FROM_YESTERDAY:
		startTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeDay,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeDay, Value: -int64(value)},
		}
		endTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeDay,
		}
		return
	case rpc_call.FROM_WEEK:
		startTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeWeek,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeDay, Value: 7 * (-int64(value))},
		}
		endTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeWeek,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeDay, Value: 7 * (-int64(value) + 1)},
		}
		return
	case rpc_call.FROM_MONTH:
		startTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeMonth,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeMonth, Value: -int64(value)},
		}
		endTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeMonth,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeMonth, Value: -int64(value) + 1},
		}
		return
	case rpc_call.FROM_YEAR:
		startTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeYear,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeYear, Value: -int64(value)},
		}
		endTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeYear,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeYear, Value: -int64(value) + 1},
		}
		return
	case rpc_call.FROM_QUARTER:
		startTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeQuarter,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeMonth, Value: 3 * (-int64(value))},
		}
		endTimeFormat = &proto.TimeFormat{
			TimeFormatType: proto.TimeFormatTypeQuarter,
			Diff:           proto.TimeDiff{Type: proto.TimeFormatTypeMonth, Value: 3 * (-int64(value) + 1)},
		}
		return
	default:
		err = errors.Errorf("不支持的相对时间参数操作符<%s>", condition.Operator)
		return
	}
}

func (s *ConvertHelper) genRelativeDynamicDatetimeParamsCondition(filter *proto.Filter, depTableName string, condition *proto.Condition) (newFilter *proto.Filter, err error) {
	newFilter = filter
	field, err := s.leftToSqlBuildFilterField(depTableName, condition.Left)
	if err != nil {
		return nil, err
	}
	field.TimeFormat.TimeFormatType = proto.TimeFormatTypeSecond
	if condition.Operator == rpc_call.FROM_TODAY || condition.Operator == rpc_call.FROM_YESTERDAY {
		field.TimeFormat.TimeFormatType = proto.TimeFormatTypeExtractDay
	}
	start, end, err := s.genDatetimeDynamicParam(condition.Operator, condition.Right.Value)
	if err != nil {
		return nil, err
	}
	subFilter1 := new(proto.Filter)
	subFilter1.Associate = proto.NoAssociate
	subFilter1.Field = field
	subFilter1.Operate = proto.Gte
	subFilter1.Value = proto.ValueContent{
		Type:  proto.ValueType_StringValue,
		Value: start,
	}
	if err != nil {
		return nil, err
	}
	subFilter2 := new(proto.Filter)
	subFilter2.Field = field
	subFilter2.Associate = proto.AND
	subFilter2.Operate = proto.Lt
	subFilter2.Value = proto.ValueContent{
		Type:  proto.ValueType_StringValue,
		Value: end,
	}
	if err != nil {
		return nil, err
	}
	newFilter.SubFilter = []*proto.Filter{subFilter1, subFilter2}
	return newFilter, nil
}

func (s *ConvertHelper) genDatetimeDynamicParam(op rpc_call.ConditionOperator, valueRaw interface{}) (string, string, error) {
	if valueRaw == nil {
		return "", "", errors.New("不支持的相对时间参数值<nil>")
	}
	value, ok := valueRaw.(float64)
	if !ok {
		return "", "", errors.Errorf("不支持的相对时间参数值<%v>", valueRaw)
	}

	switch op {
	case rpc_call.FROM_TODAY:
		return fmt.Sprintf("$[FAR_%1.f_start]", value), "$[FAR_0_end]", nil
	case rpc_call.FROM_YESTERDAY:
		return fmt.Sprintf("$[FAR_YESTERDAY_%1.f_start]", value), "$[FAR_YESTERDAY_1_end]", nil
	case rpc_call.FROM_WEEK:
		return fmt.Sprintf("$[FROM_WEEK_%1.f_start]", value), fmt.Sprintf("$[FROM_WEEK_%1.f_end]", value), nil
	case rpc_call.FROM_MONTH:
		return fmt.Sprintf("$[FROM_MONTH_%1.f_start]", value), fmt.Sprintf("$[FROM_MONTH_%1.f_end]", value), nil
	case rpc_call.FROM_QUARTER:
		return fmt.Sprintf("$[FROM_QUARTER_%1.f_start]", value), fmt.Sprintf("$[FROM_QUARTER_%1.f_end]", value), nil
	case rpc_call.FROM_YEAR:
		return fmt.Sprintf("$[FROM_YEAR_%1.f_start]", value), fmt.Sprintf("$[FROM_YEAR_%1.f_end]", value), nil
	default:
		return "", "", errors.Errorf("不支持的动态时间操作符<%s>", op)
	}
}

func (s *ConvertHelper) genTableName(rawTableName string) string {
	if rawTableName == "" {
		return ""
	}
	if !s.withBrackets {
		return rawTableName
	}
	return fmt.Sprintf("{%s}", rawTableName)
}

func (s *ConvertHelper) genFieldName(rawFieldName string) string {
	if rawFieldName == "" {
		return ""
	}
	if !s.withBrackets {
		return rawFieldName
	}
	return fmt.Sprintf("[%s]", rawFieldName)
}

func (s *ConvertHelper) leftToSqlBuildFilterField(depTableName string, left *proto.Dimension) (*proto.Field, error) {
	if left.PropName == "" {
		return nil, errors.New("where条件字段左值为空")
	}
	// 如果字段中有tableName，使用字段中的tableName，否则使用depTableName，否则使用空
	tableName := depTableName
	if left.ObjName != "" {
		// 来源单表模型中旧数据里ObjName是空, 使用DepTableName
		tableName = left.ObjName
	}

	return &proto.Field{
		ModelField: proto.ModelField{
			Table: s.genTableName(tableName),
			Field: s.genFieldName(left.PropName),
		},
		TimeFormat: proto.TimeFormat{},
		Constraint: "",
	}, nil
}

func (s *ConvertHelper) calOperatorToSqlBuildOperate(op rpc_call.ConditionOperator) proto.OperateType {
	switch op {
	case rpc_call.EQ:
		return proto.Equal
	case rpc_call.GT:
		return proto.Gt
	case rpc_call.LT:
		return proto.Lt
	case rpc_call.GTE:
		return proto.Gte
	case rpc_call.LTE:
		return proto.Lte
	case rpc_call.NEQ:
		return proto.NotEqual
	case rpc_call.IN:
		return proto.Contain
	case rpc_call.NOT_IN:
		return proto.NotContain
	case rpc_call.LIKE:
		return proto.Like
	default:
		return proto.Empty
	}
}

func (s *ConvertHelper) parseRightBetweenValue(right *proto.Dimension) (val1 string, val2 string, err error) {
	valueSlice, ok := right.Value.([]interface{})
	if !ok {
		return "", "", errors.Errorf("where条件值类型错误, between操作符右值应该为列表类型")
	}
	if len(valueSlice) != 2 {
		return "", "", errors.Errorf("where条件值类型错误, between操作符右值长度应该为2")
	}

	val1, ok = valueSlice[0].(string)
	if !ok {
		return "", "", errors.Errorf("where条件值类型错误, between操作符右值子类型应该为string")
	}
	val2, ok = valueSlice[1].(string)
	if !ok {
		return "", "", errors.Errorf("where条件值类型错误, between操作符右值子类型应该为string")
	}
	return
}

func (s *ConvertHelper) rightToSqlBuildFilterInValue(right *proto.Dimension) (proto.ValueContent, error) {
	// 如果是变量，对变量进行替换
	if varName, ok := s.getVariable(right.Value); ok && s.varReplacer != nil {
		varExpr, err := s.varReplacer.GetVariableValue(varName)
		if err != nil {
			return proto.ValueContent{}, err
		}
		return proto.ValueContent{
			Type: proto.ValueType_ExpressValue,
			// FIXME: 特殊处理, 底层会增加括号, 这里把括号去除掉
			Value: strings.TrimRight(strings.TrimLeft(varExpr, "("), ")"),
		}, nil
	}

	// in条件value值是string类型
	value, ok := right.Value.(string)
	if !ok {
		return proto.ValueContent{}, errors.Errorf("where条件值类型错误, 逻辑操作符右值应该为string类型")
	}
	slices := strings.Split(value, ",")
	newSlices := []string{}
	for _, slice := range slices {
		newSlices = append(newSlices, fmt.Sprintf("'%s'", slice))
	}
	return proto.ValueContent{
		Type:       proto.ValueType_ExpressArray,
		Value:      "",
		ValueArray: newSlices,
	}, nil
}

func (s *ConvertHelper) rightToSqlBuildFilterLikeValue(right *proto.Dimension) (proto.ValueContent, error) {
	// 如果是变量，对变量进行替换
	if varName, ok := s.getVariable(right.Value); ok && s.varReplacer != nil {
		varExpr, err := s.varReplacer.GetVariableValue(varName)
		if err != nil {
			return proto.ValueContent{}, err
		}
		return proto.ValueContent{
			Type:  proto.ValueType_ExpressValue,
			Value: varExpr,
		}, nil
	}

	// value值必须是string类型
	value, ok := right.Value.(string)
	if !ok {
		return proto.ValueContent{}, errors.Errorf("where条件值类型错误, 逻辑操作符右值应该为string类型")
	}
	return proto.ValueContent{
		Type:       proto.ValueType_StringValue,
		Value:      fmt.Sprintf("%%%s%%", value),
		ValueArray: nil,
	}, nil
}

func (s *ConvertHelper) rightToSqlBuildFilterSingleValue(left *proto.Dimension, right *proto.Dimension) (proto.ValueContent, error) {
	// 如果是变量，对变量进行替换
	if varName, ok := s.getVariable(right.Value); ok && s.varReplacer != nil {
		varExpr, err := s.varReplacer.GetVariableValue(varName)
		if err != nil {
			return proto.ValueContent{}, err
		}
		return proto.ValueContent{
			Type:  proto.ValueType_ExpressValue,
			Value: varExpr,
		}, nil
	}

	switch value := right.Value.(type) {
	case string:
		valueType := proto.ValueType_StringValue
		switch left.SystemFieldType {
		case rpc_call.Decimal, rpc_call.Double:
			valueType = proto.ValueType_FloatValue
		case rpc_call.Bigint:
			valueType = proto.ValueType_IntegerValue
		default:
		}
		return proto.ValueContent{
			Type:  valueType,
			Value: value,
		}, nil
	case bool:
		v := "false"
		if value {
			v = "true"
		}
		return proto.ValueContent{
			Type:  proto.ValueType_ExpressValue,
			Value: v,
		}, nil
	default:
		return proto.ValueContent{}, errors.Errorf("where条件值类型错误, 逻辑操作符右值应该为string或bool类型")
	}
}

func (s *ConvertHelper) viewContentConditionLogicalRelationToSqlBuildAssociate(relation rpc_call.ConditionLogicalRelation) proto.AssociateType {
	switch relation {
	case rpc_call.NoConditionLogicalRelation:
		return proto.NoAssociate
	case rpc_call.AND:
		return proto.AND
	case rpc_call.OR:
		return proto.OR
	default:
		return proto.NoAssociate
	}
}

func (s *ConvertHelper) isTimeField(fieldType string) bool {
	if fieldType == "date" || fieldType == "datetime" || fieldType == "timestamp" {
		return true
	}
	return false
}

func (s *ConvertHelper) timeFieldFilterFormat(right *proto.Dimension, filter *proto.Filter) (*proto.Filter, error) {
	//时间值类型不在范围内的,直接返回
	if right.TimeValueType < int32(DynamicTime) || right.TimeValueType > int32(TimeVariable) {
		return filter, nil
	}

	switch right.TimeValueType {
	case int32(DynamicTime): //动态时间
		filter.Value.Type = proto.ValueType_CustomSymbol
		filter.Value.SymbolValue = proto.CustomSymbolCategory_CurrentTime

		value := DynamicTimeType(fmt.Sprint(right.Value))
		if value == Now {
			filter.Field.TimeFormat.TimeFormatType = proto.TimeFormatTypeSecond
			filter.Value.TimeFormat = &proto.TimeFormat{
				TimeFormatType: proto.TimeFormatTypeExtractSecond,
			}
		} else if value == Today {
			filter.Field.TimeFormat.TimeFormatType = proto.TimeFormatTypeExtractDay
			filter.Value.TimeFormat = &proto.TimeFormat{
				TimeFormatType: proto.TimeFormatTypeExtractDay,
			}
		} else if value == CurrentMonth {
			filter.Field.TimeFormat.TimeFormatType = proto.TimeFormatTypeExtractMonth
			filter.Value.TimeFormat = &proto.TimeFormat{
				TimeFormatType: proto.TimeFormatTypeExtractMonth,
			}
		} else if value == CurrentYear {
			filter.Field.TimeFormat.TimeFormatType = proto.TimeFormatTypeExtractYear
			filter.Value.TimeFormat = &proto.TimeFormat{
				TimeFormatType: proto.TimeFormatTypeExtractYear,
			}
		} else {
			return nil, fmt.Errorf("时间字段动态类型时间参数错误")
		}

	case int32(ConstantTime): //固定时间
		filter.Field.TimeFormat.TimeFormatType = proto.TimeFormatTypeExtractDay
	case int32(TableField): //表内字段的处理和左值的处理一样
		filter.Value.Type = proto.ValueType_ExpressValue
		filter.Value.Value = fmt.Sprintf("%s.%s", right.ObjName, right.PropName)
	case int32(TimeVariable): //时间变量
		filter.Value.Type = proto.ValueType_ExpressValue
		v, err := s.varReplacer.GetVariableValue(right.Value.(string))
		if err != nil {
			return nil, err
		}
		filter.Value.Value = v
	}
	return filter, nil
}
