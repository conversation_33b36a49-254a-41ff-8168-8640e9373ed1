package injection

import (
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/vitess/go/vt/sqlparser"
	"strconv"
	"strings"
)

func (s *ConvertHelper) NewConditionToFilter(depTableName string, conditions []global.Condition, combo string) ([]proto.Filter, error) {
	var ans = make([]proto.Filter, 0)
	if len(conditions) < 1 {
		return ans, nil
	}
	var indexLength = 1000
	var conditionMap = make([]proto.Filter, indexLength)
	for _, condition := range conditions {
		index, filter, err := s.buildNewCondition(depTableName, condition)
		if err != nil {
			return ans, err
		}
		if index < 1 || index >= indexLength {
			return ans, errors.Errorf("条件索引<%d>超出范围: 1 <= index <= %d", index, indexLength)
		}
		conditionMap[index] = *filter
	}
	return buildFilterByRelatedExpress(conditionMap, combo)
}

func (s *ConvertHelper) buildNewCondition(depTableName string, cond global.Condition) (index int, filter *proto.Filter, err error) {
	index = cond.Index
	filter = &proto.Filter{}
	operator := rpc_call.ConditionOperator(cond.Operator)
	var leftFieldDimension = convertField2Dimension(cond.LeftField)
	var rightValueDimension = convertValue2Dimension(cond.RightValue)
	switch operator {
	case rpc_call.NoConditionOperator, rpc_call.EQ, rpc_call.GT, rpc_call.LT, rpc_call.GTE, rpc_call.LTE, rpc_call.NEQ:
		if variable, ok := s.getVariable(rightValueDimension.Value); ok && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(variable) {
			filter = s.new1eq1Filter(proto.NoAssociate)
			return
		}
		filter.Field, err = s.leftToSqlBuildFilterField(depTableName, leftFieldDimension)
		if err != nil {
			return
		}
		filter.Operate = s.calOperatorToSqlBuildOperate(operator)
		filter.Value, err = s.rightToSqlBuildFilterSingleValue(leftFieldDimension, rightValueDimension)
		if err != nil {
			return
		}
		// 判断字段是否是时间字段,是的话字段和值要特殊处理一下
		if s.isTimeField(leftFieldDimension.FieldType) {
			if rightValueDimension.TimeValueType == int32(TimeVariable) && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(rightValueDimension.Value.(string)) {
				filter = s.new1eq1Filter(proto.NoAssociate)
				return
			}
			filter, err = s.timeFieldFilterFormat(rightValueDimension, filter)
			if err != nil {
				return
			}
		}
		return
	case rpc_call.IN, rpc_call.NOT_IN:
		if variable, ok := s.getVariable(rightValueDimension.Value); ok && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(variable) {
			filter = s.new1eq1Filter(proto.NoAssociate)
			return
		}
		filter.Field, err = s.leftToSqlBuildFilterField(depTableName, leftFieldDimension)
		if err != nil {
			return
		}
		filter.Operate = s.calOperatorToSqlBuildOperate(operator)
		filter.Value, err = s.rightToSqlBuildFilterInValue(rightValueDimension)
		if err != nil {
			return
		}
		return
	case rpc_call.LIKE:
		if variable, ok := s.getVariable(rightValueDimension.Value); ok && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(variable) {
			filter = s.new1eq1Filter(proto.NoAssociate)
			return
		}
		filter.Field, err = s.leftToSqlBuildFilterField(depTableName, leftFieldDimension)
		if err != nil {
			return
		}
		filter.Operate = s.calOperatorToSqlBuildOperate(operator)
		filter.Value, err = s.rightToSqlBuildFilterLikeValue(rightValueDimension)
		if err != nil {
			return
		}
		return
	case rpc_call.IS_NULL:
		filter.Field, err = s.leftToSqlBuildFilterField(depTableName, leftFieldDimension)
		if err != nil {
			return
		}
		filter.Operate = proto.IsEmpty
		return
	case rpc_call.IS_NOT_NULL:
		filter.Field, err = s.leftToSqlBuildFilterField(depTableName, leftFieldDimension)
		if err != nil {
			return
		}
		filter.Operate = proto.IsNotEmpty
		return
	case rpc_call.NOT_LIKE, rpc_call.NOT_BETWEEN:
		err = errors.Errorf("暂不支持的操作符<%s>", operator)
		return
	case rpc_call.BETWEEN:
		var field *proto.Field
		field, err = s.leftToSqlBuildFilterField(depTableName, leftFieldDimension)
		if err != nil {
			return
		}
		var val1, val2 string
		val1, val2, err = s.parseRightBetweenValue(rightValueDimension)
		if err != nil {
			return
		}
		// 判断是否是变量
		leftVar, leftIsVar := s.getVariable(val1)
		rightVar, rightIsVar := s.getVariable(val2)
		if leftIsVar && rightIsVar && s.varReplacer != nil && s.varReplacer.ShouldFilterIgnoreWithVar(leftVar) &&
			s.varReplacer.ShouldFilterIgnoreWithVar(rightVar) {
			filter = s.new1eq1Filter(proto.NoAssociate)
			return
		}
		subFilter1 := new(proto.Filter)
		subFilter1.Associate = proto.NoAssociate
		subFilter1.Field = field
		subFilter1.Operate = proto.Gte
		if s.varReplacer != nil && leftIsVar {
			// 变量处理
			var leftVarVal string
			leftVarVal, err = s.varReplacer.GetVariableValue(leftVar)
			if err != nil {
				return
			}
			subFilter1.Value = proto.ValueContent{
				Type:  proto.ValueType_ExpressValue,
				Value: leftVarVal,
			}
		} else {
			subFilter1.Value = proto.ValueContent{
				Type:  proto.ValueType_StringValue,
				Value: val1,
			}
		}
		subFilter2 := new(proto.Filter)
		subFilter2.Field = field
		subFilter2.Associate = proto.AND
		subFilter2.Operate = proto.Lte
		if s.varReplacer != nil && rightIsVar {
			var rightVarVal string
			// 变量处理
			rightVarVal, err = s.varReplacer.GetVariableValue(leftVar)
			if err != nil {
				return
			}
			subFilter1.Value = proto.ValueContent{
				Type:  proto.ValueType_ExpressValue,
				Value: rightVarVal,
			}
		} else {
			subFilter2.Value = proto.ValueContent{
				Type:  proto.ValueType_StringValue,
				Value: val2,
			}
		}
		filter.SubFilter = []*proto.Filter{subFilter1, subFilter2}
		return
	// 日期条件 - 动态参数
	case rpc_call.FROM_TODAY, rpc_call.FROM_YESTERDAY, rpc_call.FROM_WEEK, rpc_call.FROM_MONTH,
		rpc_call.FROM_YEAR, rpc_call.FROM_QUARTER:
		filter, err = s.genRelativeStaticDatetimeParamsCondition(filter, depTableName, convert2OldCondition(&cond))
		return
	// 日期条件 - 区间变量
	case rpc_call.FROM_RANGE_VARIABLE:
		filter, err = s.genDynamicDateTimeRangeCondition(filter, depTableName, convert2OldCondition(&cond))
		return
	default:
		err = errors.Errorf("不支持的操作符<%s>", operator)
		return
	}
}

func buildFilterByRelatedExpress(filterMap []proto.Filter, combo string) (ans []proto.Filter, err error) {
	sql := "select " + combo + " from dual"
	var astTree sqlparser.Statement
	astTree, err = sqlparser.Parse(sql)
	if err != nil {
		return
	}
	if err = sqlparser.Walk(func(node sqlparser.SQLNode) (kontinue bool, err error) {
		kontinue = true
		if cond, ok := node.(*sqlparser.AndExpr); ok {
			ans, err = buildFilterBySingleRelatedExpress(filterMap, cond.Left, cond.Right, proto.AND)
			kontinue = false
		}
		if cond, ok := node.(*sqlparser.OrExpr); ok {
			ans, err = buildFilterBySingleRelatedExpress(filterMap, cond.Left, cond.Right, proto.OR)
			kontinue = false
		}
		if intVal, ok := node.(*sqlparser.Literal); ok && intVal.Type == sqlparser.IntVal {
			var result *proto.Filter
			result, err = buildFilterByIndex(filterMap, intVal.Val)
			if err == nil && result != nil {
				ans = []proto.Filter{
					*result,
				}
			}
			kontinue = false
		}
		return
	}, astTree); err != nil {
		return
	}
	return
}

func buildFilterBySingleRelatedExpress(filterMap []proto.Filter, leftExpr, rightExpr sqlparser.Expr,
	associateType proto.AssociateType) (ans []proto.Filter, err error) {
	leftBuf := sqlparser.NewTrackedBuffer(nil)
	leftExpr.Format(leftBuf)
	var leftExpress = leftBuf.String()
	var leftFilters []proto.Filter
	if existsRelatedSymbol(leftExpress) {
		leftFilters, err = buildFilterByRelatedExpress(filterMap, leftExpress)
	} else {
		var condition *proto.Filter
		condition, err = buildFilterByIndex(filterMap, leftExpress)
		if err == nil && condition != nil {
			leftFilters = []proto.Filter{
				*condition,
			}
		}
	}
	if err != nil {
		return
	}
	rightBuf := sqlparser.NewTrackedBuffer(nil)
	rightExpr.Format(rightBuf)
	var rightExpress = rightBuf.String()
	var rightFilters []proto.Filter
	if existsRelatedSymbol(rightExpress) {
		rightFilters, err = buildFilterByRelatedExpress(filterMap, rightExpress)
	} else {
		var condition *proto.Filter
		condition, err = buildFilterByIndex(filterMap, rightExpress)
		if err == nil && condition != nil {
			rightFilters = []proto.Filter{
				*condition,
			}
		}
	}
	if err != nil {
		return
	}
	ans = make([]proto.Filter, 0)
	// 兼容一下左右过滤条件可能不存在的情况
	if len(leftFilters) > 0 && len(rightFilters) > 0 {
		ans = append(ans, proto.Filter{
			SubFilter: lo.Map(leftFilters, func(item proto.Filter, _ int) *proto.Filter {
				return &item
			}),
		})
		ans = append(ans, proto.Filter{
			SubFilter: lo.Map(rightFilters, func(item proto.Filter, _ int) *proto.Filter {
				return &item
			}),
			Associate: associateType,
		})
	} else if len(leftFilters) > 0 {
		ans = append(ans, proto.Filter{
			SubFilter: lo.Map(leftFilters, func(item proto.Filter, _ int) *proto.Filter {
				return &item
			}),
		})
	} else if len(rightFilters) > 0 {
		ans = append(ans, proto.Filter{
			SubFilter: lo.Map(rightFilters, func(item proto.Filter, _ int) *proto.Filter {
				return &item
			}),
		})
	}
	return
}

func buildFilterByIndex(filterMap []proto.Filter, index string) (ans *proto.Filter, err error) {
	var arrIndex int
	if arrIndex, err = strconv.Atoi(strings.TrimSpace(index)); err != nil {
		err = errors.Errorf("非法的索引符号: %s", index)
		return
	}
	if arrIndex < 1 || arrIndex >= len(filterMap) {
		err = errors.Errorf("Combo中索引不在[1,%d)范围中", len(filterMap))
		return
	}
	result := filterMap[arrIndex]
	if len(result.SubFilter) < 1 && result.Operate == proto.Empty {
		return
	}
	ans = &result
	return
}

func existsRelatedSymbol(combo string) bool {
	return strings.Contains(combo, "and") ||
		strings.Contains(combo, "or") ||
		strings.Contains(combo, "AND") ||
		strings.Contains(combo, "OR")
}

func convertField2Dimension(field global.LeftField) *proto.Dimension {
	return &proto.Dimension{
		ID:               field.Id,
		NameCn:           field.NameCn,
		Alias:            field.Alias,
		ObjName:          field.Table,
		PropName:         field.Name,
		Specifier:        field.Specifier,
		FieldType:        field.FieldType,
		SystemFieldType:  rpc_call.SystemFieldType(field.SystemFieldType),
		ConvertFieldType: field.ConvertFieldType,
		ConvertFieldTypeParams: rpc_call.ConvertFieldTypeParams{
			Length: int32(field.ConvertFieldTypeParams.Length),
			Scale:  int32(field.ConvertFieldTypeParams.Scale),
		},
	}
}

func convertValue2Dimension(value global.RightValue) *proto.Dimension {
	return &proto.Dimension{
		ID:               value.Id,
		NameCn:           value.NameCn,
		Alias:            value.Alias,
		ObjName:          value.Table,
		PropName:         value.Name,
		Specifier:        value.Specifier,
		FieldType:        value.FieldType,
		SystemFieldType:  rpc_call.SystemFieldType(value.SystemFieldType),
		ConvertFieldType: value.ConvertFieldType,
		ConvertFieldTypeParams: rpc_call.ConvertFieldTypeParams{
			Length: int32(value.ConvertFieldTypeParams.Length),
			Scale:  int32(value.ConvertFieldTypeParams.Scale),
		},
		Value:         value.Value,
		TimeValueType: int32(value.TimeValueType),
	}
}

func convert2OldCondition(newCondition *global.Condition) *proto.Condition {
	return &proto.Condition{
		Left:     convertField2Dimension(newCondition.LeftField),
		Right:    convertValue2Dimension(newCondition.RightValue),
		Operator: rpc_call.ConditionOperator(newCondition.Operator),
	}
}
