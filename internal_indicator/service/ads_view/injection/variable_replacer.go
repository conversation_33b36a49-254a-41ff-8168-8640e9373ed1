package injection

import (
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/time_utils"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
)

const dmpIgnoreFlag = "_MYSOFT_BIGDATA_NULL_"

type VariableReplacerI interface {
	// ReplaceExprVariables 替换文本中的变量
	ReplaceExprVariables(sql string) (string, error)
	// GetVariableValue 获取变量的值
	GetVariableValue(name string) (string, error)
	// ShouldFilterIgnoreWithVar 判断变量在过滤条件中时，该过滤条件是否应该被忽略
	ShouldFilterIgnoreWithVar(name string) bool
	// IsTimeVariableFormatWithDate 判断时间变量格式是否是date
	IsTimeVariableFormatWithDate(name string) bool
	// GetVariableValueDefinition 获取变量定义
	GetVariableValueDefinition(name string) (*indicator_query.VariableValue, error)
	// GetExprVariableAllValueMap 获取表达式中的变量默认值map集合
	GetExprVariableAllValueMap(sql string) (varValueMap map[string]string, err error)
}

var _ VariableReplacerI = (*VariableReplacer)(nil)

type VariableReplacer struct {
	varDefs           []rpc_call.Variable              // 变量定义
	varVals           []*indicator_query.VariableValue // 变量动态值
	emptyVarSelectAll bool                             // 变量值为dmpIgnoreFlag时, 是否忽略过滤条件
}

func NewVariableReplacer(emptyVarSelectAll bool, varDefs []rpc_call.Variable, varVals []*indicator_query.VariableValue) *VariableReplacer {
	return &VariableReplacer{
		varDefs:           varDefs,
		varVals:           varVals,
		emptyVarSelectAll: emptyVarSelectAll,
	}
}

// ShouldFilterIgnoreWithVar 当开启了EmptyVarSelectAll选项, 如果变量值是dmpIgnoreFlag, 或变量没有传入动态值, 过滤器应该被忽略
func (s *VariableReplacer) ShouldFilterIgnoreWithVar(name string) bool {
	if !s.emptyVarSelectAll {
		return false
	}
	varDef, ok := lo.Find(s.varDefs, func(item rpc_call.Variable) bool { return item.Name == name })
	if !ok {
		return false
	}
	varVal, ok := lo.Find(s.varVals, func(item *indicator_query.VariableValue) bool { return item.Name == name })
	if !ok {
		return true
	}
	return s.varValIsDmpIgnoreFlag(varDef, varVal)
}

func (s *VariableReplacer) varValIsDmpIgnoreFlag(varDef rpc_call.Variable, varVal *indicator_query.VariableValue) bool {
	switch varDef.ScopeType {
	case rpc_call.SingleValue:
		return varVal.SingleValueContent != nil && varVal.SingleValueContent.Value == dmpIgnoreFlag
	case rpc_call.RangeValue:
		return varVal.RangeValueContent != nil && varVal.RangeValueContent.Left == dmpIgnoreFlag && varVal.RangeValueContent.Right == dmpIgnoreFlag
	case rpc_call.SequenceValue:
		return varVal.SequenceValueContent != nil && len(varVal.SequenceValueContent.ValueList) == 1 && varVal.SequenceValueContent.ValueList[0] == dmpIgnoreFlag
	default:
		return false
	}
}

func (s *VariableReplacer) GetVariableValueDefinition(name string) (*indicator_query.VariableValue, error) {
	varVal, ok := lo.Find(s.varVals, func(item *indicator_query.VariableValue) bool { return item.Name == name })
	if !ok {
		return nil, errors.Errorf("未查询到变量[%s]定义", name)
	}
	return varVal, nil
}

func (s *VariableReplacer) GetVariableValue(name string) (val string, err error) {
	// 获取变量定义
	varDef, ok := lo.Find(s.varDefs, func(item rpc_call.Variable) bool { return item.Name == name })
	if !ok {
		err = errors.New("未查询到变量[%s]定义")
		return
	}
	// 获取变量动态值, 如果变量动态值存在, 并且不是特殊值, 使用变量动态值
	varVal, ok := lo.Find(s.varVals, func(item *indicator_query.VariableValue) bool { return item.Name == name })
	if ok && !s.varValIsDmpIgnoreFlag(varDef, varVal) {
		return s.getVarDynamicValue(&varDef, varVal)
	}
	// 否则使用变量默认值
	return s.getVarDefaultValue(&varDef)
}

func (s *VariableReplacer) ReplaceExprVariables(sql string) (newSql string, err error) {
	// 解析sql片段中的变量
	exprRefVars := RegGetVariableExpr(sql)
	varValueMap := map[string]string{}
	// 获取变量替换值
	for _, v := range exprRefVars {
		varValueMap[v], err = s.GetVariableValue(v)
		if err != nil {
			return
		}
	}
	// 使用正则替换变量
	return RegReplaceVariableExpr(sql, varValueMap), nil
}

func (s *VariableReplacer) GetExprVariableAllValueMap(sql string) (varValueMap map[string]string, err error) {
	// 解析sql片段中的变量
	exprRefVars := RegGetVariableExpr(sql)
	varValueMap = map[string]string{}
	// 获取变量替换值
	for _, v := range exprRefVars {
		varValueMap[v], err = s.GetVariableValue(v)
		if err != nil {
			return
		}
	}
	return varValueMap, err
}

func (s *VariableReplacer) IsTimeVariableFormatWithDate(name string) bool {
	for _, varDef := range s.varDefs {
		if varDef.Name != name {
			continue
		}
		if varDef.ScopeType == rpc_call.SingleValue && varDef.DefaultDatetimeContent.DynamicType == rpc_call.StaticValue &&
			len(varDef.DefaultDatetimeContent.StaticValueContent) != 0 {
			val := varDef.DefaultDatetimeContent.StaticValueContent[0]
			_, err := time.Parse("2006-01-02", val)
			if err != nil {
				return false
			} else {
				return true
			}
		}
	}
	return true
}

func (s *VariableReplacer) getVarDynamicValue(varDef *rpc_call.Variable, varVal *indicator_query.VariableValue) (string, error) {
	if err := s.validVarVal(varDef, varVal); err != nil {
		return "", err
	}
	switch varDef.ScopeType {
	case rpc_call.SequenceValue:
		return s.wrapValue(varDef.ScopeType, varVal.SequenceValueContent.ValueList...), nil
	case rpc_call.SingleValue:
		return s.wrapValue(varDef.ScopeType, varVal.SingleValueContent.Value), nil
	case rpc_call.RangeValue:
		return s.wrapValue(varDef.ScopeType, varVal.RangeValueContent.Left, varVal.RangeValueContent.Right), nil
	default:
		return "", nil
	}
}

func (s *VariableReplacer) validDatetimeVarFormat(val string) error {
	if _, err := time.Parse("2006-01-02", val); err != nil {
		if _, err = time.Parse("2006-01-02 15:04:05", val); err != nil {
			return errors.Errorf("非法的日期格式[%s]", val)
		}
	}
	return nil
}

func (s *VariableReplacer) escapeStringVar(sql string) string {
	dest := make([]byte, 0, 2*len(sql))
	var escape byte
	for i := 0; i < len(sql); i++ {
		c := sql[i]
		escape = 0
		switch c {
		case 0: /* Must be escaped for 'mysql' */
			escape = '0'
			break
		case '\n': /* Must be escaped for logs */
			escape = 'n'
			break
		case '\r':
			escape = 'r'
			break
		case '\\':
			escape = '\\'
			break
		case '\'':
			escape = '\''
			break
			//case '"': /* Better safe than sorry */
			//	escape = '"'
			//	break
			//case '\032': //十进制26,八进制32,十六进制1a, /* This gives problems on Win32 */
			//	escape = 'Z'
		}

		if escape != 0 {
			dest = append(dest, '\\', escape)
		} else {
			dest = append(dest, c)
		}
	}

	return string(dest)
}

func (s *VariableReplacer) validVarVal(varDef *rpc_call.Variable, varVal *indicator_query.VariableValue) error {
	switch varDef.VariableType {
	case rpc_call.DatetimeVar:
		switch varDef.ScopeType {
		case rpc_call.SingleValue:
			if varVal.ScopeType != indicator_query.ScopeType_single {
				return errors.Errorf("变量[%s]范围类型[%s]错误", varVal.Name, varVal.ScopeType)
			}
			if varVal.SingleValueContent == nil {
				return errors.Errorf("变量[%s]值为空", varVal.Name)
			}
			if err := s.validDatetimeVarFormat(varVal.SingleValueContent.Value); err != nil {
				return err
			}
		case rpc_call.RangeValue:
			if varVal.ScopeType != indicator_query.ScopeType_range {
				return errors.Errorf("变量[%s]范围类型[%s]错误", varVal.Name, varVal.ScopeType)
			}
			if varVal.RangeValueContent == nil {
				return errors.Errorf("变量[%s]值为空", varVal.Name)
			}
			if err := s.validDatetimeVarFormat(varVal.RangeValueContent.Left); err != nil {
				return err
			}
			if err := s.validDatetimeVarFormat(varVal.RangeValueContent.Right); err != nil {
				return err
			}
		}
	case rpc_call.StringVar:
		switch varDef.ScopeType {
		case rpc_call.SingleValue:
			if varVal.ScopeType != indicator_query.ScopeType_single {
				return errors.Errorf("变量[%s]范围类型[%s]错误", varVal.Name, varVal.ScopeType)
			}
			if varVal.SingleValueContent == nil {
				return errors.Errorf("变量[%s]值为空", varVal.Name)
			}
		case rpc_call.SequenceValue:
			if varVal.ScopeType != indicator_query.ScopeType_sequence {
				return errors.Errorf("变量[%s]范围类型[%s]错误", varVal.Name, varVal.ScopeType)
			}
			if varVal.SequenceValueContent == nil {
				return errors.Errorf("变量[%s]值为空", varVal.Name)
			}
		case rpc_call.RangeValue:
			if varVal.ScopeType != indicator_query.ScopeType_range {
				return errors.Errorf("变量[%s]范围类型[%s]错误", varVal.Name, varVal.ScopeType)
			}
			if varVal.RangeValueContent == nil {
				return errors.Errorf("变量[%s]值为空", varVal.Name)
			}
		}
	default:
		return errors.Errorf("不支持的变量[%s]类型[%s]", varVal.Name, varDef.VariableType)
	}
	return nil
}

func (s *VariableReplacer) getVarDefaultValue(v *rpc_call.Variable) (string, error) {
	if v.VariableType == rpc_call.StringVar {
		switch v.ScopeType {
		case rpc_call.SequenceValue:
			values := strings.Split(v.DefaultStringContent.Value, ",")
			return s.wrapValue(v.ScopeType, values...), nil
		case rpc_call.SingleValue:
			return s.wrapValue(v.ScopeType, v.DefaultStringContent.Value), nil
		default:
			return s.wrapValue(v.ScopeType, v.DefaultStringContent.Value), nil
		}
	} else {
		return s.convertDatetimeVariableContent(v)
	}
}

func (s *VariableReplacer) convertDatetimeVariableContent(v *rpc_call.Variable) (string, error) {
	varDef := v.DefaultDatetimeContent
	if varDef.DynamicType == rpc_call.StaticValue {
		var now = time.Now()
		if v.ScopeType == rpc_call.SingleValue {
			var staticValue = now.Format(time.DateOnly)
			if len(varDef.StaticValueContent) > 0 {
				staticValue = varDef.StaticValueContent[0]
			}
			return s.wrapValue(v.ScopeType, staticValue), nil
		} else {
			// 设置默认值为当前日期月初和月末
			year, month, _ := now.Date()
			firstDay := time.Date(year, month, 1, 0, 0, 0, 0, now.Location())
			lastDay := firstDay.AddDate(0, 1, -1)
			left, right := firstDay.Format(time.DateOnly), lastDay.Format(time.DateOnly)
			if len(varDef.StaticValueContent) != 2 && len(varDef.StaticValueContent) != 0 {
				return "", errors.Errorf("变量[%s]静态时间默认值[%v]错误", v.Name, varDef.StaticValueContent)
			}
			if len(varDef.StaticValueContent) == 2 {
				left, right = varDef.StaticValueContent[0], varDef.StaticValueContent[1]
			}
			return s.wrapValue(v.ScopeType, left, right), nil
		}
	}

	// 替换动态时间参数
	return s.convertDatetimeDynamicVariable(v)
}

func (s *VariableReplacer) convertDatetimeDynamicVariable(v *rpc_call.Variable) (string, error) {
	// 依照产品逻辑，后续会废弃presto做查询, 这里仅需要支持sr语法
	varDef := v.DefaultDatetimeContent
	val := varDef.DynamicValueContent.RelativeValue
	var begin string
	var end string
	switch v.ScopeType {
	case rpc_call.RangeValue:
		switch varDef.DynamicValueContent.RelativeType {
		case rpc_call.Far:
			begin, end = time_utils.FarRange(val, 0)
		case rpc_call.FarYesterday:
			begin, end = time_utils.FarRange(val, 1)
		case rpc_call.FromWeek:
			begin, end = time_utils.FromWeekRange(val)
		case rpc_call.FromMonth:
			begin, end = time_utils.FromMonthRange(val)
		case rpc_call.FromQuarter:
			begin, end = time_utils.FromQuarterRange(val)
		case rpc_call.FromYear:
			begin, end = time_utils.FromYearRange(val)
		case rpc_call.RelativeTypeNone:
			begin, end = time_utils.FarRange(1, 0) // 如果没设置默认值，则默认为昨天到今天
		default:
			return "", errors.Errorf("不支持的相对时间类型[%s]", varDef.DynamicValueContent.RelativeType)
		}
		return s.wrapValue(v.ScopeType, begin, end), nil
	case rpc_call.SingleValue:
		switch varDef.DynamicValueContent.RelativeType {
		case rpc_call.Today:
			return s.wrapValue(v.ScopeType, time_utils.Far(0)), nil
		case rpc_call.Yesterday:
			return s.wrapValue(v.ScopeType, time_utils.Far(1)), nil
		case rpc_call.Far:
			return s.wrapValue(v.ScopeType, time_utils.Far(val)), nil
		case rpc_call.RelativeTypeNone:
			return s.wrapValue(v.ScopeType, time_utils.Far(1)), nil // 如果没设置默认值，则默认为昨天
		default:
			return "", errors.Errorf("不支持的相对时间类型[%s]", varDef.DynamicValueContent.RelativeType)
		}
	default:
		return "", errors.Errorf("不支持的时间区间类型[%s]", varDef.DynamicType)
	}
}

func (s *VariableReplacer) wrapValue(scope rpc_call.ScopeType, vals ...string) string {
	switch scope {
	case rpc_call.SingleValue:
		return fmt.Sprintf("'%s'", s.escapeStringVar(vals[0]))
	case rpc_call.RangeValue:
		return fmt.Sprintf("'%s' and '%s'", s.escapeStringVar(vals[0]), s.escapeStringVar(vals[1]))
	case rpc_call.SequenceValue:
		if len(vals) == 0 {
			return "null"
		}
		escapedVals := lo.Map(vals, func(item string, _ int) string {
			return "'" + s.escapeStringVar(item) + "'"
		})
		return "(" + strings.Join(escapedVals, ",") + ")"
	default:
		return ""
	}
}
