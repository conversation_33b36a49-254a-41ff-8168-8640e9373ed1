package injection

import (
	"context"

	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

var _ Variables = (*DefaultIndicatorFilterImpl)(nil)
var _ GlobalFilter = (*DefaultIndicatorFilterImpl)(nil)

type DefaultIndicatorFilterImpl struct {
	viewContent *proto.ViewContentNew
	varVals     []*indicator_query.VariableValue
}

func NewDefaultIndicatorFilterImpl(viewContent *proto.ViewContentNew, varVals []*indicator_query.VariableValue) *DefaultIndicatorFilterImpl {
	return &DefaultIndicatorFilterImpl{
		viewContent: viewContent,
		varVals:     varVals,
	}
}

func (s *DefaultIndicatorFilterImpl) GetGlobalFilter(ctx context.Context) (map[string][]*proto.Filter, error) {
	if s.viewContent == nil {
		return nil, nil
	}
	helper := NewConvertHelper(WithVarValues(&VariableContext{
		EmptyVarSelectAll: s.viewContent.EmptyVarSelectAll,
		VarDefs:           s.viewContent.Variables,
		VarVals:           s.varVals,
	}))
	result := make(map[string][]*proto.Filter)
	for _, modelFilter := range s.viewContent.GlobalFilter {
		filters, err := helper.NewConditionToFilter(s.viewContent.Dependence.TableName, modelFilter.Conditions, modelFilter.Combo)
		if err != nil {
			return nil, err
		}
		result[modelFilter.TableName] = lo.Map(filters, func(item proto.Filter, _ int) *proto.Filter {
			return &item
		})
	}
	global.Logger.JsonDebug("[DefaultFilterImpl] GetFilter Result: ", result)
	return result, nil
}

func (s *DefaultIndicatorFilterImpl) GetFilter(ctx context.Context) ([]*proto.Filter, error) {
	var ans = make([]*proto.Filter, 0)
	return ans, nil
}

func (s *DefaultIndicatorFilterImpl) GetVariableContext(ctx context.Context) (*VariableContext, error) {
	return &VariableContext{
		EmptyVarSelectAll: s.viewContent.EmptyVarSelectAll,
		VarDefs:           s.viewContent.Variables,
		VarVals:           nil,
	}, nil
}
