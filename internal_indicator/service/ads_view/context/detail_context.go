package context

import (
	"context"
	"fmt"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

type DetailBuildMetadata struct {
	ModelRelation     *common.ModelRelation `copier:"-"` // 忽略拷贝该字段
	AnalysisDimension []*proto.ModelField   // 所有维度
	AdditionalFields  []*proto.Field        // 查询的字段
	Filters           []*proto.Filter
	GlobalFilters     map[string][]*proto.Filter
	Sort              []*proto.Sort
	Limit             *proto.Limit
	VarCtx            *injection.VariableContext
}

type DetailBuildContext struct {
	ctx context.Context
	// 注入的水管
	dep          injection.DependenceModel
	fields       injection.DetailDefinition
	limit        injection.QueryLimit
	filter       injection.DetailFilter
	globalFilter injection.GlobalFilter
	sort         injection.DetailSort
	// 从水管抽取出来的水(元数据)
	metadata            *DetailBuildMetadata
	varctx              injection.Variables
	detailModelRelation injection.DetailModelRelation
}

type DetailOption func(buildContext *DetailBuildContext)

func WithDetailFilter(filter injection.DetailFilter) DetailOption {
	return func(s *DetailBuildContext) {
		s.filter = filter
	}
}

func WithDetailSort(sort injection.DetailSort) DetailOption {
	return func(s *DetailBuildContext) {
		s.sort = sort
	}
}

func WithDetailLimit(limit injection.QueryLimit) DetailOption {
	return func(s *DetailBuildContext) {
		s.limit = limit
	}
}

func WithDetailVariables(variables injection.Variables) DetailOption {
	return func(s *DetailBuildContext) {
		s.varctx = variables
	}
}

func WithDetailModelRelation(detailModelRelation injection.DetailModelRelation) DetailOption {
	return func(s *DetailBuildContext) {
		s.detailModelRelation = detailModelRelation
	}
}

func WithDetailGlobalFilter(globalFilter injection.GlobalFilter) DetailOption {
	return func(s *DetailBuildContext) {
		s.globalFilter = globalFilter
	}
}

func NewDetailBuildContext(ctx context.Context, dep injection.DependenceModel, fields injection.DetailDefinition,
	options ...DetailOption) *DetailBuildContext {
	var buildCtx = &DetailBuildContext{ctx: ctx, dep: dep, fields: fields}
	for _, option := range options {
		option(buildCtx)
	}
	return buildCtx
}

// Load 从依赖的水管中抽取必需的元数据
func (c *DetailBuildContext) Load() error {
	var err error
	c.metadata = &DetailBuildMetadata{}
	// 开始抽取必需的元数据
	c.metadata.ModelRelation, err = c.dep.GetModelRelation(c.ctx)
	if err != nil {
		return err
	}
	c.metadata.AnalysisDimension, err = c.dep.GetAnalyzableDimensionFields(c.ctx)
	if err != nil {
		return err
	}
	c.metadata.AdditionalFields, err = c.fields.GetAdditionalField(c.ctx)
	if err != nil {
		return err
	}
	// 抽取可选的元数据
	if c.filter != nil {
		c.metadata.Filters, err = c.filter.GetDetailFilter(c.ctx)
		if err != nil {
			return err
		}
	}
	if c.globalFilter != nil {
		c.metadata.GlobalFilters, err = c.globalFilter.GetGlobalFilter(c.ctx)
		if err != nil {
			return err
		}
	}
	if c.sort != nil {
		c.metadata.Sort, err = c.sort.GetDetailSort(c.ctx)
		if err != nil {
			return err
		}
	}
	if c.limit != nil {
		c.metadata.Limit, err = c.limit.GetLimit(c.ctx)
		if err != nil {
			return err
		}
	}
	if c.varctx != nil {
		c.metadata.VarCtx, err = c.varctx.GetVariableContext(c.ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

// GetContext 获取构建上下文中的Context
func (c *DetailBuildContext) GetContext() context.Context {
	return c.ctx
}

// GetBuildMeta 获取构建上下文中的元数据
func (c *DetailBuildContext) GetBuildMeta() *DetailBuildMetadata {
	return c.metadata
}

// GetModelRelation 获取指定模型的关联关系
func (c *DetailBuildContext) GetDetailModelRelationList() ([]*proto.DetailModelRelationInfo, error) {
	info := make([]*proto.DetailModelRelationInfo, 0)
	if c.detailModelRelation == nil {
		return info, nil
	}
	return c.detailModelRelation.GetDetailModelRelation(context.Background())
}

// GetTables 获取构建上下文中的所有表
func (c *DetailBuildContext) GetTables() ([]proto.ModelNode, error) {
	var nodeMap = make(map[string]*proto.ModelNode)
	for _, node := range c.metadata.ModelRelation.Nodes {
		nodeMap[node.Table] = node
	}
	var err error
	var tables = make([]proto.ModelNode, 0)
	// 主表不管是否使用都需要加入到表列表中
	tables = append(tables, *c.metadata.ModelRelation.Nodes[0])
	// 附加字段是明细时使用: 所以需要加入到表列表中
	for _, field := range c.metadata.AdditionalFields {
		node, ok := nodeMap[field.Table]
		if !ok {
			return nil, fmt.Errorf("[明细字段] 表%s不存在依赖模型中", field.Table)
		}
		tables = append(tables, *node)
	}
	// 获取过滤条件中引用的表
	if err = common.ForeachFieldInFilters(c.metadata.Filters, func(field *proto.Field) error {
		node, ok := nodeMap[field.Table]
		if !ok {
			return fmt.Errorf("[明细过滤] 表%s不存在依赖模型中", field.Table)
		}
		tables = append(tables, *node)
		return nil
	}); err != nil {
		return nil, err
	}
	// 获取排序中引用的表
	if err = common.ForeachFieldInSort(c.metadata.Sort, func(field *proto.Field) error {
		node, ok := nodeMap[field.Table]
		if !ok {
			return fmt.Errorf("[明细排序] 表%s不存在依赖模型中", field.Table)
		}
		tables = append(tables, *node)
		return nil
	}); err != nil {
		return nil, err
	}
	// 表去重
	tables = common.RemoveDuplicates(tables)
	return tables, nil
}

func (s *DetailBuildMetadata) GetVariableContext(_ context.Context) (*injection.VariableContext, error) {
	return s.VarCtx, nil
}
