package ads_view

import (
	context2 "context"
	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/context"
	dimension2 "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/dimension"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/indicator"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
)

// 设置缺省资源类型
var defaultResourceType = entities.RDSResourceType

type Query struct {
	buildContext     *context.BuildContext
	indicatorFactory *indicator.Factory
	visitedHelper    map[proto.ModelNode]bool
	pathHelper       []proto.ModelNode
}

func NewQuery(buildContext *context.BuildContext) *Query {
	return &Query{
		buildContext:     buildContext,
		indicatorFactory: indicator.NewFactory(),
		visitedHelper:    make(map[proto.ModelNode]bool),
		pathHelper:       make([]proto.ModelNode, 0),
	}
}

func (q *Query) GetAnalyticDimension(ctx context2.Context) (ans proto.AnalyticDimension, err error) {
	sctx, sp := trace_utils.StartSpanWithContext(ctx, "获取可分析维度")
	sp.Name("method: GetAnalyticDimension")
	defer sp.End()

	err = q.buildContext.Load(sctx)
	if err != nil {
		return
	}

	presetTimeCostFn := trace_utils.TraceTimeCost(sctx, "PresetDimension")
	if err = q.buildContext.PresetDimension(defaultResourceType); err != nil {
		return
	}
	presetTimeCostFn()

	// step1: 首先从所有度量定义获取到所有的度量表
	s1TimeCostFn := trace_utils.TraceTimeCost(sctx, "step1")
	var measureTableSet = hashset.New()
	var metadata = q.buildContext.GetBuildMeta()
	for _, definition := range metadata.Definitions {
		var refFields []proto.ModelField
		refFields, err = q.indicatorFactory.GetIndicator(definition, defaultResourceType).GetRefFields(q.buildContext.GetContext())
		if err != nil {
			return
		}
		for _, refField := range refFields {
			measureTableSet.Add(refField.Table)
		}
	}
	s1TimeCostFn()

	// step2: 根据模型场景区分出可分析的表
	s2TimeCostFn := trace_utils.TraceTimeCost(sctx, "step2")
	var analyticTableSet *hashset.Set
	var scene proto.ModelScene
	scene, err = q.buildContext.TryGetBaseOrAdvanceScene()
	if err != nil {
		return
	}
	switch scene {
	case proto.RelySingleTable:
		analyticTableSet = hashset.New(metadata.ModelRelation.Nodes[0].Table) // 依赖单表，可分析模型就是单表自身
	case proto.MissOneToManyInRelyRelation:
		measureTableSet.Clear()                                                             // 如果是多对一，则放开所有可分析维度
		analyticTableSet, err = q.getAnalyticTable(metadata.ModelRelation, measureTableSet) // 依赖关系模型，则额外处理
		if err != nil {
			return
		}
	default:
		analyticTableSet, err = q.getAnalyticTable(metadata.ModelRelation, measureTableSet) // 依赖关系模型，则额外处理
		if err != nil {
			return
		}
	}
	s2TimeCostFn()

	// step3: 将可分析模型添加到结果中
	s3TimeCostFn := trace_utils.TraceTimeCost(sctx, "step3")
	ans.Tables = make([]proto.ModelNode, 0)
	for _, table := range analyticTableSet.Values() {
		var node = metadata.ModelRelation.GetNode(table.(string))
		ans.Tables = append(ans.Tables, *node)
	}
	s3TimeCostFn()

	// step4: 将可分析模型中的可分析字段添加到结果中
	s4TimeCostFn := trace_utils.TraceTimeCost(sctx, "step4")
	ans.TableFields = make(map[string][]proto.ModelField, 0)
	for _, analyticField := range metadata.AnalysisDimension {
		if !analyticTableSet.Contains(analyticField.Table) {
			continue
		}
		list, ok := ans.TableFields[analyticField.Table]
		if !ok {
			list = make([]proto.ModelField, 0)
		}
		list = append(list, *analyticField)
		ans.TableFields[analyticField.Table] = list
	}
	s4TimeCostFn()

	// step5： 根据可分析模型和字段来判断哪些维度定义可以使用
	s5TimeCostFn := trace_utils.TraceTimeCost(sctx, "step5")
	for _, dimension := range metadata.DimensionsToBeAnalyzed {
		var instance = dimension2.NewFactory().GetDimension(dimension,
			defaultResourceType, dimension2.DynamicParam{
				Type:  proto.WithGraph,
				Value: metadata.ModelRelation,
			}, dimension2.DynamicParam{
				Type:  proto.WithIndicatorDefinition,
				Value: metadata.Definitions,
			})
		var isAnalysis bool
		isAnalysis, err = instance.IsAnalytic(q.buildContext.GetContext(), ans.TableFields)
		if err != nil {
			return
		}
		if !isAnalysis {
			continue
		}
		ans.Dimensions = append(ans.Dimensions, *dimension)
	}
	s5TimeCostFn()
	return
}

// getAnalyticTable 根据度量表和关系模型来获取可分析的表
func (q *Query) getAnalyticTable(graph *common.ModelRelation, measureTableSet *hashset.Set) (*hashset.Set, error) {
	/*
		定义并保留两种路径:
		0.路径中使用到了度量表
		1.路径中没有使用到度量表
	*/
	pathClassifyMatrix := make([][]*hashset.Set, 2)
	pathClassifyMatrix[0] = make([]*hashset.Set, 0)
	pathClassifyMatrix[1] = make([]*hashset.Set, 0)
	var mainTable = graph.Nodes[0].Table

	// step1: 将关系模型中的所有路径抽取出来形成列表
	pathList, err := q.getRelationModelPath(graph)
	if err != nil {
		return nil, err
	}

	// step2: 遍历所有路径，对路径进行分类以及优化处理
	for _, path := range pathList {
		idx, pathSet, err := q.ClassifyAndOptimizePath(graph, path, measureTableSet)
		if err != nil {
			return nil, err
		}
		pathClassifyMatrix[idx] = append(pathClassifyMatrix[idx], pathSet)
	}

	// step3: 根据分类结果来设置最终的可分析表
	var analyticTableSet = hashset.New()

	// case-1：如果存在第0类路径，那么对第0类路径求交集
	if len(pathClassifyMatrix[0]) > 0 {
		var intersectionSet = pathClassifyMatrix[0][0]
		for i := 1; i < len(pathClassifyMatrix[0]); i++ {
			intersectionSet = intersectionSet.Intersection(pathClassifyMatrix[0][i])
		}
		for _, node := range intersectionSet.Values() {
			analyticTableSet.Add(node.(proto.ModelNode).Table)
		}

		// 如果存在有度量表路径的情况下，算出来的结果为空，那么就直接返回
		if analyticTableSet.Empty() {
			return analyticTableSet, nil
		}

		// 如果存在有度量表路径的情况下，算出来的结果不包含主表，那么也直接返回
		if !analyticTableSet.Contains(mainTable) {
			return analyticTableSet, nil
		}
	}

	// case-2：如果存在第1类路径，那么对第1类路径求并集
	if len(pathClassifyMatrix[1]) > 0 {
		var unionSet = pathClassifyMatrix[1][0]
		for i := 1; i < len(pathClassifyMatrix[1]); i++ {
			unionSet = unionSet.Union(pathClassifyMatrix[1][i])
		}
		for _, node := range unionSet.Values() {
			analyticTableSet.Add(node.(proto.ModelNode).Table)
		}
	}
	return analyticTableSet, nil
}

// ClassifyAndOptimizePath 0类是路径中使用到了度量表，1类是路径中没有使用到度量表
func (q *Query) ClassifyAndOptimizePath(graph *common.ModelRelation, path []proto.ModelNode, measureTableSet *hashset.Set) (
	classifyIndex int, ans *hashset.Set, err error) {

	// 场景1: 如果度量表为空，那么默认所有路径都是可分析维度，归类为1
	if measureTableSet.Empty() {
		classifyIndex = 1
		ans = hashset.New()
		for _, node := range path {
			ans.Add(node)
		}
		return
	}

	// 首先遍历路径，构建路径中大表的索引，为后续场景分析做准备
	var bigTableSet = hashset.New()
	for _, node := range path {
		if node.IsBigTable {
			bigTableSet.Add(node.Table)
		}
	}

	var hitMeasureInPath = false
	for _, node := range path {
		if measureTableSet.Contains(node.Table) {
			hitMeasureInPath = true
			break
		}
	}

	if !hitMeasureInPath {
		// 场景2: 有度量表，但是路径中没有用到
		classifyIndex, ans, err = missMeasureInPathAnalysis(path, bigTableSet)
		if err != nil {
			return
		}
	} else {
		// 场景3: 有度量表并且路径中也用到了
		classifyIndex, ans, err = hitMeasureInPathAnalysis(graph, path, measureTableSet, bigTableSet)
		if err != nil {
			return
		}
	}
	return
}

func missMeasureInPathAnalysis(path []proto.ModelNode, bigTableSet *hashset.Set) (
	classifyIdx int, ans *hashset.Set, err error) {
	classifyIdx = 1
	ans = hashset.New()
	for i, node := range path {
		// 抛开主表，如果从表是一张N表或者是一张事实表或者汇总表，那么到这个位置就截止了
		if i > 0 && (bigTableSet.Contains(node.Table) || node.Type == proto.DwsTable || node.Type == proto.DwdTable) {
			break
		}
		ans.Add(node)
	}
	return
}

func hitMeasureInPathAnalysis(graph *common.ModelRelation, path []proto.ModelNode, measureTableSet *hashset.Set, bigTableSet *hashset.Set) (
	classifyIdx int, ans *hashset.Set, err error) {
	classifyIdx = 0
	var pathList = make([]*hashset.Set, 0)
	// 遍历找到所有的度量表路径
	for i, node := range path {
		if !measureTableSet.Contains(node.Table) {
			continue
		}
		var measurePath *hashset.Set
		measurePath, err = calcMeasurePath(graph, path, bigTableSet, i)
		if err != nil {
			return
		}
		pathList = append(pathList, measurePath)
	}
	// 对路径列表求交集
	ans = pathList[0]
	for i := 1; i < len(pathList); i++ {
		ans = ans.Intersection(pathList[i])
	}
	// 如果求完交集后，存在主表是度量表的情况，那么则将这条路径转换为第1类路径
	var mainNode = path[0]
	if ans.Contains(mainNode) && measureTableSet.Contains(mainNode.Table) {
		classifyIdx = 1
	}
	return
}

func calcMeasurePath(graph *common.ModelRelation, path []proto.ModelNode, bigTableSet *hashset.Set, measureTableIdx int) (ans *hashset.Set, err error) {
	// 找到之后，开始一路回溯至主表
	ans = hashset.New(path[measureTableIdx])
	var previousNode = path[measureTableIdx]
	for i := measureTableIdx - 1; i >= 0; i-- {
		// 所以条件是如果上一张表是小表并且当前是大表，那么就截止(即N:1关系)
		var edg = graph.GetEdge(path[i].Table, previousNode.Table)
		if edg != nil && edg.JoinDataType == proto.ManyToOne {
			break
		}
		ans.Add(path[i])
		previousNode = path[i]
	}
	// 不管最后有没有回溯到主表，我们再从找到的度量表开始往后遍历，如果遇到大表或事实表或度量表，那么就截止
	for i := measureTableIdx + 1; i < len(path); i++ {
		var node = path[i]
		if bigTableSet.Contains(node.Table) || node.Type == proto.DwsTable ||
			node.Type == proto.DwdTable {
			break
		}
		ans.Add(node)
	}
	return
}

func (q *Query) getRelationModelPath(relation *common.ModelRelation) (paths [][]proto.ModelNode, err error) {
	paths = make([][]proto.ModelNode, 0)
	if len(relation.Nodes) < 1 {
		err = errors.New("关系模型中没有节点")
		return
	}
	// 获取根节点
	root := relation.Nodes[0]
	if relation.EnterPoint != nil {
		root = relation.EnterPoint
	}
	q.visitedHelper = make(map[proto.ModelNode]bool)
	q.pathHelper = make([]proto.ModelNode, 0)
	err = q.dfsRelation(relation, nil, *root, &paths)
	if err != nil {
		return
	}
	return
}

func (q *Query) dfsRelation(relation *common.ModelRelation, incomingEdg *proto.ModelEdg,
	root proto.ModelNode, paths *[][]proto.ModelNode) error {
	// 已经被访问过，则直接返回
	if _, ok := q.visitedHelper[root]; ok {
		return nil
	}
	// 首先将该顶点加入到已访问列表中
	q.visitedHelper[root] = true
	// 将该顶点加入到路径中
	var pathNode = root
	// 内置规则: 如果不是主表并且是被1:N关联或者是一张事实表或者汇总表，那么被归类为大表写入路径
	if incomingEdg != nil && incomingEdg.JoinDataType == proto.OneToMany {
		pathNode.IsBigTable = true
	}
	q.pathHelper = append(q.pathHelper, pathNode)

	var outEdgList = relation.Edges[root]
	if len(outEdgList) < 1 {
		*paths = append(*paths, cloneAndOptimizationPath(relation, q.pathHelper))
	}

	// 接口递归访问root节点的所有邻接节点
	for _, edg := range relation.Edges[root] {
		var err = q.dfsRelation(relation, edg, edg.RelationNode, paths)
		if err != nil {
			return err
		}
	}

	// 回溯路径中移除当前节点
	q.pathHelper = q.pathHelper[:len(q.pathHelper)-1]
	return nil
}

func cloneAndOptimizationPath(relation *common.ModelRelation, path []proto.ModelNode) []proto.ModelNode {
	var ans = make([]proto.ModelNode, len(path))
	copy(ans, path)
	if len(ans) < 2 {
		return ans
	}
	// 内置规则: 如果路径中主表是N:1第一个从表，那么主表也被归类为大表
	mainNode := *relation.Nodes[0]
	for _, edg := range relation.Edges[mainNode] {
		if edg.RelationNode.Table == ans[1].Table && edg.JoinDataType == proto.ManyToOne {
			ans[0].IsBigTable = true
			break
		}
	}
	return ans
}
