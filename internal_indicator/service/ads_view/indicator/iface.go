package indicator

import (
	"context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

type AdsViewIndicator interface {
	GenViewExpr(ctx context.Context) (*proto.IndicatorExprInfo, error)
	GetRefFields(ctx context.Context) ([]proto.ModelField, error)
	ExtractAggExprInfoByField(ctx context.Context, tableName, fieldName string) ([]*proto.AggExprInfo, error)
	ApplyRollupAggExpr(ctx context.Context, aggExpr proto.AggExprInfo) error
}
