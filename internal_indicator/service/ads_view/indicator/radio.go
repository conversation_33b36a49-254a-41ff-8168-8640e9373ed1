package indicator

import (
	"context"
	"fmt"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"gitlab.mypaas.com.cn/dmp/gopkg/build_plan"
)

func DefinitionRadioExpress(ctx context.Context, engine entities.ProjectResourceType, express string) (string, error) {
	// 通过给express自动套上cast as double来解决达梦比率失败的问题
	express = fmt.Sprintf("CAST(%s AS DOUBLE)", express)
	var radioFormatExpress = fmt.Sprintf("COALESCE((%s) / NULLIF(SUM(%s) over (),0),0)", express, express)
	var result = &build_plan.Project{
		Type: build_plan.ProjectType_RawExpress,
		RawContent: &build_plan.RawExpressContent{
			RawExpress: radioFormatExpress,
			CastType: &build_plan.CastType{
				Type:      "decimal",
				Length:    19,
				Precision: 12,
			},
		},
	}
	builder, err := common.GetBuilder(engine)
	if err != nil {
		return "", err
	}
	return builder.GetProjectExpress(ctx, []*build_plan.Project{result})
}
