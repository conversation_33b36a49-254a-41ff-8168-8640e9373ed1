package indicator

import (
	"context"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/dmp/gopkg/build_plan"
)

type SimpleDefinition struct {
	def          *proto.Definition
	resourceType entities.ProjectResourceType
}

type SimpleOption func(*SimpleDefinition)

func NewSimpleDefinition(def *proto.Definition, resourceType entities.ProjectResourceType, options ...SimpleOption) *SimpleDefinition {
	var ins = &SimpleDefinition{
		def:          def,
		resourceType: resourceType,
	}
	for _, fn := range options {
		fn(ins)
	}
	return ins
}

func (s *SimpleDefinition) GenViewExpr(ctx context.Context) (*proto.IndicatorExprInfo, error) {
	var definition = s.def
	var alias = definition.TryGetAlias()
	// 如果是空定义，则返回空表达式
	if definition.IsNull {
		return &proto.IndicatorExprInfo{
			Name:      definition.Name,
			NameCn:    definition.NameCn,
			ExprAlias: alias,
			Expr:      "NULL",
		}, nil
	}
	var err error
	var express string
	// 生成最内层的字段表达式
	express, err = common.GenFieldExpr(s.resourceType, definition.Table, definition.Field)
	if err != nil {
		return nil, err
	}
	// wrap一层过滤
	if s.isApplyFilter(definition) {
		express, err = s.applySimpleFieldFilter(express, definition.Filters)
		if err != nil {
			return nil, err
		}
	}
	// wrap一层聚合函数

	//todo 抽成多引擎处理
	express = s.quoteProcessingByResourceType(express)
	express, err = common.GenAggExpr(s.resourceType, definition.Function, express)
	if err != nil {
		return nil, err
	}

	// 如果设置了强制替换表达式，则默认使用强制替换表达式
	if s.def.ForceReplaceExpression != "" {
		express = s.def.ForceReplaceExpression
	}

	// wrap一层比率
	if definition.CalcRatio {
		express, err = DefinitionRadioExpress(ctx, s.resourceType, express)
		if err != nil {
			return nil, err
		}
	}
	var ans = &proto.IndicatorExprInfo{
		Name:      definition.Name,
		NameCn:    definition.NameCn,
		Expr:      express,
		ExprAlias: alias,
	}
	return ans, nil
}

func (s *SimpleDefinition) quoteProcessingByResourceType(express string) string {
	//多引擎处理
	if s.resourceType == entities.DamengSaaSResourceType || s.resourceType == entities.DamengResourceType {

	} else {
		express = strings.ReplaceAll(express, "\"", "")
	}
	return express
}

func (s *SimpleDefinition) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	var ans = make([]proto.ModelField, 0)
	if s.def.IsNull {
		return ans, nil
	}
	ans = append(ans, proto.ModelField{
		Table: s.def.Table,
		Field: s.def.Field,
	})
	refFilterField, err := getFilterRefFields(s.def.Filters)
	if err != nil {
		return nil, err
	}
	ans = append(ans, refFilterField...)
	return ans, nil
}

func (s *SimpleDefinition) ExtractAggExprInfoByField(ctx context.Context, tableName, fieldName string) ([]*proto.AggExprInfo, error) {
	// 简单模型下，如果指定的表和字段与定义中的表和字段不一致，则返回nil
	if s.def.Table != tableName || s.def.Field != fieldName || !s.def.Function.IsAggregate() {
		return nil, nil
	}
	// 生成表达式
	exprInfo, err := s.GenViewExpr(ctx)
	if err != nil {
		return nil, err
	}
	var info = &proto.AggExprInfo{
		IndicatorName: s.def.Name,
		Func:          s.def.Function,
		Index:         0,
		Expr:          exprInfo.Expr,
		Table:         tableName,
		Alias:         exprInfo.ExprAlias,
	}
	var ans = []*proto.AggExprInfo{info}
	return ans, nil
}

func (s *SimpleDefinition) ApplyRollupAggExpr(ctx context.Context, aggExpr proto.AggExprInfo) error {
	// 如果使用了高阶字段，则忽略掉
	if s.def.FromAdvanceSubquery {
		return nil
	}
	// 从最底层开始上卷的时候会应用多次，以最新的一次变更为主
	var err error
	s.def.ForceReplaceExpression, err = common.GenAggExpr(s.resourceType, aggExpr.Func.RollupFunc(), aggExpr.Expr)
	if err != nil {
		return err
	}
	return nil
}

func (s *SimpleDefinition) applySimpleFieldFilter(express string, filters []*proto.Filter) (string, error) {
	var builder build_plan.Builder
	var err error
	builder, err = common.GetBuilder(s.resourceType)
	if err != nil {
		return "", err
	}
	var filterList = make([]*build_plan.Filter, 0)
	for _, filter := range filters {
		filterPlan, err := common.ConvertFilter(filter, nil)
		if err != nil {
			return "", err
		}
		filterList = append(filterList, filterPlan)
	}
	return builder.SelectFilterExpress(context.Background(), express, filterList)
}

func (s *SimpleDefinition) isApplyFilter(definition *proto.Definition) bool {
	if len(definition.Filters) < 1 {
		return false
	}
	// 如果来源于高阶时间子查询，则应用过滤条件
	if definition.FromAdvanceSubquery {
		return true
	}
	// 默认应用
	return true
}

func getFilterRefFields(filters []*proto.Filter) ([]proto.ModelField, error) {
	var ans = make([]proto.ModelField, 0)
	for _, filter := range filters {
		if len(filter.SubFilter) > 0 {
			fields, err := getFilterRefFields(filter.SubFilter)
			if err != nil {
				return nil, err
			}
			ans = append(ans, fields...)
			continue
		}
		if filter.LeftExpType != proto.FieldExp {
			continue
		}
		ans = append(ans, proto.ModelField{
			Table:   filter.Field.Table,
			Field:   filter.Field.Field,
			FieldCn: filter.Field.FieldCn,
		})
	}
	return ans, nil
}
