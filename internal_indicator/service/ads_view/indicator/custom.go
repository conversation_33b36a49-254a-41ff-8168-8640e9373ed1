package indicator

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"strings"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type CustomDefinition struct {
	def          *proto.Definition
	resourceType entities.ProjectResourceType
}

type CustomOption func(*CustomDefinition)

func NewCustomDefinition(def *proto.Definition, resourceType entities.ProjectResourceType, options ...CustomOption) *CustomDefinition {
	var ins = &CustomDefinition{
		def:          def,
		resourceType: resourceType,
	}
	for _, fn := range options {
		fn(ins)
	}
	return ins
}

func (s *CustomDefinition) GenViewExpr(ctx context.Context) (*proto.IndicatorExprInfo, error) {
	var definition = s.def
	var alias = definition.TryGetAlias()
	// 如果是空定义，则返回空表达式
	if definition.IsNull {
		return &proto.IndicatorExprInfo{
			Name:      definition.Name,
			NameCn:    definition.NameCn,
			Expr:      "NULL",
			ExprAlias: alias,
		}, nil
	}
	var err error
	var express = definition.SqlExpress

	// 如果设置了强制替换表达式，则默认使用强制替换表达式
	if s.def.ForceReplaceExpression != "" {
		express = s.def.ForceReplaceExpression
	}

	// wrap占比表达式
	if definition.CalcRatio {
		express, err = DefinitionRadioExpress(ctx, s.resourceType, express)
		if err != nil {
			return nil, err
		}
	}
	var ans = &proto.IndicatorExprInfo{
		Name:      definition.Name,
		NameCn:    definition.NameCn,
		Expr:      express,
		ExprAlias: alias,
	}
	return ans, nil
}

func (s *CustomDefinition) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	if s.def.IsNull {
		return []proto.ModelField{}, nil
	}
	return extractModelFields(s.def.ReferenceFields)
}

func (s *CustomDefinition) ExtractAggExprInfoByField(ctx context.Context, tableName, fieldName string) ([]*proto.AggExprInfo, error) {
	// 1.将自定义表达式转换为AST树
	var customSQL = fmt.Sprintf("SELECT %s from dual", s.def.SqlExpress)
	if s.resourceType == entities.DamengSaaSResourceType || s.resourceType == entities.DamengResourceType {
		customSQL = strings.Replace(customSQL, "\"", "`", -1)
	}
	tree, err := common.NewAstTree(customSQL, global.AppConfig.GetDBType())
	if err != nil {
		return nil, err
	}
	// 2.从AST树中获取自定义sql中的聚合函数
	var aggFuncList []common.AggInfo
	aggFuncList, err = tree.GetOuterAggFuncsByField(common.Column{
		Table: tableName,
		Field: fieldName,
	})
	if err != nil {
		return nil, err
	}
	if len(aggFuncList) < 1 {
		return nil, nil
	}
	// 3.填充后返回结果
	var ans = make([]*proto.AggExprInfo, 0)
	for _, aggFunc := range aggFuncList {
		var expr string
		expr, err = tree.ExtractAggExpr(aggFunc)
		if err != nil {
			return nil, err
		}
		if s.resourceType == entities.DamengSaaSResourceType || s.resourceType == entities.DamengResourceType {
			expr = strings.ReplaceAll(expr, "`", "")
		}
		var alias = fmt.Sprintf("%s_agg_%d", s.def.TryGetAlias(), aggFunc.Idx)
		ans = append(ans, &proto.AggExprInfo{
			IndicatorName: s.def.Name,
			Func:          aggFunc.AggFunc,
			Index:         aggFunc.Idx,
			Expr:          expr,
			Table:         tableName,
			Alias:         alias,
		})
	}
	return ans, nil
}

func (s *CustomDefinition) ApplyRollupAggExpr(ctx context.Context, aggExpr proto.AggExprInfo) error {
	// 如果使用了高阶字段，则忽略掉
	if s.def.FromAdvanceSubquery {
		return nil
	}
	// 自定义sql中可能会出现多个聚合函数，依次替换掉就好了(如果同样的位置的聚合函数里面有多个字段，即多次聚合替换，则以最后一次为准)
	if s.def.ForceReplaceExpression == "" {
		s.def.ForceReplaceExpression = s.def.SqlExpress
	}

	var customSQL = fmt.Sprintf("SELECT %s FROM dual", s.def.ForceReplaceExpression)
	tree, err := common.NewAstTree(customSQL, global.AppConfig.GetDBType())
	if err != nil {
		return err
	}
	var replacedSql string
	var newAggExpr string
	newAggExpr, err = common.GenAggExpr(s.resourceType, aggExpr.Func.RollupFunc(), aggExpr.Expr)
	if err != nil {
		return err
	}

	//替换双引号为反引号 SUM("dwd_reality_business_order_detail_di"."指标2_agg_0") --> SUM(dwd_reality_business_order_detail_di.指标2_agg_0) 这种情况报错
	newAggExpr = strings.ReplaceAll(newAggExpr, "\"", "`")
	replacedSql, err = tree.ReplaceAggExpr(common.AggInfo{AggFunc: aggExpr.Func, Idx: aggExpr.Index}, newAggExpr)
	if err != nil {
		return err
	}

	// 掐掉SELECT头和FROM尾
	replacedSql = strings.TrimPrefix(replacedSql, "select ")
	replacedSql = strings.TrimSuffix(replacedSql, " from dual")
	s.def.ForceReplaceExpression = replacedSql
	replacedSql = strings.ReplaceAll(replacedSql, "`", "")
	return nil
}

func extractModelFields(fields []*proto.Field) ([]proto.ModelField, error) {
	var ans = make([]proto.ModelField, 0)
	for _, field := range fields {
		ans = append(ans, proto.ModelField{
			Table: field.Table,
			Field: field.Field,
		})
	}
	return ans, nil
}
