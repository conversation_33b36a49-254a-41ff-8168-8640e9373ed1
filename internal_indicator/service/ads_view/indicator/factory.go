package indicator

import (
	"context"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type Factory struct {
}

func NewFactory() *Factory {
	return &Factory{}
}

type DynamicParam struct {
	Type  proto.DynamicParamType
	Value any
}

func (s *Factory) GetIndicator(indicatorDef *proto.Definition, resourceType entities.ProjectResourceType, params ...DynamicParam) AdsViewIndicator {
	var indicator AdsViewIndicator
	switch indicatorDef.FormulaType {
	case proto.DefaultFormulaType:
		indicator = NewSimpleDefinition(indicatorDef, resourceType)
	case proto.CustomFormulaType:
		indicator = NewCustomDefinition(indicatorDef, resourceType)
	default:
		indicator = NewDefaultIndicator("创建指标表达式生成器时类型不支持，执行表达式生成无效")
	}
	return indicator
}

type DefaultIndicator struct {
	genViewExprErrMsg string
}

func NewDefaultIndicator(errMsg string) *DefaultIndicator {
	return &DefaultIndicator{
		genViewExprErrMsg: errMsg,
	}
}

func (s *DefaultIndicator) GenViewExpr(ctx context.Context) (*proto.IndicatorExprInfo, error) {
	return nil, errors.New(s.genViewExprErrMsg)
}

func (s *DefaultIndicator) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	return []proto.ModelField{}, nil
}

func (s *DefaultIndicator) ExtractAggExprInfoByField(ctx context.Context, tableName string, fieldName string) ([]*proto.AggExprInfo, error) {
	return nil, nil
}

func (s *DefaultIndicator) ApplyRollupAggExpr(ctx context.Context, aggExpr proto.AggExprInfo) error {
	return nil
}
