package ads_view

import (
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/scene"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/scene/contain_advance_datetime"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/scene/data_completion_required"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/scene/detail_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/scene/hit_one_to_many"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type Builder struct {
	buildContext       *context.BuildContext
	detailBuildContext *context.DetailBuildContext
	resourceType       entities.ProjectResourceType
}

func NewBuilder(buildContext *context.BuildContext, resourceType entities.ProjectResourceType) *Builder {
	return &Builder{buildContext: buildContext, resourceType: resourceType}
}

func NewBuilderForDetail(detailBuildContext *context.DetailBuildContext, resourceType entities.ProjectResourceType) *Builder {
	return &Builder{detailBuildContext: detailBuildContext, resourceType: resourceType}
}

func (b *Builder) ToSQL() (sql string, err error) {
	if b.detailBuildContext != nil {
		return b.genDetailSQL()
	}
	return b.genIndicatorSQL()
}

func (b *Builder) genIndicatorSQL() (sql string, err error) {
	// 1、先调用构建上下文的加载函数获取基础元数据
	if err = b.buildContext.Load(b.buildContext.GetContext()); err != nil {
		return
	}
	if err = b.buildContext.PresetDimension(b.resourceType); err != nil {
		return
	}
	// 尝试判断是否为wrap场景
	var wrapScene proto.ModelScene
	wrapScene, err = b.buildContext.TryGetWrapScene()
	if err != nil {
		return
	}
	if wrapScene != proto.UnKnownScene {
		sql, err = b.toWrapSceneSql(wrapScene)
		return
	}
	// 尝试获取基础场景或高阶场景
	var modelScene proto.ModelScene
	modelScene, err = b.buildContext.TryGetBaseOrAdvanceScene()
	if err != nil {
		return
	}
	sql, err = b.toBaseOrAdvanceSceneSql(modelScene)
	return
}

func (b *Builder) toBaseOrAdvanceSceneSql(modelScene proto.ModelScene) (sql string, err error) {
	var sceneImpl scene.ModelSceneStatement
	switch modelScene {
	case proto.RelySingleTable:
		sceneImpl = scene.NewSingleTableImpl(b.buildContext, b.resourceType)
	case proto.HitOneToManyInRelyRelation:
		sceneImpl = hit_one_to_many.NewHitOneToManyImpl(b.buildContext, b.resourceType)
	case proto.MissOneToManyInRelyRelation:
		sceneImpl = scene.NewMissOneToManyImpl(b.buildContext, b.resourceType)
	case proto.ContainAdvanceDatetime:
		advanceDatetimeImpl := contain_advance_datetime.NewContainAdvanceDatetimeImpl(b.buildContext, b.resourceType)
		var newMetadata *context.BuildMetadata
		// 重新生成元数据
		if newMetadata, err = advanceDatetimeImpl.RebuildMetadata(); err != nil {
			return
		}
		var newBuildCtx = context.NewBuildContext(b.buildContext.GetContext(), newMetadata, newMetadata,
			context.WithGlobalFilter(newMetadata),
			context.WithFilter(newMetadata),
			context.WithAggregate(newMetadata),
			context.WithSort(newMetadata),
			context.WithLimit(newMetadata),
			context.WithVariables(newMetadata),
			context.WithNonNativeFlag(), // 标识上下文是改造过后的，非原生的
		)
		newBuilder := NewBuilder(newBuildCtx, b.resourceType)
		return newBuilder.ToSQL()
	default:
		err = errors.New("invalid model scene")
		return
	}
	sql, err = sceneImpl.GenSQL()
	return
}

func (b *Builder) toWrapSceneSql(modelScene proto.ModelScene) (sql string, err error) {
	// 首先获取被wrap的场景sql
	var wrapperScene proto.ModelScene
	wrapperScene, err = b.buildContext.TryGetBaseOrAdvanceScene()
	if err != nil {
		return
	}
	var wrapperSql string
	wrapperSql, err = b.toBaseOrAdvanceSceneSql(wrapperScene)
	if err != nil {
		return
	}
	var sceneImpl scene.ModelSceneStatement
	switch modelScene {
	case proto.DateCompletionRequired:
		sceneImpl = data_completion_required.NewDataCompletionRequiredImpl(b.buildContext, b.resourceType, wrapperSql)
	default:
		err = errors.New("invalid wrap model scene")
	}
	sql, err = sceneImpl.GenSQL()
	return
}

func (b *Builder) genDetailSQL() (sql string, err error) {
	// 1、先调用明细构建上下文的加载函数获取基础元数据
	if err = b.detailBuildContext.Load(); err != nil {
		return
	}
	sql, err = detail_query.NewGenerator(b.detailBuildContext, b.resourceType).ToSQL()
	return
}
