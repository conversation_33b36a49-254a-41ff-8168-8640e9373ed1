package dimension

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type Factory struct {
	defaultDimension *DefaultDimension
}

func NewFactory() *Factory {
	return &Factory{
		defaultDimension: NewDefaultDimension(),
	}
}

type DynamicParam struct {
	Type  proto.DynamicParamType
	Value any
}

func (s *Factory) GetDimension(dim *proto.Field, resourceType entities.ProjectResourceType,
	params ...DynamicParam) AdsViewDimension {
	var dimension AdsViewDimension
	if dim.DimDefinition == nil {
		dimension = s.defaultDimension
		return dimension
	}
	switch dim.DimDefinition.GetValueType {
	case proto.FieldAssign:
		dimension = NewFieldAssign(dim, resourceType)
	case proto.CondTag:
		dimension = NewCondTag(dim, resourceType)
	case proto.BuiltinFunction:
		dimension = NewBuiltinFunc(dim, resourceType)
	case proto.AdvanceDatetime:
		var options = applyDynamicParamsToAdvanceDatetimeOption(params...)
		dimension = NewAdvanceDatetime(dim, resourceType, options...)
	default:
		dimension = s.defaultDimension
	}
	return dimension
}

func applyDynamicParamsToAdvanceDatetimeOption(params ...DynamicParam) []AdvanceDatetimeOption {
	var ans = make([]AdvanceDatetimeOption, 0)
	for _, param := range params {
		switch param.Type {
		case proto.WithGraph:
			graph, ok := param.Value.(*common.ModelRelation)
			if ok {
				ans = append(ans, WithGraph(graph))
			}
		case proto.WithIndicatorDefinition:
			set, ok := param.Value.([]*proto.Definition)
			if ok {
				ans = append(ans, WithIndicatorDefinition(set))
			}
		}
	}
	return ans
}

type DefaultDimension struct {
}

func NewDefaultDimension() *DefaultDimension {
	return &DefaultDimension{}
}

func (s *DefaultDimension) GenViewExpr(ctx context.Context) (string, error) {
	return "", fmt.Errorf("创建维度表达式生成器时类型不支持，执行表达式生成无效")
}

func (s *DefaultDimension) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	return []proto.ModelField{}, nil
}

func (s *DefaultDimension) IsAnalytic(ctx context.Context, analyticModel map[string][]proto.ModelField) (bool, error) {
	return false, nil
}

func (s *DefaultDimension) IsComplete(ctx context.Context) (bool, error) {
	return false, nil
}
