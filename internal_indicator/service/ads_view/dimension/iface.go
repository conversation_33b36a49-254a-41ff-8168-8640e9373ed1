package dimension

import (
	"context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

type AdsViewDimension interface {
	GenViewExpr(ctx context.Context) (string, error)
	GetRefFields(ctx context.Context) ([]proto.ModelField, error)
	IsAnalytic(ctx context.Context, analyticModel map[string][]proto.ModelField) (bool, error)
	IsComplete(ctx context.Context) (bool, error)
}
