package dimension

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/dmp/gopkg/build_plan"
)

type BuiltinFunc struct {
	DefaultDimension
	definition   *proto.Field
	data         *proto.BuiltinFuncData
	resourceType entities.ProjectResourceType
}

func NewBuiltinFunc(definition *proto.Field, resourceType entities.ProjectResourceType) *BuiltinFunc {
	return &BuiltinFunc{
		definition:   definition,
		data:         &definition.DimDefinition.BuiltinFuncData,
		resourceType: resourceType,
	}
}

func (s *BuiltinFunc) GenViewExpr(ctx context.Context) (string, error) {
	if s.data == nil {
		return "", errors.New("维度数据为空")
	}
	builder, err := common.GetBuilder(s.resourceType)
	if err != nil {
		return "", err
	}
	var project = &build_plan.Project{
		Type: build_plan.ProjectType_Field,
		Field: &build_plan.FieldContent{
			Table:     s.data.QuoteTableName,
			FieldName: s.data.QuoteFieldName,
		},
	}
	switch s.data.BuiltinFunc {
	case proto.TimeDiffFromCurrent, proto.TimeDiffFromCurrentFiledSubTodayWithToday:
		project = genDateFieldSubToday(s.data.QuoteTableName, s.data.QuoteFieldName)
	case proto.TimeDiffFromCurrentTodaySubField, proto.TimeDiffFromCurrentTodaySubFieldWithToday:
		project = genTodaySubDateField(s.data.QuoteTableName, s.data.QuoteFieldName)
	case proto.ExtractYearMonth:
		project.Field.FieldTimeFormat = build_plan.TimeFieldStatType_ExtractMonth
	case proto.ExtractYear:
		project.Field.FieldTimeFormat = build_plan.TimeFieldStatType_ExtractYear
	case proto.ExtractDay:
		project.Field.FieldTimeFormat = build_plan.TimeFieldStatType_ExtractDay
	}
	var projects = make([]*build_plan.Project, 0)
	projects = append(projects, project)
	var expr string
	expr, err = builder.GetProjectExpress(ctx, projects)
	if err != nil {
		return "", err
	}
	// 日期差值函数需要加1
	if s.data.BuiltinFunc == proto.TimeDiffFromCurrentFiledSubTodayWithToday ||
		s.data.BuiltinFunc == proto.TimeDiffFromCurrentTodaySubFieldWithToday {
		expr = fmt.Sprintf(" (%s + 1)", expr)
	}
	return expr, nil
}

func (s *BuiltinFunc) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	if s.data == nil {
		return nil, errors.New("维度数据为空")
	}
	var data = s.data
	var res = []proto.ModelField{
		{
			Table: data.QuoteTableName,
			Field: data.QuoteFieldName,
		},
	}
	return res, nil
}

func (s *BuiltinFunc) IsAnalytic(ctx context.Context, analyticModel map[string][]proto.ModelField) (bool, error) {
	_, ok := analyticModel[s.data.QuoteTableName]
	if !ok {
		return false, nil
	}
	return true, nil
}

func (s *BuiltinFunc) IsComplete(ctx context.Context) (bool, error) {
	if s.data == nil {
		return false, errors.New("维度数据为空")
	}
	switch s.data.BuiltinFunc {
	case proto.ExtractYearMonth, proto.ExtractYear, proto.ExtractDay:
		return s.data.IsAutoCompletionDate == 1, nil
	default:
		return false, nil
	}
}

func genDateFieldSubToday(table, field string) *build_plan.Project {
	var ans = &build_plan.Project{
		Type: build_plan.ProjectType_Function,
		Func: &build_plan.FuncExpressContent{
			Type: build_plan.FieldFuncType_DateDiff,
			Params: []*build_plan.Project{
				{
					Type: build_plan.ProjectType_Value,
					Value: &build_plan.ValueContent{
						Type:        build_plan.ValueType_CustomSymbol,
						SymbolValue: build_plan.CustomSymbolCategory_DayUnit,
					},
				},
				{
					Type: build_plan.ProjectType_Field,
					Field: &build_plan.FieldContent{
						Table:     table,
						FieldName: field,
					},
				},
				{
					Type: build_plan.ProjectType_Value,
					Value: &build_plan.ValueContent{
						Type:        build_plan.ValueType_CustomSymbol,
						SymbolValue: build_plan.CustomSymbolCategory_CurrentDate,
					},
				},
			},
		},
	}
	return ans
}

func genTodaySubDateField(table, field string) *build_plan.Project {
	var ans = &build_plan.Project{
		Type: build_plan.ProjectType_Function,
		Func: &build_plan.FuncExpressContent{
			Type: build_plan.FieldFuncType_DateDiff,
			Params: []*build_plan.Project{
				{
					Type: build_plan.ProjectType_Value,
					Value: &build_plan.ValueContent{
						Type:        build_plan.ValueType_CustomSymbol,
						SymbolValue: build_plan.CustomSymbolCategory_DayUnit,
					},
				},
				{
					Type: build_plan.ProjectType_Value,
					Value: &build_plan.ValueContent{
						Type:        build_plan.ValueType_CustomSymbol,
						SymbolValue: build_plan.CustomSymbolCategory_CurrentDate,
					},
				},
				{
					Type: build_plan.ProjectType_Field,
					Field: &build_plan.FieldContent{
						Table:     table,
						FieldName: field,
					},
				},
			},
		},
	}
	return ans
}
