package dimension

import (
	"context"
	"fmt"
	"github.com/emirpasic/gods/maps/linkedhashmap"
	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

const AdvanceDatetimeSubqueryTableName = "adt__"

var _ AdsViewDimension = (*AdvanceDatetime)(nil)

type AdvanceDatetime struct {
	DefaultDimension
	definition   *proto.Field
	dimName      string
	data         *proto.AdvanceDatetimeData
	resourceType entities.ProjectResourceType

	graph       *common.ModelRelation
	definitions []*proto.Definition
}

type AdvanceDatetimeOption func(*AdvanceDatetime)

func WithGraph(graph *common.ModelRelation) AdvanceDatetimeOption {
	return func(s *AdvanceDatetime) {
		s.graph = graph
	}
}

func WithIndicatorDefinition(definitions []*proto.Definition) AdvanceDatetimeOption {
	return func(s *AdvanceDatetime) {
		s.definitions = definitions
	}
}

func (s *AdvanceDatetime) GenViewExpr(ctx context.Context) (string, error) {
	if s.data == nil {
		return "", errors.New("维度数据为空")
	}
	var expr = fmt.Sprintf("%s.%s", AdvanceDatetimeSubqueryTableName, s.dimName)
	return expr, nil
}

func (s *AdvanceDatetime) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	var ans = make([]proto.ModelField, 0)
	if s.data == nil {
		return ans, nil
	}
	switch s.data.AssignType {
	case proto.AssignByTable:
		for _, quoteField := range s.data.AssignByTableContent.QuoteFields {
			ans = append(ans, proto.ModelField{
				Table:     quoteField.QuoteTableName,
				Field:     quoteField.QuoteFieldName,
				FieldType: quoteField.QuoteFieldType,
				Id:        quoteField.QuoteFieldID,
			})
		}
	case proto.AssignByNum:
		for _, group := range s.data.AssignByNumContent.RelationNumGroups {
			ans = append(ans, proto.ModelField{
				Table:     group.QuoteField.QuoteTableName,
				Field:     group.QuoteField.QuoteFieldName,
				Id:        group.QuoteField.QuoteFieldID,
				FieldType: group.QuoteField.QuoteFieldType,
			})
		}
	}
	return ans, nil
}

func (s *AdvanceDatetime) IsAnalytic(ctx context.Context, analyticModel map[string][]proto.ModelField) (ok bool, err error) {
	// 先检查是否符合可分析模型
	ok, err = s.isAccordWithAnalyticModel(ctx, analyticModel)
	if err != nil {
		return
	}
	if !ok {
		return
	}
	// 再检查是否符合规则
	ok, err = s.isAccordWithRule(ctx)
	return
}

func (s *AdvanceDatetime) IsComplete(ctx context.Context) (bool, error) {
	if s.data == nil {
		return false, errors.New("维度数据为空")
	}
	return s.data.Expand.IsAutoCompletionDate == 1, nil
}

// isAccordWithAnalyticModel 判断是否符合可分析模型
func (s *AdvanceDatetime) isAccordWithAnalyticModel(ctx context.Context, analyticModel map[string][]proto.ModelField) (bool, error) {
	// 如果维度定义是高阶时间字段,则查看其最近公共祖先是否在可分析模型中
	fields, err := s.GetRefFields(ctx)
	if err != nil {
		return false, err
	}
	// 如果没有关联模型，默认是单表，可以分析
	if s.graph == nil || len(s.graph.Nodes) == 1 {
		return true, nil
	}
	var parentTableMap = linkedhashmap.New()
	for _, field := range fields {
		parentTableMap.Put(field.Table, hashset.New())
	}
	err = s.graph.BFS(func(node *proto.ModelNode, edg *proto.ModelEdg) error {
		if edg == nil {
			return nil
		}
		parents, found := parentTableMap.Get(edg.RelationNode.Table)
		if found {
			parents.(*hashset.Set).Add(node.Table)
		}
		return nil
	})
	if err != nil {
		return false, err
	}
	// 通过查看各个表之间是否有交集并且是否在可分析模型中
	var intersection = hashset.New()
	if parentTableMap.Size() > 0 {
		for i, parents := range parentTableMap.Values() {
			if i == 0 {
				intersection = parents.(*hashset.Set)
			} else {
				intersection = intersection.Intersection(parents.(*hashset.Set))
			}
		}
	}
	var ans = false
	for k := range analyticModel {
		if intersection.Contains(k) {
			ans = true
			break
		}
	}
	return ans, nil
}

func (s *AdvanceDatetime) isAccordWithRule(ctx context.Context) (bool, error) {
	// 遍历度量，填充引用的表集合、度量code集合
	var refTables = hashset.New()
	var refIndicatorCodes = hashset.New()
	for _, definition := range s.definitions {
		for _, refField := range definition.ReferenceFields {
			refTables.Add(refField.Table)
		}
		refIndicatorCodes.Add(definition.OriginPropMeta.FieldCode)
	}
	var ok = false
	// 按来源表: 高阶时间字段维度中引用的表必须和度量中引用的表一致
	if s.data.AssignType == proto.AssignByTable {
		var dimRefFields = hashset.New()
		for _, quoteField := range s.data.AssignByTableContent.QuoteFields {
			dimRefFields.Add(quoteField.QuoteTableName)
		}
		// 如果所有度量引用表在高阶维度中能找到那么就认为可分析，否则不可分析
		if refTables.Difference(dimRefFields).Empty() {
			ok = true
		}
		return ok, nil
	}
	// 按度量赋值: 高阶时间字段维度中引用的度量code必须和度量中引用的度量code一致
	if s.data.AssignType == proto.AssignByNum {
		var dimRefIndicatorCodes = hashset.New()
		for _, group := range s.data.AssignByNumContent.RelationNumGroups {
			for _, indicatorCode := range group.RelationIndicatorCodes {
				dimRefIndicatorCodes.Add(indicatorCode)
			}
		}
		// 如果所有度量code都能在高阶维度中找到，那么就认为可分析，否则不可分析
		if refIndicatorCodes.Difference(dimRefIndicatorCodes).Empty() {
			ok = true
		}
		return ok, nil
	}
	return ok, nil
}

func NewAdvanceDatetime(definition *proto.Field, resourceType entities.ProjectResourceType,
	options ...AdvanceDatetimeOption) *AdvanceDatetime {
	var ans = &AdvanceDatetime{
		definition:   definition,
		dimName:      definition.Field,
		data:         &definition.DimDefinition.AdvanceDatetimeData,
		resourceType: resourceType,
	}
	for _, option := range options {
		option(ans)
	}
	return ans
}
