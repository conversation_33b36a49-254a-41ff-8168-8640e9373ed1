package dimension

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/dmp/gopkg/build_plan"
	"strconv"
	"time"
)

type CondTag struct {
	DefaultDimension
	definition   *proto.Field
	data         *proto.CondTagData
	resourceType entities.ProjectResourceType
}

func NewCondTag(definition *proto.Field, resourceType entities.ProjectResourceType) *CondTag {
	return &CondTag{
		definition:   definition,
		data:         &definition.DimDefinition.CondTagData,
		resourceType: resourceType,
	}
}

type handleCtx struct {
	table        string
	field        string
	resourceType entities.ProjectResourceType
	category     proto.CondTagFieldType
	groups       []proto.Group
}

func (s *CondTag) GenViewExpr(ctx context.Context) (string, error) {
	if s.data == nil {
		return "", errors.New("维度数据为空")
	}
	var data = s.data
	var condTagType = ClassifyFieldType(proto.FieldType(data.QuoteFieldType))
	var params = &handleCtx{
		table:        data.QuoteTableName,
		field:        data.QuoteFieldName,
		resourceType: s.resourceType,
		category:     condTagType,
		groups:       data.GroupList,
	}
	return handleDataGroup(ctx, params)
}

func (s *CondTag) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	if s.data == nil {
		return nil, errors.New("维度数据为空")
	}
	var data = s.data
	var res = []proto.ModelField{
		{
			Table: data.QuoteTableName,
			Field: data.QuoteFieldName,
		},
	}
	return res, nil
}

func (s *CondTag) IsAnalytic(ctx context.Context, analyticModel map[string][]proto.ModelField) (bool, error) {
	_, ok := analyticModel[s.data.QuoteTableName]
	if !ok {
		return false, nil
	}
	return true, nil
}

func ClassifyFieldType(fieldType proto.FieldType) proto.CondTagFieldType {
	var ans = proto.CondTagFieldTypeString
	switch fieldType {
	case proto.Date, proto.Timestamp, proto.DateTime:
		ans = proto.CondTagFieldTypeTime
	case proto.Bigint, proto.Decimal, proto.Double, proto.Integer, proto.Float, proto.Int:
		ans = proto.CondTagFieldTypeNumber
	}
	return ans
}

// 数据分组处理主逻辑: 处理为case when表达式
func handleDataGroup(ctx context.Context, param *handleCtx) (string, error) {
	if param == nil {
		return "", errors.New("")
	}
	builder, err := common.GetBuilder(param.resourceType)
	if err != nil {
		return "", err
	}
	var boolExpressList = make([]*build_plan.BoolExpress, 0)
	var defaultExpress = &build_plan.ValueContent{
		Type: build_plan.ValueType_NullValue,
	}
	for _, group := range param.groups {
		// 如果设置了默认组
		if group.GroupType == proto.GroupTypeDefault {
			defaultExpress = &build_plan.ValueContent{
				Type:  build_plan.ValueType_StringValue,
				Value: group.GroupName,
			}
			continue
		}
		conditionExpr, err := customGroupTransform(ctx, param, group)
		if err != nil {
			return "", err
		}
		boolExpressList = append(boolExpressList, &build_plan.BoolExpress{
			Condition: conditionExpr,
			TrueContent: &build_plan.ValueContent{
				Type:  build_plan.ValueType_StringValue,
				Value: group.GroupName,
			},
		})
	}
	// 如果只有默认值，则返回默认值
	if len(boolExpressList) < 1 {
		return builder.GetProjectExpress(ctx, []*build_plan.Project{{
			Type:  build_plan.ProjectType_Value,
			Value: defaultExpress,
		}})
	}
	// 在获取分组过滤表达式并组装为case when表达式
	caseWhenExpress := &build_plan.Project{
		Type: build_plan.ProjectType_Condition,
		Condition: &build_plan.ConditionContent{
			Type:           build_plan.ConditionType_CASE,
			DefaultContent: defaultExpress,
		},
	}
	caseWhenExpress.Condition.Expresses = append(caseWhenExpress.Condition.Expresses, boolExpressList...)
	return builder.GetProjectExpress(ctx, []*build_plan.Project{caseWhenExpress})
}

// 处理为条件表达式
func customGroupTransform(ctx context.Context, param *handleCtx, group proto.Group) (string, error) {
	if len(group.Where.Info) < 1 {
		return "", errors.New("数据分组条件传值为空")
	}
	builder, err := common.GetBuilder(param.resourceType)
	if err != nil {
		return "", err
	}
	// 执行函数获取表达式
	var filterList = make([]*build_plan.Filter, 0)
	for i, whereItem := range group.Where.Info {
		if whereItem.Operate == "" {
			return "", errors.New("条件操作符为空")
		}
		var err error
		var filter *build_plan.Filter
		switch param.category {
		case proto.CondTagFieldTypeString:
			filter, err = genStringFilter(param.table, param.field, &whereItem)
		case proto.CondTagFieldTypeNumber:
			filter, err = genNumberFilter(param.table, param.field, &whereItem)
		case proto.CondTagFieldTypeTime:
			filter, err = genTimeFilter(param.table, param.field, &whereItem)
		}
		if err != nil {
			return "", err
		}
		if i != 0 {
			filter.AssociatePrev = common.ConvertAssociate(group.Where.Relation)
		}
		filterList = append(filterList, filter)
	}
	condition, err := builder.GetFilterExpress(ctx, filterList)
	if err != nil {
		return "", err
	}
	return condition, nil
}

func genStringFilter(table, field string, where *proto.WhereInfo) (*build_plan.Filter, error) {
	if where.Operate != proto.Contain && where.Operate != proto.NotContain &&
		where.Operate != proto.Equal && where.Operate != proto.NotEqual {
		return nil, fmt.Errorf("文本型字段不支持 %s 操作符", where.Operate)
	}
	var filter = &build_plan.Filter{
		Field: &build_plan.Project{
			Type: build_plan.ProjectType_Field,
			Field: &build_plan.FieldContent{
				Table:     table,
				FieldName: field,
			},
		},
		Operate: common.ConvertOperate(where.Operate),
		ValueContent: &build_plan.ValueContent{
			Type:  build_plan.ValueType_StringValue,
			Value: where.FixedVal,
		},
	}
	return filter, nil
}

func genNumberFilter(table, field string, where *proto.WhereInfo) (*build_plan.Filter, error) {
	if where.Operate != proto.Gt && where.Operate != proto.Gte &&
		where.Operate != proto.Lt && where.Operate != proto.Lte &&
		where.Operate != proto.Equal && where.Operate != proto.NotEqual {
		return nil, fmt.Errorf("数值型字段不支持 %s 操作符", where.Operate)
	}
	_, err := strconv.ParseFloat(where.FixedVal, 64)
	if err != nil {
		return nil, fmt.Errorf("数值型字段的条件表达式传了一个非数值: %s", where.FixedVal)
	}
	var filter = &build_plan.Filter{
		Field: &build_plan.Project{
			Type: build_plan.ProjectType_Field,
			Field: &build_plan.FieldContent{
				Table:     table,
				FieldName: field,
			},
		},
		Operate: common.ConvertOperate(where.Operate),
		ValueContent: &build_plan.ValueContent{
			Type:  build_plan.ValueType_FloatValue,
			Value: where.FixedVal,
		},
	}
	return filter, nil
}

func genTimeFilter(table, field string, where *proto.WhereInfo) (*build_plan.Filter, error) {
	if where == nil {
		return nil, errors.New("参数传值为空")
	}
	switch where.Operate {
	case proto.Between:
		return genBetweenTimeFilter(table, field, where)
	case proto.DynamicTime:
		return genDynamicTimeFilter(table, field, where)
	default:
		return genDefaultTimeFilter(table, field, where)
	}
}

func genDefaultTimeFilter(table, field string, where *proto.WhereInfo) (*build_plan.Filter, error) {
	if where.Operate != proto.Gt && where.Operate != proto.Gte &&
		where.Operate != proto.Lt && where.Operate != proto.Lte &&
		where.Operate != proto.Equal && where.Operate != proto.NotEqual {
		return nil, fmt.Errorf("时间型字段不支持 %s 操作符", where.Operate)
	}
	if where.ValType != proto.ValueDataTypeFixedTime && where.ValType != proto.ValueDataTypeOtherTimeField {
		return nil, fmt.Errorf("时间型字段的条件表达式不支持 %s 类型的值", where.ValType)
	}
	var valContent = &build_plan.ValueContent{}
	if where.ValType == proto.ValueDataTypeFixedTime {
		sec, err := strconv.ParseInt(where.FixedVal, 10, 64)
		if err != nil {
			return nil, err
		}
		valContent.Type = build_plan.ValueType_StringValue
		valContent.Value = time.Unix(sec, 0).Format("2006-01-02 15:04:05")
	} else if where.ValType == proto.ValueDataTypeOtherTimeField {
		valContent.Type = build_plan.ValueType_ExpressValue
		valContent.Value = fmt.Sprintf("%s.%s", table, where.Field)
	}
	var filter = &build_plan.Filter{
		Field: &build_plan.Project{
			Type: build_plan.ProjectType_Field,
			Field: &build_plan.FieldContent{
				Table:     table,
				FieldName: field,
			},
		},
		Operate:      common.ConvertOperate(where.Operate),
		ValueContent: valContent,
	}
	return filter, nil
}

func genBetweenTimeFilter(table, field string, where *proto.WhereInfo) (*build_plan.Filter, error) {
	var subFilterList = make([]*build_plan.Filter, 0)
	leftSec, err := strconv.ParseInt(where.RangeLeftVal, 10, 64)
	if err != nil {
		return nil, err
	}
	var left = &build_plan.Filter{
		Field: &build_plan.Project{
			Type: build_plan.ProjectType_Field,
			Field: &build_plan.FieldContent{
				Table:     table,
				FieldName: field,
			},
		},
		Operate: common.ConvertOperate(proto.Gte),
		ValueContent: &build_plan.ValueContent{
			Type:  build_plan.ValueType_StringValue,
			Value: time.Unix(leftSec, 0).Format("2006-01-02 15:04:05"),
		},
	}
	rightSec, err := strconv.ParseInt(where.RangeRightVal, 10, 64)
	if err != nil {
		return nil, err
	}
	var right = &build_plan.Filter{
		Field: &build_plan.Project{
			Type: build_plan.ProjectType_Field,
			Field: &build_plan.FieldContent{
				Table:     table,
				FieldName: field,
			},
		},
		Operate: common.ConvertOperate(proto.Lte),
		ValueContent: &build_plan.ValueContent{
			Type:  build_plan.ValueType_StringValue,
			Value: time.Unix(rightSec, 0).Format("2006-01-02 15:04:05"),
		},
		AssociatePrev: build_plan.AssociateType_AND,
	}
	subFilterList = append(subFilterList, left, right)

	var filter = &build_plan.Filter{
		SubFilter: subFilterList,
	}
	return filter, nil
}

func genDynamicTimeFilter(table, field string, where *proto.WhereInfo) (*build_plan.Filter, error) {
	left, err := genDateProject(table, field, where.ValType)
	if err != nil {
		return nil, err
	}
	var operate = build_plan.OperateType_Eq
	if where.TimeIntervalVal.Operate != "" {
		operate = common.ConvertOperate(where.TimeIntervalVal.Operate)
	}
	right, err := genDateValue(where.ValType, where.TimeIntervalVal.Val)
	if err != nil {
		return nil, err
	}
	var filter = &build_plan.Filter{
		Field:        left,
		Operate:      operate,
		ValueContent: right,
	}
	return filter, nil
}

func genDateProject(table, field string, valType proto.ValueDataType) (*build_plan.Project, error) {
	var ans = &build_plan.Project{
		Type: build_plan.ProjectType_Field,
		Field: &build_plan.FieldContent{
			Table:     table,
			FieldName: field,
		},
	}
	switch valType {
	case proto.ValueDataTypeYear:
		ans.Field.FieldTimeFormat = build_plan.TimeFieldStatType_ExtractYear
	case proto.ValueDataTypeMonth:
		ans.Field.FieldTimeFormat = build_plan.TimeFieldStatType_ExtractMonth
	case proto.ValueDataTypeWeek:
		ans.Field.FieldTimeFormat = build_plan.TimeFieldStatType_ExtractWeek
	case proto.ValueDataTypeBeforeToday, proto.ValueDataTypeAfterToday:
		ans.Field.FieldTimeFormat = build_plan.TimeFieldStatType_ExtractDay
		ans.Field.TimeStatDiffType = build_plan.TimeFieldStatType_ExtractDay
	}
	return ans, nil
}

func genDateValue(valType proto.ValueDataType, val string) (*build_plan.ValueContent, error) {
	var ans = &build_plan.ValueContent{}
	switch valType {
	case proto.ValueDataTypeYear:
		ans.Type = build_plan.ValueType_CustomSymbol
		ans.SymbolValue = build_plan.CustomSymbolCategory_CurrentDate
		ans.TimeFormat = &build_plan.TimeFormat{
			TimeFormat: build_plan.TimeFieldStatType_ExtractYear,
		}
	case proto.ValueDataTypeMonth:
		ans.Type = build_plan.ValueType_CustomSymbol
		ans.SymbolValue = build_plan.CustomSymbolCategory_CurrentDate
		ans.TimeFormat = &build_plan.TimeFormat{
			TimeFormat: build_plan.TimeFieldStatType_ExtractMonth,
		}
	case proto.ValueDataTypeWeek:
		ans.Type = build_plan.ValueType_CustomSymbol
		ans.SymbolValue = build_plan.CustomSymbolCategory_CurrentDate
		ans.TimeFormat = &build_plan.TimeFormat{
			TimeFormat: build_plan.TimeFieldStatType_ExtractWeek,
		}
	case proto.ValueDataTypeBeforeToday:
		days, err := strconv.ParseInt(val, 10, 64)
		if err != nil {
			return nil, err
		}
		ans.Type = build_plan.ValueType_CustomSymbol
		ans.SymbolValue = build_plan.CustomSymbolCategory_CurrentDate
		ans.TimeFormat = &build_plan.TimeFormat{
			TimeFormat:    build_plan.TimeFieldStatType_ExtractDay,
			TimeDiffType:  build_plan.TimeFieldStatType_Day,
			TimeDiffValue: -1 * days,
		}
	case proto.ValueDataTypeAfterToday:
		days, err := strconv.ParseInt(val, 10, 64)
		if err != nil {
			return nil, err
		}
		ans.Type = build_plan.ValueType_CustomSymbol
		ans.SymbolValue = build_plan.CustomSymbolCategory_CurrentDate
		ans.TimeFormat = &build_plan.TimeFormat{
			TimeFormat:    build_plan.TimeFieldStatType_ExtractDay,
			TimeDiffType:  build_plan.TimeFieldStatType_Day,
			TimeDiffValue: days,
		}
	}
	return ans, nil
}
