package dimension

import (
	"context"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/dmp/gopkg/build_plan"
)

type FieldAssign struct {
	DefaultDimension
	definition   *proto.Field
	data         *proto.FieldAssignData
	resourceType entities.ProjectResourceType
}

func NewFieldAssign(definition *proto.Field, resourceType entities.ProjectResourceType) *FieldAssign {
	return &FieldAssign{
		definition:   definition,
		data:         &definition.DimDefinition.FieldAssignData,
		resourceType: resourceType,
	}
}

func (s *FieldAssign) GenViewExpr(ctx context.Context) (string, error) {
	if s.data == nil {
		return "", errors.New("维度数据为空")
	}
	var field proto.Field
	var err = copier.Copy(&field, s.definition)
	if err != nil {
		return "", err
	}
	field.Table = s.data.QuoteTableName
	field.Field = s.data.QuoteFieldName
	var exprPlan *build_plan.Project
	var alias = field.Field
	if field.Alias != "" {
		alias = field.Alias
	}
	exprPlan = &build_plan.Project{
		Type: build_plan.ProjectType_Field,
		Field: &build_plan.FieldContent{
			Schema:     field.Schema,
			Table:      field.Table,
			FieldName:  field.Field,
			FieldAlias: alias,
		},
	}
	if exprPlan.Field != nil {
		exprPlan.Field.FieldAlias = ""
	}
	if exprPlan.Value != nil {
		exprPlan.Value.Alias = ""
	}
	if exprPlan.RawContent != nil {
		exprPlan.RawContent.Alias = ""
	}
	var builder build_plan.Builder
	builder, err = common.GetBuilder(s.resourceType)
	if err != nil {
		return "", err
	}
	return builder.GetProjectExpress(ctx, []*build_plan.Project{exprPlan})
}

func (s *FieldAssign) GetRefFields(ctx context.Context) ([]proto.ModelField, error) {
	if s.data == nil {
		return nil, errors.New("维度数据为空")
	}
	var data = s.data
	var res = []proto.ModelField{
		{
			Table: data.QuoteTableName,
			Field: data.QuoteFieldName,
		},
	}
	return res, nil
}

func (s *FieldAssign) IsAnalytic(ctx context.Context, analyticModel map[string][]proto.ModelField) (bool, error) {
	_, ok := analyticModel[s.data.QuoteTableName]
	if !ok {
		return false, nil
	}
	return true, nil
}

func (s *FieldAssign) IsComplete(ctx context.Context) (bool, error) {
	if s.data == nil {
		return false, errors.New("维度数据为空")
	}
	return s.data.IsAutoCompletionDate == 1, nil
}
