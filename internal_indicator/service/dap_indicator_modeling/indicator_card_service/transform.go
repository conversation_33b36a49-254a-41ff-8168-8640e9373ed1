package indicator_card_service

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type Transformer struct {
	viewContent  *proto.ViewContentNew
	resourceType entities.ProjectResourceType
	*injection.DefaultDepModelImpl
	*injection.DefaultIndicatorFilterImpl
}

func NewTransformer(ctx context.Context, resourceType entities.ProjectResourceType, viewContent *proto.ViewContentNew) *Transformer {
	if viewContent.Dependence == nil {
		panic("依赖模型为空")
	}
	return &Transformer{
		viewContent:  viewContent,
		resourceType: resourceType,
	}
}

func (n *Transformer) PreCheckAndPrepareData(ctx context.Context, repo *indicator_repository.IndicatorRepository) (err error) {
	// 从数据库中取相关数据
	if n.viewContent.Dependence == nil {
		return errors.New("服务器内部错误, 应用表依赖模型不存在")
	}

	r := indicator_cache.NewIndicatorCache(repo.Project)
	n.DefaultDepModelImpl = injection.NewDefaultDepModelImpl(n.viewContent.Dependence, &n.viewContent.Dependence.RelationalModel, injection.WithLoadCache(r))
	n.DefaultIndicatorFilterImpl = injection.NewDefaultIndicatorFilterImpl(n.viewContent, nil)
	return n.DefaultDepModelImpl.PreLoad(ctx, repo, string(global.Prod))
}

// 获取指标定义
func (n *Transformer) GetDefinitions(ctx context.Context) ([]*proto.Definition, error) {
	helper := injection.NewConvertHelper(injection.WithUseCast(), injection.WithVarValues(&injection.VariableContext{
		n.viewContent.EmptyVarSelectAll,
		n.viewContent.Variables,
		nil,
	}))
	result := []*proto.Definition{}
	for idx := range n.viewContent.CustomFieldDimensions {
		indicator := &n.viewContent.CustomFieldDimensions[idx]
		definition, err := helper.IndicatorToDefinition(indicator.Alias, indicator, n.resourceType)
		if err != nil {
			return nil, err
		}
		result = append(result, definition)
	}
	global.Logger.JsonTrace("[AdsQuery] GetDefinitions Result: ", result)
	return result, nil
}

// 获取指标聚合
func (n *Transformer) GetAggregate(ctx context.Context) (*proto.Aggregate, error) {
	viewContent := n.viewContent
	result := &proto.Aggregate{
		Fields: make([]*proto.Field, 0),
		Having: make([]*proto.Filter, 0),
	}
	for _, groupByInfo := range viewContent.GroupByInfoList {
		field := proto.Field{}
		field.Field = groupByInfo.Field
		field.Table = groupByInfo.Table
		field.Alias = groupByInfo.Alias
		field.DimDefinition = groupByInfo.DimViewData
		field.TimeFormat.TimeFormatType = groupByInfo.TimeFormat.TimeFormatType
		result.Fields = append(result.Fields, &field)
	}

	for _, havingInfo := range viewContent.HavingInfoList {
		filter := &proto.Filter{}
		field := &proto.Field{}
		field.Table = havingInfo.Table
		field.Alias = havingInfo.Alias
		field.Field = havingInfo.Field
		filter.Field = field
		filter.Associate = havingInfo.Associate
		filter.Operate = havingInfo.Operate
		filter.Value = proto.ValueContent{
			Value: havingInfo.Val,
			Type:  havingInfo.ValueType,
		}
		result.Having = append(result.Having, filter)
	}
	return result, nil
}

func convertCustomizeWhereInfoToFilter(whereInfoList []*proto.CustomizeWhereInfo) []*proto.Filter {
	result := make([]*proto.Filter, 0)
	for index, whereInfo := range whereInfoList {
		filter := proto.Filter{}
		if index == 0 {
			filter.Associate = proto.NoAssociate
		} else {
			filter.Associate = whereInfo.Associate
		}
		if len(whereInfo.SubCustomizeWhereInfo) > 0 {
			filter.SubFilter = convertCustomizeWhereInfoToFilter(whereInfo.SubCustomizeWhereInfo)
			result = append(result, &filter)
			continue
		}
		filter.Field = &proto.Field{
			ModelField: proto.ModelField{
				Field: whereInfo.Field,
				Table: whereInfo.Table,
			},
			DimDefinition: whereInfo.DimViewData,
		}
		filter.Operate = whereInfo.Operate
		filter.Value.Value = whereInfo.Val
		if filter.Operate == proto.Like || filter.Operate == proto.NotLike {
			filter.Value.Value = fmt.Sprintf("%%%s%%", whereInfo.Val)
		}
		filter.Value.Type = whereInfo.ValueType

		filter.Field.TimeFormat.TimeFormatType = whereInfo.TimeFormat.TimeFormatType
		filter.Field.TimeFormat.Diff = whereInfo.TimeFormat.Diff
		result = append(result, &filter)
	}
	return result
}

// 获取指标过滤
func (n *Transformer) GetFilter(ctx context.Context) ([]*proto.Filter, error) {
	viewContent := n.viewContent
	result := make([]*proto.Filter, 0)
	associate := proto.NoAssociate
	if len(result) != 0 {
		associate = proto.AND
	}
	customFilter := convertCustomizeWhereInfoToFilter(viewContent.WhereInfoList)
	if len(customFilter) > 0 {
		result = append(result, &proto.Filter{
			Associate: associate,
			SubFilter: customFilter,
		})
	}
	return result, nil
}

// 获取排序
func (n *Transformer) GetSort(ctx context.Context) ([]*proto.Sort, error) {
	result := make([]*proto.Sort, 0)
	viewContent := n.viewContent
	for _, v := range viewContent.OrderInfoList {
		result = append(result, &proto.Sort{
			Field: &proto.Field{
				ModelField: proto.ModelField{
					Field: v.Field,
					Table: v.Table,
				},
				DimDefinition: v.DimViewData,
			},
			Direction: v.Direction,
		})
	}
	return result, nil
}

// 默认限制
const DEFAULT_LIMIT = 500

func (n *Transformer) GetLimit(ctx context.Context) (*proto.Limit, error) {
	result := &proto.Limit{}
	if n.viewContent.LimitInfo != nil && n.viewContent.LimitInfo.IsCalculateTotal { //计算总数的话去掉limit
		return result, nil
	}

	if n.viewContent.LimitInfo != nil {
		result.Offset = n.viewContent.LimitInfo.Offset
		result.Limit = n.viewContent.LimitInfo.Limit
	} else if global.AppConfig.DmpApi.MaxIndicatorCardLimit != 0 {
		result.Limit = global.AppConfig.DmpApi.MaxIndicatorCardLimit
	} else {
		result.Limit = DEFAULT_LIMIT
	}
	return result, nil
}
