package indicator_card_service

import (
	"context"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global/consts"
	"sync"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_query/service/bigdata_query_service/table_permission_service"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/errgroup"
	db_utils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/saas_db"
	"gitlab.mypaas.com.cn/fast/tracker-go/trace"

	"google.golang.org/grpc/metadata"

	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_card"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errcode"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/fast"
	pkgFast "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
)

type IndicatorCardService struct {
	permissionService table_permission_service.TablePermissionService
}

func NewIndicatorCardService() *IndicatorCardService {
	s := &IndicatorCardService{}
	global.Container.MustExtract(&s.permissionService)
	return s
}

func (i *IndicatorCardService) QueryIndicatorData(ctx context.Context, req *indicator_card.QueryIndicatorDataRequest) (*indicator_card.QueryIndicatorDataResponse, error) {
	result := new(indicator_card.QueryIndicatorDataResponse)
	result.RequestId = trace.GetTraceId(ctx)
	result.Data = &indicator_card.IndicatorQueryData{}
	result.Data.List = make([]*indicator_card.IndicatorResultInfo, 0)
	result.ErrCode = errcode.ErrorCodeSuccess
	result.ErrMsg = "成功"

	var err error
	if req.OrgCode != "" {
		dapCommonService := dap_common.NewDapCommonServiceRepoImpl()
		tenantProjectRelInfo, err := dapCommonService.GetTenantProjectRel(ctx, req.OrgCode)
		if err != nil {
			return result, err
		}
		if tenantProjectRelInfo.CustomProjectCode != "" {
			req.Project = tenantProjectRelInfo.CustomProjectCode
		} else if tenantProjectRelInfo.CommonProjectCode != "" {
			req.Project = tenantProjectRelInfo.CommonProjectCode
		}
	}

	event := fast.StartEvent(pkgFast.CallApi, req.Project, consts.ADMIN)
	fastRecordReq := &IndicatorDataBizParamsReq{
		Project:        req.Project,
		Tenant:         req.OrgCode,
		ReqParams:      req,
		RespParams:     result,
		ReqTime:        time.Now(),
		ExecuteSql:     []string{},
		SqlCostTimeMap: map[string]string{},
		Source:         req.Source,
	}
	event.RecordBizParams(GenerateQueryIndicatorDataBizParams(ctx, fastRecordReq))
	defer func() {
		if err != nil {
			fastRecordReq.Result = "调用失败"
		} else {
			fastRecordReq.Result = "调用成功"
		}
		event.EndEvent(ctx, err)
	}()

	//请求参数校验
	err = i.checkQueryIndicatorDataReq(req)
	if err != nil {
		return result, err
	}

	var wrapper *common.IndicatorRepoWrapper
	if wrapper, err = i.getIndicatorRepoWrapper(ctx, req.Project); err != nil {
		return result, err
	}

	//将project塞进上下文
	mdCopy := metadata.New(map[string]string{
		"project_code": req.Project,
	})
	ctx = metadata.NewOutgoingContext(ctx, mdCopy)

	//获取查询资源
	dapCommonService := dap_common.NewDapCommonServiceRepoImpl()
	pr, err := dapCommonService.GetStorageResourceV3(ctx, req.Project, req.OrgCode)
	if err != nil {
		return result, err
	}

	//获取sql构建和查询预备数据
	indicatorFieldInfoList, indicatorBuildSqlInfoMap, err := i.preCheckAndPrepareData(ctx, wrapper, req.Project, pr.ResourceType, req)
	if err != nil {
		return result, err
	}
	queryIndicatorResultMap := make(map[string]*QueryIndicatorResult, 0)
	for _, info := range indicatorFieldInfoList {
		fastRecordReq.IndicatorCodeList = append(fastRecordReq.IndicatorCodeList, info.Code)
		queryIndicatorResult := &QueryIndicatorResult{}
		queryIndicatorResult.IndicatorId = info.ID
		queryIndicatorResult.IndicatorName = info.NameCn
		queryIndicatorResult.IndicatorDescription = info.Description
		queryIndicatorResult.IndicatorUnit = info.IndicatorUnit
		queryIndicatorResultMap[info.ID] = queryIndicatorResult
	}

	fastRecordReq.SqlExecStartTime = time.Now()
	//生成sql并查询数据
	var mutex sync.Mutex
	eg := errgroup.WithContext(ctx)
	for _, indicatorBuildSqlInfo := range indicatorBuildSqlInfoMap {
		queryReq := &QueryReq{
			IndicatorBuildSqlInfo: indicatorBuildSqlInfo,
			CalculateCondition:    req.CalculateCondition,
			ChartType:             req.ChartType,
			Resource:              pr,
			UserCode:              req.UserCode,
		}
		eg.Go(func(ctx context.Context) error {
			qrList, err := i.getQueryResult(ctx, fastRecordReq, queryReq)

			mutex.Lock()
			for _, qr := range qrList {
				if qr == nil {
					continue
				}

				rt := &indicator_card.IndicatorResultInfo{
					IndicatorId:                qr.IndicatorId,
					IndicatorCode:              qr.IndicatorCode,
					IndicatorName:              queryIndicatorResultMap[qr.IndicatorId].IndicatorName,
					IndicatorDescription:       queryIndicatorResultMap[qr.IndicatorId].IndicatorDescription,
					IndicatorVal:               qr.IndicatorVal,
					IndicatorsTrend:            qr.IndicatorsTrend,
					IndicatorsAggregations:     qr.IndicatorsAggregations,
					IndicatorsAggregationTotal: qr.IndicatorsAggregationTotal,
					IndicatorUnit:              queryIndicatorResultMap[qr.IndicatorId].IndicatorUnit,
					CalculateConditionCode:     qr.CalculateConditionCode,
					IndicatorQuerySql:          lo.Ternary(global.AppConfig.DmpApi.ShadowSql, "", qr.Sql),
				}
				result.Data.List = append(result.Data.List, rt)
			}
			mutex.Unlock()
			return err
		})
	}

	if err = eg.Wait(); err != nil {
		fastRecordReq.SqlExecEndTime = time.Now()
		return result, err
	}
	fastRecordReq.SqlExecEndTime = time.Now()

	return result, nil
}

func (i *IndicatorCardService) getIndicatorRepoWrapper(ctx context.Context, projectCode string) (
	*common.IndicatorRepoWrapper, error) {
	db, err := db_utils.GetProjectDB(ctx, projectCode)
	if err != nil {
		return nil, err
	}
	repo := indicator_repository.NewIndicatorRepository(db, projectCode)
	cache := indicator_cache.NewIndicatorCache(projectCode)
	return common.NewIndicatorRepoWrapper(repo, cache), nil
}
