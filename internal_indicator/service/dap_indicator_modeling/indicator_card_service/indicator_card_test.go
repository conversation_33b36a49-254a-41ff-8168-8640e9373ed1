package indicator_card_service

import (
	"context"
	"github.com/golang/protobuf/jsonpb"
	"github.com/stretchr/testify/assert"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_card"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/test_helper"
	"io/ioutil"
	"testing"
)

const path = "./fixtures/"

// 调试使用，依赖项:
// 连接dap_common公共服务
func TestIndicatorCardQueryData(t *testing.T) {

	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	t.Run("调试使用", func(t *testing.T) {
		var req = &indicator_card.QueryIndicatorDataRequest{}
		bs, err := ioutil.ReadFile(path + "debug.json")
		at.Nil(err)
		err = jsonpb.UnmarshalString(string(bs), req)
		at.Nil(err)

		service := NewIndicatorCardService()
		rsp, err := service.QueryIndicatorData(ctx, req)
		at.Nil(err)
		t.Logf("取数结果: %+v", rsp)
	})

	t.Run("冒烟", func(t *testing.T) {
		var req = &indicator_card.QueryIndicatorDataRequest{}

		smokeList := []string{
			"smoke1.json",
		}
		for _, smokeFileName := range smokeList {
			bs, err := ioutil.ReadFile(path + smokeFileName)
			at.Nil(err)
			err = jsonpb.UnmarshalString(string(bs), req)
			at.Nil(err)

			service := NewIndicatorCardService()
			rsp, err := service.QueryIndicatorData(ctx, req)
			at.Nil(err)
			t.Logf("取数结果: %+v", rsp)
		}
	})
}