package indicator_card_service

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_card"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type QueryIndicatorResult struct {
	indicator_card.IndicatorResultInfo
	Sql string
	Err error
}

type QueryReq struct {
	IndicatorBuildSqlInfo *common.IndicatorBuildSqlInfo
	CalculateCondition    *indicator_card.IndicatorCalculateCondition
	ChartType             int32
	EngineType            entities.ProjectResourceType
	TenantStandardProject string
	ProjectCode           string
	TablePermission       map[string]string
	NeedRowFilter         bool //是否需要行级权限过滤
	TenantCode            string
	UserCode              string
	UserID                string
	Resource              *proto.ResourceInfo
}
