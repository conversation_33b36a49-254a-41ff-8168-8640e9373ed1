package indicator_card_service

import (
	"github.com/pkg/errors"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_card"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
)

func (i *IndicatorCardService) checkQueryIndicatorDataReq(req *indicator_card.QueryIndicatorDataRequest) error {
	if req.Project == "" || len(req.IndicatorInfo) == 0 {
		return errors.New("参数错误,请检查project、indicator_info是否为空")
	}

	if req.ChartType != common.ChartTypeIndicatorBatch && req.CalculateCondition == nil {
		return errors.New("参数错误,指标计算条件为空")
	}

	if req.ChartType == common.ChartTypeIndicatorRank {
		if req.CalculateCondition.IndicatorOrder == nil {
			return errors.New("参数错误,指标排行参数为空")
		}
		if req.CalculateCondition.IndicatorOrder.Num == 0 {
			return errors.New("参数错误,指标排行参数有误,num为0")
		}
		if req.CalculateCondition.IndicatorOrder.Type == "" ||
			(req.CalculateCondition.IndicatorOrder.Type != "desc" && req.CalculateCondition.IndicatorOrder.Type != "asc") {
			return errors.New("参数错误,指标排行参数有误,type错误")
		}
	}
	return nil
}
