package indicator_card_service

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/fast/tracker-go/trace"
	"strings"
	"sync"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/base"
	fastPkg "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
)

type IndicatorDataBizParamsReq struct {
	Project           string
	Tenant            string
	ReqParams         interface{}
	RespParams        interface{}
	ReqTime           time.Time
	ExecuteSql        []string
	SqlExecStartTime  time.Time
	SqlExecEndTime    time.Time
	Result            string
	SqlCostTimeMap    map[string]string
	Mutex             sync.Mutex
	IndicatorCodeList []string
	Source            int32
}

func GenerateQueryIndicatorDataBizParams(ctx context.Context, req *IndicatorDataBizParamsReq) base.BizParamsGenerator {
	return func() map[string]interface{} {
		res := fastPkg.ApiCommonBizParam{
			TraceID:        trace.GetTraceId(ctx),
			TenantCode:     req.Tenant,
			ApiName:        "指标取数",
			ApiType:        fastPkg.IndicatorCardApi,
			RequestMethod:  "POST",
			LogTime:        time.Now().Format("2006-01-02 15:04:05.000000"),
			RequestPath:    "/openapi/indicator/indicator_card/query_indicator_data",
			RequestParams:  req.ReqParams,
			RequestTime:    req.ReqTime.Format("2006-01-02 15:04:05.000000"),
			ResponseTime:   time.Now().Format("2006-01-02 15:04:05.000000"),
			CostTime:       time.Now().Sub(req.ReqTime).Milliseconds(),
			ExecuteSql:     strings.Join(req.ExecuteSql, ","),
			ExecuteSqlTime: req.SqlExecEndTime.Sub(req.SqlExecStartTime).Milliseconds(),
			Extra: map[string]interface{}{
				"sql_cost_time":          req.SqlCostTimeMap,
				"request_indicator_code": strings.Join(req.IndicatorCodeList, ","),
				"request_indicator_num":  len(req.IndicatorCodeList),
			},
		}
		return res.ToFlatMap()
	}
}

func (fastReq *IndicatorDataBizParamsReq) recordSqlExecTime(sql string, duration time.Duration) {
	fastReq.Mutex.Lock()
	if duration == 0 {
		fastReq.SqlCostTimeMap[fmt.Sprintf("%s---%d", sql, time.Now().UnixNano())] = fmt.Sprintf("%dms", 0)
	} else {
		fastReq.SqlCostTimeMap[fmt.Sprintf("%s---%d", sql, time.Now().UnixNano())] = fmt.Sprintf("%dms", duration.Milliseconds())
	}
	fastReq.Mutex.Unlock()
}
