package indicator_card_service

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_card"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view"
	context2 "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/engine"
	"gitlab.mypaas.com.cn/dmp/gopkg/build_plan"
)

func (i *IndicatorCardService) TransIndicatorSql(ctx context.Context, info *common.IndicatorBuildSqlInfo, calculateCondition *indicator_card.IndicatorCalculateCondition) (string, error) {
	viewContentNew, err := common.GetViewContentNew(info.ViewContent)
	if err != nil {
		return "", err
	}

	//填充指标定义
	customFieldDimensions := make([]rpc_call.Prop, 0)
	customFieldDimensions = append(customFieldDimensions, common.GetCustomFieldDimension(info.IndicatorFieldCode, info.IndicatorFieldAlias, viewContentNew.CustomFieldDimensions))
	viewContentNew.CustomFieldDimensions = customFieldDimensions

	//填充指标过滤-普通条件过滤
	if calculateCondition.Where != nil {
		for index, v := range calculateCondition.Where.Info {
			relation := strings.ToLower(calculateCondition.Where.Relation)
			associatePrev := proto.NoAssociate
			if index != 0 {
				associatePrev = proto.AssociateType(relation)
			}
			timeFormatType := proto.TimeFormatTypeNoType
			if v.ValType == common.FieldTypeTime {
				timeFormatType = proto.TimeFormatTypeDay
			}
			viewContentNew.WhereInfoList = append(viewContentNew.WhereInfoList, &proto.CustomizeWhereInfo{
				CustomizeInfo: proto.CustomizeInfo{
					TimeFormat: proto.TimeFormat{
						TimeFormatType: timeFormatType,
					},
					Field:       v.Field,
					Operate:     proto.OperateType(v.Operate),
					Val:         v.Val,
					Associate:   associatePrev,
					Table:       v.Table,
					ValueType:   proto.ValueType(common.ValueTypes[int(v.ValType)]),
					DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", v.Table, v.Field)]),
				},
			})
		}
	}

	viewContentNew.LimitInfo = &proto.CustomizeLimitInfo{ //指标不处理默认排序
		IsCalculateTotal: true,
	}

	transformer := NewTransformer(ctx, info.ProjectResourceType, viewContentNew)
	err = transformer.PreCheckAndPrepareData(ctx, info.Repo)
	if err != nil {
		return "", err
	}
	//构建明细查询sql
	var buildCtx = context2.NewBuildContext(ctx, transformer, transformer,
		context2.WithGlobalFilter(transformer),
		context2.WithFilter(transformer),
		context2.WithAggregate(transformer), // TO DO 这里虽然没有聚合,但是不加上底层会报错,先这样处理
		context2.WithVariables(transformer),
	)

	sql := ""
	adsViewBuilder := ads_view.NewBuilder(buildCtx, info.ProjectResourceType)
	sql, err = adsViewBuilder.ToSQL()
	if err != nil {
		err = errors.Wrapf(err, "生成明细sql失败")
		return "", err
	}

	return sql, nil
}

func (i *IndicatorCardService) TransIndicatorTrendSql(ctx context.Context, info *common.IndicatorBuildSqlInfo, calculateCondition *indicator_card.IndicatorCalculateCondition) (string, error) {
	viewContentNew, err := common.GetViewContentNew(info.ViewContent)
	if err != nil {
		return "", err
	}

	//填充指标定义
	customFieldDimensions := make([]rpc_call.Prop, 0)
	customFieldDimensions = append(customFieldDimensions, common.GetCustomFieldDimension(info.IndicatorFieldCode, info.IndicatorFieldAlias, viewContentNew.CustomFieldDimensions))
	viewContentNew.CustomFieldDimensions = customFieldDimensions

	//填充指标过滤-普通条件过滤
	if calculateCondition.Where != nil {
		for index, v := range calculateCondition.Where.Info {
			relation := strings.ToLower(calculateCondition.Where.Relation)
			associatePrev := proto.NoAssociate
			if index != 0 {
				associatePrev = proto.AssociateType(relation)
			}
			timeFormatType := proto.TimeFormatTypeNoType
			if v.ValType == common.FieldTypeTime {
				timeFormatType = proto.TimeFormatTypeDay
			}
			viewContentNew.WhereInfoList = append(viewContentNew.WhereInfoList, &proto.CustomizeWhereInfo{
				CustomizeInfo: proto.CustomizeInfo{
					TimeFormat: proto.TimeFormat{
						TimeFormatType: timeFormatType,
					},
					Field:       v.Field,
					Operate:     proto.OperateType(v.Operate),
					Val:         v.Val,
					Associate:   associatePrev,
					Table:       v.Table,
					ValueType:   proto.ValueType(common.ValueTypes[int(v.ValType)]),
					DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", v.Table, v.Field)]),
				},
			})
		}
	}

	//填充时间字段聚合
	//group by
	timeFormatType := proto.TimeFormatTypeExtractDay
	if info.IndicatorTimeField != "" {
		switch info.IndicatorMinimumTimeParticle {
		case common.YearType:
			timeFormatType = proto.TimeFormatTypeExtractYear
		case common.QuarterType:
			timeFormatType = proto.TimeFormatTypeExtractQuater
		case common.MonthType:
			timeFormatType = proto.TimeFormatTypeExtractMonth
		case common.WeekType:
			timeFormatType = proto.TimeFormatTypeExtractWeek
		}
		viewContentNew.GroupByInfoList = append(viewContentNew.GroupByInfoList, &proto.CustomizGroupByInfo{
			CustomizeInfo: proto.CustomizeInfo{
				Table: info.IndicatorTimeTable,
				Field: info.IndicatorTimeField,
				TimeFormat: proto.TimeFormat{
					TimeFormatType: timeFormatType,
				},
				DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", info.IndicatorTimeTable, info.IndicatorTimeField)]),
			},
		})
	}

	//填充字段聚合
	//分析维度字段
	for _, v := range calculateCondition.GroupBy {
		//group by
		alias := v.Name
		if v.Name == "" {
			alias = v.EnName
		}
		viewContentNew.GroupByInfoList = append(viewContentNew.GroupByInfoList, &proto.CustomizGroupByInfo{
			CustomizeInfo: proto.CustomizeInfo{
				Table:       v.Table,
				Field:       v.EnName,
				Alias:       alias,
				DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", v.Table, v.EnName)]),
			},
		})
	}

	viewContentNew.LimitInfo = &proto.CustomizeLimitInfo{ //指标趋势不处理默认排序
		IsCalculateTotal: true,
	}

	//填充字段排序
	for _, v := range calculateCondition.OrderBy {
		direction := proto.Direction_Desc
		if v.Type == common.OrderTypeAsc {
			direction = proto.Direction_Asc
		}
		viewContentNew.OrderInfoList = append(viewContentNew.OrderInfoList, &proto.CustomizeOrderInfo{
			CustomizeInfo: proto.CustomizeInfo{
				Table:       v.Table,
				Field:       v.EnName,
				DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", v.Table, v.EnName)]),
			},
			Direction: direction,
		})
	}

	transformer := NewTransformer(ctx, info.ProjectResourceType, viewContentNew)
	err = transformer.PreCheckAndPrepareData(ctx, info.Repo)
	if err != nil {
		return "", err
	}
	//构建明细查询sql
	var buildCtx = context2.NewBuildContext(ctx, transformer, transformer,
		context2.WithGlobalFilter(transformer),
		context2.WithFilter(transformer),
		context2.WithAggregate(transformer),
		context2.WithSort(transformer),
		context2.WithVariables(transformer),
	)

	sql := ""
	adsViewBuilder := ads_view.NewBuilder(buildCtx, info.ProjectResourceType)
	sql, err = adsViewBuilder.ToSQL()
	if err != nil {
		err = errors.Wrapf(err, "生成明细sql失败")
		return "", err
	}

	return sql, nil
}

func (i *IndicatorCardService) TransIndicatorComponentSql(ctx context.Context, info *common.IndicatorBuildSqlInfo, is_calculate_total bool, chartType int32, calculateCondition *indicator_card.IndicatorCalculateCondition) (string, error) {
	viewContentNew, err := common.GetViewContentNew(info.ViewContent)
	if err != nil {
		return "", err
	}

	//填充指标定义
	customFieldDimensions := make([]rpc_call.Prop, 0)
	customFieldDimensions = append(customFieldDimensions, common.GetCustomFieldDimension(info.IndicatorFieldCode, info.IndicatorFieldAlias, viewContentNew.CustomFieldDimensions))
	for _, filterIndicatorInfo := range info.FilterIndicatorInfoMap {
		customFieldDimensions = append(customFieldDimensions, common.GetCustomFieldDimension(filterIndicatorInfo.FieldCode, filterIndicatorInfo.Name, viewContentNew.CustomFieldDimensions))
	}
	viewContentNew.CustomFieldDimensions = customFieldDimensions

	//填充指标过滤-普通条件过滤
	if calculateCondition.Where != nil {
		for index, v := range calculateCondition.Where.Info {
			relation := strings.ToLower(calculateCondition.Where.Relation)
			associatePrev := proto.NoAssociate
			if index != 0 {
				associatePrev = proto.AssociateType(relation)
			}
			timeFormatType := proto.TimeFormatTypeNoType
			if v.ValType == common.FieldTypeTime {
				timeFormatType = proto.TimeFormatTypeDay
			}
			viewContentNew.WhereInfoList = append(viewContentNew.WhereInfoList, &proto.CustomizeWhereInfo{
				CustomizeInfo: proto.CustomizeInfo{
					TimeFormat: proto.TimeFormat{
						TimeFormatType: timeFormatType,
					},
					Field:       v.Field,
					Operate:     proto.OperateType(v.Operate),
					Val:         v.Val,
					Associate:   associatePrev,
					Table:       v.Table,
					ValueType:   proto.ValueType(common.ValueTypes[int(v.ValType)]),
					DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", v.Table, v.Field)]),
				},
			})
		}
	}

	//填充字段聚合
	//组合分析维度字段
	for _, v := range calculateCondition.GroupBy {
		//group by
		alias := v.Name
		if v.Name == "" {
			alias = v.EnName
		}
		viewContentNew.GroupByInfoList = append(viewContentNew.GroupByInfoList, &proto.CustomizGroupByInfo{
			CustomizeInfo: proto.CustomizeInfo{
				Table:       v.Table,
				Field:       v.EnName,
				Alias:       alias,
				DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", v.Table, v.EnName)]),
			},
		})
	}

	//填充having过滤
	for _, filter := range info.IndicatorFilters {

		indicatorAlias := info.IndicatorFieldAlias
		if filter.FilterIndicatorCode != "" {
			filterIndicatorInfo := info.FilterIndicatorInfoMap[filter.FilterIndicatorCode]
			indicatorAlias = filterIndicatorInfo.Name
		}
		havingField := indicatorAlias
		viewContentNew.HavingInfoList = append(viewContentNew.HavingInfoList, &proto.CustomizeHavingInfo{
			CustomizeInfo: proto.CustomizeInfo{
				Field:     havingField,
				Operate:   proto.OperateType(filter.Operate),
				Associate: proto.AssociateType(filter.AssociatePrevious),
				Val:       filter.Val,
				ValueType: proto.ValueType_FloatValue,
			},
		})
	}
	//填充指标排序,指标排序用
	if chartType == common.ChartTypeIndicatorRank && calculateCondition.IndicatorOrder != nil {
		//oder by
		direction := proto.Direction_Desc
		if calculateCondition.IndicatorOrder.Type == common.OrderTypeAsc {
			direction = proto.Direction_Asc
		}
		viewContentNew.OrderInfoList = append(viewContentNew.OrderInfoList, &proto.CustomizeOrderInfo{
			CustomizeInfo: proto.CustomizeInfo{
				Field: info.IndicatorFieldAlias,
			},
			Direction: direction,
		})
		//limit
		if !is_calculate_total {
			var offset int32 = 0
			if calculateCondition.IndicatorOrder.Page > 0 {
				offset = (calculateCondition.IndicatorOrder.Page - 1) * calculateCondition.IndicatorOrder.Num
			}
			viewContentNew.LimitInfo = &proto.CustomizeLimitInfo{
				Limit:            int64(calculateCondition.IndicatorOrder.Num),
				Offset:           int64(offset),
				IsCalculateTotal: false,
			}
		}
	} else if len(calculateCondition.GroupBy) == 0 { //没传group_by的话,就不处理limit了
		viewContentNew.LimitInfo = &proto.CustomizeLimitInfo{
			IsCalculateTotal: true,
		}
	}

	if is_calculate_total { //设置了计算总数的话,就不处理limit了
		viewContentNew.LimitInfo = &proto.CustomizeLimitInfo{
			IsCalculateTotal: true,
		}
	}

	//填充字段排序
	for _, v := range calculateCondition.OrderBy {
		direction := proto.Direction_Desc
		if v.Type == common.OrderTypeAsc {
			direction = proto.Direction_Asc
		}
		viewContentNew.OrderInfoList = append(viewContentNew.OrderInfoList, &proto.CustomizeOrderInfo{
			CustomizeInfo: proto.CustomizeInfo{
				Table:       v.Table,
				Field:       v.EnName,
				DimViewData: common.FieldViewModelMapToDimViewData(info.DimViewDataMap[fmt.Sprintf("%s.%s", v.Table, v.EnName)]),
			},
			Direction: direction,
		})
	}

	transformer := NewTransformer(ctx, info.ProjectResourceType, viewContentNew)
	err = transformer.PreCheckAndPrepareData(ctx, info.Repo)
	if err != nil {
		return "", err
	}

	//构建明细查询sql
	var buildCtx = context2.NewBuildContext(ctx, transformer, transformer,
		context2.WithGlobalFilter(transformer),
		context2.WithFilter(transformer),
		context2.WithAggregate(transformer),
		context2.WithSort(transformer),
		context2.WithLimit(transformer),
		context2.WithVariables(transformer),
	)

	sql := ""
	adsViewBuilder := ads_view.NewBuilder(buildCtx, info.ProjectResourceType)
	sql, err = adsViewBuilder.ToSQL()
	if err != nil {
		err = errors.Wrapf(err, "生成明细sql失败")
		return "", err
	}

	return sql, nil
}

func (i *IndicatorCardService) getRealIndicatorRawExpress(indicatorField string) string {
	replacer := strings.NewReplacer("{", "", "}", "", "[", "", "]", "")
	return replacer.Replace(indicatorField)
}

func (i *IndicatorCardService) TransIndicatorComponentCountSql(ctx context.Context, info *common.IndicatorBuildSqlInfo, chartType int32, calculateCondition *indicator_card.IndicatorCalculateCondition) (string, error) {
	sql, err := i.TransIndicatorComponentSql(ctx, info, true, chartType, calculateCondition)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("select count(1) as %s from (%s) t ", IndicatorListTotal, sql), nil
}

func (i *IndicatorCardService) getDateRange(dateRange []string, timeType int32, dayNum int32) []string {
	if timeType != 0 {
		return common.GetTimeDateRange(timeType, int(dayNum))
	}

	//如果没有时间筛选器,计算出当前统计时间的时间范围用来拼接sql
	if len(dateRange) != 0 {
		return dateRange
	}

	return []string{}
}

// 获取指标 timeField 最近的时间子查询sql
func (i *IndicatorCardService) buildLateTimeFieldSubSql(ctx context.Context, info *common.IndicatorBuildSqlInfo) (string, error) {
	plan := build_plan.BuildPlan{}

	timeTableName := ""
	if info.IndicatorTimeTable != "" {
		timeTableName = info.IndicatorTimeTable
	} else {
		timeTableName = info.IndicatorTable
	}

	// 填充指标字段
	plan.Fields = make([]*build_plan.Project, 0)
	plan.Aggregation = new(build_plan.Aggregation)
	plan.Aggregation.Fields = make([]*build_plan.Project, 0)
	plan.Aggregation = &build_plan.Aggregation{}
	plan.Aggregation.Fields = make([]*build_plan.Project, 0)
	field1 := &build_plan.Project{
		Field: &build_plan.FieldContent{
			Table:           "a",
			FieldName:       info.IndicatorTimeField,
			FieldAlias:      info.IndicatorTimeField,
			FieldTimeFormat: build_plan.TimeFieldStatType_ExtractDay,
		},
		Type: build_plan.ProjectType_Field,
	}
	plan.Aggregation.Fields = append(plan.Aggregation.Fields, field1)

	plan.Fields = append(plan.Fields, field1)

	// 填充指标表
	plan.Table = &build_plan.TableScan{
		Type:        0,
		SingleTable: timeTableName,
		Alias:       "a",
	}

	engineManager := engine.NewEngineManager()
	builder, err := engineManager.GetBuilder(info.EngineType)
	if err != nil {
		return "", err
	}
	subSql, err := builder.BuildSql(ctx, &plan)
	if err != nil {
		return "", err
	}

	extPlan := build_plan.BuildPlan{}

	extPlan.Fields = append(extPlan.Fields, &build_plan.Project{
		Type: build_plan.ProjectType_Field,
		Field: &build_plan.FieldContent{
			Table:      "u",
			FieldName:  info.IndicatorTimeField,
			FieldFunc:  build_plan.FieldFuncType_Max,
			FieldAlias: info.IndicatorTimeField,
		},
	})

	extPlan.Table = &build_plan.TableScan{
		Type:        build_plan.FromType_FromSubQuerySql,
		SqlSubQuery: subSql,
		Alias:       "u",
	}

	lastSql, err := builder.BuildSql(ctx, &extPlan)
	if err != nil {
		return "", err
	}

	return lastSql, nil
}
