package indicator_card_service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	psProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_query/service/bigdata_query_service/table_permission_service/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/errgroup"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_card"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	pkgFast "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
)

func (i *IndicatorCardService) isAggregationField(field string, indicatorBuildSqlInfo *common.IndicatorBuildSqlInfo) bool {
	fieldMap := make(map[string]bool, 0)
	for _, v := range indicatorBuildSqlInfo.FilterIndicatorInfoMap {
		fieldMap[v.Name] = true
	}
	_, ok := fieldMap[field]
	if field != indicatorBuildSqlInfo.IndicatorFieldAlias && !ok {
		return true
	}
	return false
}

func (i *IndicatorCardService) getQueryResult(ctx context.Context, fastRecordReq *IndicatorDataBizParamsReq, req *QueryReq) ([]*QueryIndicatorResult, error) {
	queryResultList := make([]*QueryIndicatorResult, 0)
	var err error
	queryResult := &QueryIndicatorResult{}
	switch req.ChartType {
	case common.ChartTypeIndicator:
		queryResult, err = i.getIndicatorQueryResult(ctx, fastRecordReq, req)
		queryResultList = append(queryResultList, queryResult)
	case common.ChartTypeIndicatorTrend:
		queryResult, err = i.getIndicatorTrendQueryResult(ctx, fastRecordReq, req)
		queryResultList = append(queryResultList, queryResult)
	case common.ChartTypeIndicatorPercent, common.ChartTypeDimensionAnalysis, common.ChartTypeIndicatorRank:
		queryResult, err = i.getIndicatorComponentQueryResult(ctx, fastRecordReq, req)
		queryResultList = append(queryResultList, queryResult)
	case common.ChartTypeIndicatorBatch:
		queryResultList, err = i.getIndicatorBatchQueryResult(ctx, fastRecordReq, req)
	default:
		return queryResultList, errors.New("chart_type 类型错误")
	}
	return queryResultList, err
}

func (i *IndicatorCardService) getIndicatorQueryResult(ctx context.Context, fastRecordReq *IndicatorDataBizParamsReq, req *QueryReq) (*QueryIndicatorResult, error) {
	queryResult := &QueryIndicatorResult{}
	queryResult.IndicatorId = req.IndicatorBuildSqlInfo.IndicatorId
	queryResult.IndicatorCode = req.IndicatorBuildSqlInfo.IndicatorCode
	queryResult.CalculateConditionCode = req.CalculateCondition.CalculateConditionCode

	sql, err := i.TransIndicatorSql(ctx, req.IndicatorBuildSqlInfo, req.CalculateCondition)
	fastRecordReq.ExecuteSql = append(fastRecordReq.ExecuteSql, sql)
	queryResult.Sql = sql
	if err != nil {
		return queryResult, errors.Wrap(fmt.Errorf("指标code:%s 查询条件code:%s sql构建失败:%s", queryResult.IndicatorCode, queryResult.CalculateConditionCode, err.Error()), pkgFast.BusinessErrorMsg)
	}
	if req.UserCode != "" {
		var rsp *psProto.AddPermissionRsp
		rsp, err = i.permissionService.AddPermission(ctx, &psProto.AddPermissionReq{
			SQL:         sql,
			ProjectCode: req.IndicatorBuildSqlInfo.Project,
			TenantCode:  req.IndicatorBuildSqlInfo.OrgCode,
			AuthParams: &psProto.AuthParams{
				UserCode: req.UserCode,
			},
		})
		if err != nil {
			return queryResult, err
		}
		queryResult.Sql = rsp.SQL
	}

	data, duration, sql, err := common.QuerySqlByDataSourceMeta(ctx, req.Resource, req.IndicatorBuildSqlInfo.Project, queryResult.Sql)
	fastRecordReq.recordSqlExecTime(sql, duration)
	queryResult.Sql = sql
	if err != nil {
		return queryResult, errors.Wrap(fmt.Errorf("指标code:%s 查询条件code:%s sql查询失败:%s", queryResult.IndicatorCode, queryResult.CalculateConditionCode, err.Error()), pkgFast.BusinessErrorMsg)
	}

	if data.Data == nil {
		return queryResult, nil
	}

	jsonData, err := json.Marshal(data.Data)
	if err != nil {
		return queryResult, err
	}

	dataV := gjson.ParseBytes(jsonData).Array()
	for _, v := range dataV {
		val := v.Get(req.IndicatorBuildSqlInfo.IndicatorFieldAlias).Float()
		queryResult.IndicatorVal = floatToStringWithDecimalPoint(val)
	}
	return queryResult, nil
}

func (i *IndicatorCardService) getIndicatorTrendQueryResult(ctx context.Context, fastRecordReq *IndicatorDataBizParamsReq, req *QueryReq) (*QueryIndicatorResult, error) {
	queryResult := &QueryIndicatorResult{}
	queryResult.IndicatorId = req.IndicatorBuildSqlInfo.IndicatorId
	queryResult.IndicatorCode = req.IndicatorBuildSqlInfo.IndicatorCode
	queryResult.CalculateConditionCode = req.CalculateCondition.CalculateConditionCode

	sql, err := i.TransIndicatorTrendSql(ctx, req.IndicatorBuildSqlInfo, req.CalculateCondition)
	queryResult.Sql = sql
	fastRecordReq.ExecuteSql = append(fastRecordReq.ExecuteSql, sql)
	if err != nil {
		return queryResult, errors.Wrap(fmt.Errorf("指标code:%s 查询条件code:%s sql构建失败:%s", queryResult.IndicatorCode, queryResult.CalculateConditionCode, err.Error()), pkgFast.BusinessErrorMsg)
	}
	if req.UserCode != "" {
		var rsp *psProto.AddPermissionRsp
		rsp, err = i.permissionService.AddPermission(ctx, &psProto.AddPermissionReq{
			SQL:         sql,
			ProjectCode: req.IndicatorBuildSqlInfo.Project,
			TenantCode:  req.IndicatorBuildSqlInfo.OrgCode,
			AuthParams: &psProto.AuthParams{
				UserCode: req.UserCode,
			},
		})
		if err != nil {
			return queryResult, err
		}
		queryResult.Sql = rsp.SQL
	}

	data, duration, sql, err := common.QuerySqlByDataSourceMeta(ctx, req.Resource, req.IndicatorBuildSqlInfo.Project, queryResult.Sql)
	fastRecordReq.recordSqlExecTime(sql, duration)
	queryResult.Sql = sql
	if err != nil {
		return queryResult, errors.Wrap(fmt.Errorf("指标code:%s 查询条件code:%s sql查询失败:%s", queryResult.IndicatorCode, queryResult.CalculateConditionCode, err.Error()), pkgFast.BusinessErrorMsg)
	}

	if data.Data == nil {
		return queryResult, nil
	}

	jsonData, err := json.Marshal(data.Data)
	if err != nil {
		return queryResult, err
	}

	dataList := gjson.ParseBytes(jsonData).Array()
	for _, v := range dataList {
		cntName := req.IndicatorBuildSqlInfo.IndicatorFieldAlias
		val := v.Get(cntName).Float()

		dimensions := make([]*indicator_card.IndicatorTrend_KV, 0)
		vInfo := v.Map()
		for k, _ := range vInfo {
			if k != req.IndicatorBuildSqlInfo.IndicatorTimeField && k != req.IndicatorBuildSqlInfo.IndicatorFieldAlias {
				val := vInfo[k].String()
				dimensions = append(dimensions, &indicator_card.IndicatorTrend_KV{
					Name: k,
					Val:  val,
				})
			}
		}

		queryResult.IndicatorsTrend = append(queryResult.IndicatorsTrend, &indicator_card.IndicatorTrend{
			Date:       v.Get(req.IndicatorBuildSqlInfo.IndicatorTimeField).String(),
			Val:        floatToStringWithDecimalPoint(val),
			Dimensions: dimensions,
		})
	}
	return queryResult, nil
}

func (i *IndicatorCardService) getIndicatorComponentQueryResult(ctx context.Context, fastRecordReq *IndicatorDataBizParamsReq, req *QueryReq) (*QueryIndicatorResult, error) {
	queryResult := &QueryIndicatorResult{}
	queryResult.IndicatorId = req.IndicatorBuildSqlInfo.IndicatorId
	queryResult.IndicatorCode = req.IndicatorBuildSqlInfo.IndicatorCode
	queryResult.CalculateConditionCode = req.CalculateCondition.CalculateConditionCode

	sql, err := i.TransIndicatorComponentSql(ctx, req.IndicatorBuildSqlInfo, false, req.ChartType, req.CalculateCondition)
	queryResult.Sql = sql
	fastRecordReq.ExecuteSql = append(fastRecordReq.ExecuteSql, sql)
	if err != nil {
		return queryResult, errors.Wrap(fmt.Errorf("指标code:%s 查询条件code:%s sql构建失败:%s", queryResult.IndicatorCode, queryResult.CalculateConditionCode, err.Error()), pkgFast.BusinessErrorMsg)
	}
	if req.UserCode != "" {
		var rsp *psProto.AddPermissionRsp
		rsp, err = i.permissionService.AddPermission(ctx, &psProto.AddPermissionReq{
			SQL:         sql,
			ProjectCode: req.IndicatorBuildSqlInfo.Project,
			TenantCode:  req.IndicatorBuildSqlInfo.OrgCode,
			AuthParams: &psProto.AuthParams{
				UserCode: req.UserCode,
			},
		})
		if err != nil {
			return queryResult, err
		}
		queryResult.Sql = rsp.SQL
	}

	data, duration, sql, err := common.QuerySqlByDataSourceMeta(ctx, req.Resource, req.IndicatorBuildSqlInfo.Project, queryResult.Sql)
	fastRecordReq.recordSqlExecTime(sql, duration)
	queryResult.Sql = sql
	if err != nil {
		return queryResult, errors.Wrap(fmt.Errorf("指标code:%s 查询条件code:%s sql查询失败:%s", queryResult.IndicatorCode, queryResult.CalculateConditionCode, err.Error()), pkgFast.BusinessErrorMsg)
	}

	jsonData, err := json.Marshal(data.Data)
	if err != nil {
		return queryResult, err
	}

	dataArr := gjson.ParseBytes(jsonData).Array()
	queryResult.IndicatorsAggregations = make([]*indicator_card.IndicatorsAggregation, 0)
	for _, info := range dataArr {
		indicatorVal := info.Get(req.IndicatorBuildSqlInfo.IndicatorFieldAlias).Float()
		temp := &indicator_card.IndicatorsAggregation{
			IndicatorVal: floatToStringWithDecimalPoint(indicatorVal),
		}
		temp.Dimensions = make([]*indicator_card.IndicatorsAggregation_KV, 0)
		mInfo := info.Map()
		for k, _ := range mInfo {
			if i.isAggregationField(k, req.IndicatorBuildSqlInfo) {
				val := mInfo[k].String()
				dimensions := indicator_card.IndicatorsAggregation_KV{
					Name: k,
					Val:  val,
				}
				temp.Dimensions = append(temp.Dimensions, &dimensions)
			}
		}
		queryResult.IndicatorsAggregations = append(queryResult.IndicatorsAggregations, temp)
	}

	if req.CalculateCondition.IsCalculateTotal && len(queryResult.IndicatorsAggregations) != 0 {
		totalSql, err := i.TransIndicatorComponentCountSql(ctx, req.IndicatorBuildSqlInfo, req.ChartType, req.CalculateCondition)
		fastRecordReq.ExecuteSql = append(fastRecordReq.ExecuteSql, totalSql)
		if err != nil {
			return queryResult, err
		}
		data, duration, sql, err := common.QuerySqlByDataSourceMeta(ctx, req.Resource, req.IndicatorBuildSqlInfo.Project, totalSql)
		fastRecordReq.recordSqlExecTime(sql, duration)
		queryResult.Sql = sql
		if err != nil {
			return queryResult, err
		}

		jsonData, err := json.Marshal(data.Data)
		if err != nil {
			return queryResult, err
		}

		dataV := gjson.ParseBytes(jsonData).Array()
		for _, v := range dataV {
			val := v.Get(IndicatorListTotal).Int()
			queryResult.IndicatorsAggregationTotal = int32(val)
		}
	}
	return queryResult, nil
}

func (i *IndicatorCardService) getIndicatorBatchQueryResult(ctx context.Context, fastRecordReq *IndicatorDataBizParamsReq, req *QueryReq) ([]*QueryIndicatorResult, error) {
	//生成sql并查询数据
	queryResultList := make([]*QueryIndicatorResult, 0)
	var eg *errgroup.Group
	var mutex sync.Mutex
	eg = errgroup.WithContext(ctx)
	for _, calculateCondition := range req.IndicatorBuildSqlInfo.CalculateConditionList {
		calculateCondition := calculateCondition
		if calculateCondition.ChartType < common.ChartTypeIndicator ||
			calculateCondition.ChartType > common.ChartTypeIndicatorRank {
			return queryResultList, errors.New("chart_type 类型错误")
		}
		queryReq := &QueryReq{
			IndicatorBuildSqlInfo: req.IndicatorBuildSqlInfo,
			CalculateCondition:    calculateCondition,
			ChartType:             calculateCondition.ChartType,
			Resource:              req.Resource,
			UserCode:              req.UserCode,
		}
		eg.Go(func(ctx context.Context) error {
			qrList, err := i.getQueryResult(ctx, fastRecordReq, queryReq)
			if err != nil {
				return err
			}
			mutex.Lock()
			for _, qr := range qrList {
				if qr == nil {
					continue
				}
				newQr := qr
				queryResultList = append(queryResultList, newQr)
			}
			mutex.Unlock()
			return err
		})
	}
	if err := eg.Wait(); err != nil {
		return queryResultList, err
	}
	return queryResultList, nil
}

func (i *IndicatorCardService) preCheckAndPrepareData(ctx context.Context, wrapper *common.IndicatorRepoWrapper, project string, resourceType string, req *indicator_card.QueryIndicatorDataRequest) ([]*indicator_repository.IndicatorModelAndFieldInfo, map[string]*common.IndicatorBuildSqlInfo, error) {
	//获取查询引擎
	projectResourceType := entities.ProjectResourceType(resourceType)
	queryEngineType, err := common.GetEngineTypeNew(projectResourceType)
	if err != nil {
		return nil, nil, err
	}

	if len(req.IndicatorInfo) == 0 {
		return nil, nil, errors.New("指标信息为空")
	}

	//获取指标信息
	indicatorCodes := make([]string, 0)
	for _, v := range req.IndicatorInfo {
		if v.IndicatorCode != "" {
			indicatorCodes = append(indicatorCodes, v.IndicatorCode)
		}
	}
	if len(indicatorCodes) == 0 {
		return nil, nil, errors.New("指标编码为空")
	}
	queryIndicatorMap := make(map[string]bool, 0)
	for _, v := range indicatorCodes {
		queryIndicatorMap[v] = true
	}

	//获取指标模型code
	indicatorModelCodeList, err := wrapper.GetIndicatorModelCodeByIndicatorCodes(ctx, indicatorCodes, string(global.Prod))
	if err != nil {
		return nil, nil, err
	}
	if len(indicatorModelCodeList) == 0 {
		return nil, nil, errors.New("无有效指标信息")
	}

	//获取指标和维度列表
	indicatorModelAndFieldList, err := wrapper.GetIndicatorModelAndFieldList(ctx, indicatorModelCodeList, string(global.Prod))
	if err != nil {
		return nil, nil, err
	}

	//获取指标模型view_content
	indicatorModelViewContentMap := make(map[string]string, 0)
	indicatorModelList, err := wrapper.GetIndicatorListByCodes(ctx, indicatorModelCodeList, string(global.Prod))
	if err != nil {
		return nil, nil, err
	}
	for _, v := range indicatorModelList {
		indicatorModelViewContentMap[v.Code] = v.ViewContent
	}

	//获取维度数据
	dimViewDataMap := make(map[string]*modelBase.FieldViewModel, 0)
	for _, v := range indicatorModelAndFieldList {
		if v.DimType != string(modelBase.DimensionDimType) {
			continue
		}
		dimViewData := &modelBase.FieldViewModel{}
		if v.DimViewData != "" {
			json.Unmarshal([]byte(v.DimViewData), dimViewData)
		}
		dimViewDataMap[fmt.Sprintf("%s.%s", v.ModelNameEn, v.Name)] = dimViewData
	}

	indicatorFilterMap := make(map[string][]*indicator_card.IndicatorFilter, 0)
	indicatorCalculateConditionMap := make(map[string][]*indicator_card.IndicatorCalculateCondition, 0)
	for _, indicator := range req.IndicatorInfo {
		code := indicator.IndicatorCode
		indicatorFilterMap[code] = indicator.IndicatorFilters
		indicatorCalculateConditionMap[code] = indicator.CalculateConditionList
	}

	//生成指标构建sql结构
	indicatorBuildSqlInfoMap := make(map[string]*common.IndicatorBuildSqlInfo, 0)
	newIndicatorModelAndFieldList := make([]*indicator_repository.IndicatorModelAndFieldInfo, 0)
	for _, info := range indicatorModelAndFieldList {
		if _, ok := queryIndicatorMap[info.Code]; !ok {
			continue
		}
		newIndicatorModelAndFieldList = append(newIndicatorModelAndFieldList, info)
		indicatorBuildSqlInfo := &common.IndicatorBuildSqlInfo{
			IndicatorField:      info.Name,
			IndicatorFieldAlias: info.Name,
			IndicatorTable:      info.ModelNameEn,
			IndicatorCode:       info.Code,
			IndicatorFieldCode:  info.FieldCode,
			IndicatorModelCode:  info.ModelCode,
			IndicatorFieldCn:    info.NameCn,
			IndicatorId:         info.ID,
			ViewContent:         indicatorModelViewContentMap[info.ModelCode],
			DimViewDataMap:      dimViewDataMap,
			Project:             project,
			Repo:                wrapper.GetRepo(),
			EngineType:          queryEngineType,
			ProjectResourceType: projectResourceType,
			OrgCode:             req.OrgCode,
		}

		indicatorBuildSqlInfo.IndicatorFilters = indicatorFilterMap[info.Code]
		indicatorBuildSqlInfo.CalculateConditionList = indicatorCalculateConditionMap[info.Code]
		indicatorBuildSqlInfo.FilterIndicatorInfoMap = make(map[string]*indicator_repository.IndicatorModelAndFieldInfo, 0)
		for _, filter := range indicatorBuildSqlInfo.IndicatorFilters { //获取过滤指标code对应的sql片段
			if filter.FilterIndicatorCode != "" {
				filterIndicatorFieldInfo, err := wrapper.GetIndicatorModelAndFieldInfoByCode(ctx, filter.FilterIndicatorCode, string(global.Prod))
				if err != nil {
					return nil, nil, err
				}
				if filterIndicatorFieldInfo.ID == "" {
					return nil, nil, errors.Wrap(fmt.Errorf("指标过滤条件错误,过滤指标:%v,不存在", filter.FilterIndicatorCode), pkgFast.BusinessErrorMsg)
				}

				indicatorBuildSqlInfo.FilterIndicatorInfoMap[filter.FilterIndicatorCode] = filterIndicatorFieldInfo
			}
		}
		indicatorBuildSqlInfoMap[info.ID] = indicatorBuildSqlInfo
	}
	return newIndicatorModelAndFieldList, indicatorBuildSqlInfoMap, nil
}

func floatToStringWithDecimalPoint(val float64) string {
	val, _ = strconv.ParseFloat(fmt.Sprintf("%.8f", val), 64)
	valStr := strconv.FormatFloat(val, 'f', -1, 64)
	return valStr
}
