package indicator_common_service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global/consts"

	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	modelBase "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_query/service/bigdata_query_service/table_permission_service"
	psProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_query/service/bigdata_query_service/table_permission_service/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_query/service/common_service/physical_model_service"
	pmProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_query/service/common_service/physical_model_service/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/errgroup"
	"gitlab.mypaas.com.cn/fast/tracker-go/trace"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/fast"

	"github.com/pkg/errors"
	"github.com/tidwall/gjson"

	pkgFast "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"

	dap_common "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view"
	context2 "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/biz/modeling/model/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errcode"
)

type IndicatorCommonService struct {
	permissionService    table_permission_service.TablePermissionService
	physicalModelService physical_model_service.PhysicalModelService
	localCache           *cache.LocalCache
	dapCommonService     dap_common.DapCommonService
}

func NewIndicatorCommonService() *IndicatorCommonService {
	s := &IndicatorCommonService{
		dapCommonService: dap_common.NewDapCommonServiceRepoImpl(),
	}
	global.Container.MustExtract(&s.permissionService)
	global.Container.MustExtract(&s.physicalModelService)
	global.Container.MustExtract(&s.localCache)
	return s
}

func (i *IndicatorCommonService) GetIndicatorList(ctx context.Context, req *indicator_common.GetIndicatorListRequest) (*indicator_common.GetIndicatorListResponse, error) {
	result := new(indicator_common.GetIndicatorListResponse)
	result.RequestId = trace.GetTraceId(ctx)
	result.Data = make([]*indicator_common.IndicatorListData, 0)
	result.ErrCode = 1
	result.ErrMsg = "成功"

	var err error
	if req.TenantCode != "" {
		tenantProjectRelInfo, err := i.dapCommonService.GetTenantProjectRel(ctx, req.TenantCode)
		if err != nil {
			return result, err
		}
		if tenantProjectRelInfo.CustomProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CustomProjectCode
		} else if tenantProjectRelInfo.CommonProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CommonProjectCode
		}
	}

	event := fast.StartEvent(pkgFast.CallApi, req.ProjectCode, consts.ADMIN)
	fastRecordReq := &IndicatorListBizParams{
		Project:    req.ProjectCode,
		Tenant:     req.TenantCode,
		ReqTime:    time.Now(),
		ReqParams:  req,
		RespParams: result,
	}
	defer func() {
		if err != nil {
			fastRecordReq.Result = "调用失败"
		} else {
			fastRecordReq.Result = "调用成功"
		}
		event.EndEvent(ctx, err)
	}()
	event.RecordBizParams(GenerateGetIndicatorListBizParams(ctx, fastRecordReq))

	var wrapper *common.IndicatorRepoWrapper
	wrapper, err = i.getIndicatorRepoWrapper(ctx, req.ProjectCode)
	if err != nil {
		return result, err
	}

	indicatorModelCodeList := make([]string, 0)
	keyWordList := make([]string, 0)
	codes := make([]string, 0)
	queryCodeMap := make(map[string]bool, 0)
	if req.IndicatorCode != "" {
		codes = strings.Split(req.IndicatorCode, ",")
	}
	for _, v := range codes {
		queryCodeMap[v] = true
	}
	if req.ClassId != "" {
		indicatorModelCodeList = strings.Split(req.ClassId, ",")
	}
	if req.Keyword != "" {
		keyWordList = strings.Split(req.Keyword, ",")
	}
	if len(codes) == 0 && len(indicatorModelCodeList) == 0 && len(keyWordList) == 0 {
		return result, err
	}

	//先根据查询的指标code和关键词筛选需要查询的指标模型code列表
	modelCodeList, err := wrapper.GetIndicatorModelCodeByCodesAndKeyWords(ctx, codes, keyWordList, string(global.Prod))
	if err != nil {
		return result, err
	}
	if len(modelCodeList) != 0 {
		indicatorModelCodeList = append(indicatorModelCodeList, modelCodeList...)
	}
	// 对indicatorModelCodeList去个重
	indicatorModelCodeList = utils.Deduplication(indicatorModelCodeList)

	//获取指标列表数据
	indicatorFieldList, err := wrapper.GetIndicatorModelAndFieldList(ctx, indicatorModelCodeList, string(global.Prod))
	if err != nil {
		return result, err
	}
	if len(indicatorFieldList) == 0 {
		return result, nil
	}

	//获取指标维度信息
	dimensionMap := make(map[string][]*indicator_repository.IndicatorModelAndFieldInfo, 0)
	for _, v := range indicatorFieldList {
		if v.DimType == string(modelBase.DimensionDimType) {
			dimensionMap[v.ModelCode] = append(dimensionMap[v.ModelCode], v)
		}
	}

	//获取指标模型view_content
	indicatorModelViewContentMap := make(map[string]string, 0)
	indicatorModelList, err := wrapper.GetIndicatorListByCodes(ctx, indicatorModelCodeList, string(global.Prod))
	if err != nil {
		return result, err
	}
	for _, v := range indicatorModelList {
		indicatorModelViewContentMap[v.Code] = v.ViewContent
	}

	//获取每个指标的可分析维度
	DimensionCodeMap := make(map[string]bool, 0)
	DimensionFieldTypeReqList := make([]*pmProto.IndicatorDimension, 0)
	for _, indicatorField := range indicatorFieldList {
		//有指标查询code过来的话,只返回对应的指标信息
		if len(codes) != 0 {
			if _, ok := queryCodeMap[indicatorField.Code]; !ok {
				continue
			}
		}

		if indicatorField.DimType == string(modelBase.DimensionDimType) {
			continue
		}

		for _, v := range dimensionMap[indicatorField.ModelCode] {
			if _, ok := DimensionCodeMap[v.FieldCode]; ok {
				continue
			}
			DimensionFieldTypeReqList = append(DimensionFieldTypeReqList, &pmProto.IndicatorDimension{
				Code:           v.FieldCode,
				DimViewDataRaw: v.DimViewData,
			})
		}
		sctx := context.WithValue(ctx, cache.CtxKeyInstance, i.localCache)
		analyticalFieldInfos, err := i.getAnalyticalFieldInfos(sctx, &getAnalyticalFieldInfosReq{
			projectCode:   req.ProjectCode,
			fieldCode:     indicatorField.FieldCode,
			modelName:     indicatorField.ModelName,
			modelNameEn:   indicatorField.ModelNameEn,
			viewContent:   indicatorModelViewContentMap[indicatorField.ModelCode],
			dimensionList: dimensionMap[indicatorField.ModelCode],
		})
		if err != nil {
			return result, err
		}
		result.Data = append(result.Data, &indicator_common.IndicatorListData{
			Id:            indicatorField.ID,
			IndicatorCode: indicatorField.Code,
			Name:          indicatorField.Name,
			NameCn:        indicatorField.NameCn,
			IndicatorUnit: indicatorField.IndicatorUnit,
			Description:   indicatorField.Description,
			CreatedBy:     indicatorField.CreatedBy,
			CreatedOn:     indicatorField.CreatedOn.Format(time.DateTime),
			ModifiedBy:    indicatorField.ModifiedBy,
			ModifiedOn:    indicatorField.ModifiedOn.Format(time.DateTime),
			Class: &indicator_common.ClassItem{
				Id:                   indicatorField.ModelCode,
				Name:                 indicatorField.ModelName,
				AnalyticalFieldInfos: analyticalFieldInfos,
			},
		})
	}

	//处理维度类型
	if !req.IsNotNeedFieldDerivation {
		resourceInfo, err := i.dapCommonService.GetStorageResourceV3(ctx, req.ProjectCode, req.TenantCode)
		if err != nil {
			return result, err
		}
		ftReq := &pmProto.BatchGetIndicatorDimensionFieldTypeReq{
			ProjectCode:            req.ProjectCode,
			ResourceType:           resourceInfo.ResourceType,
			IndicatorDimensionList: DimensionFieldTypeReqList,
		}

		rsp, err := i.batchGetIndicatorDimensionFieldType(ctx, ftReq)
		if err != nil {
			result.Data = make([]*indicator_common.IndicatorListData, 0)
			return result, err
		}
		fieldTypeInfoMap := make(map[string]string, 0)
		for _, fieldTypeInfo := range rsp.List {
			fieldTypeInfoMap[fieldTypeInfo.Code] = fieldTypeInfo.FieldType
		}
		for i, info := range result.Data {
			if info.Class != nil {
				for j, field := range info.Class.AnalyticalFieldInfos {
					if fieldType, ok := fieldTypeInfoMap[field.FieldCode]; ok {
						result.Data[i].Class.AnalyticalFieldInfos[j].Type = common.GetFieldType(fieldType)
					}
				}
			}
		}
	}
	return result, nil
}

func (i *IndicatorCommonService) batchGetIndicatorDimensionFieldType(ctx context.Context, req *pmProto.BatchGetIndicatorDimensionFieldTypeReq) (
	rsp *pmProto.BatchGetIndicatorDimensionFieldTypeRsp, err error) {
	var indicatorCodes = lo.Map(req.IndicatorDimensionList, func(indicatorDimension *pmProto.IndicatorDimension, index int) string {
		// 截取code前缀，只保留前8个字符
		var parts = strings.Split(indicatorDimension.Code, "-")
		if len(parts) > 0 {
			return parts[0]
		}
		return indicatorDimension.Code
	})
	var suffix = strings.TrimSpace(strings.Join(indicatorCodes, ","))
	if len(suffix) < 1 {
		suffix = "null"
	}
	var key = fmt.Sprintf("BatchGetIndicatorDimensionFieldType_%s_%s_%s", req.ProjectCode, req.ResourceType, suffix)
	value, found := i.localCache.Get(ctx, key)
	if found {
		var ok bool
		if rsp, ok = value.(*pmProto.BatchGetIndicatorDimensionFieldTypeRsp); ok {
			return
		} else {
			global.Logger.Errorf("cache value type error, expect *pmProto.BatchGetIndicatorDimensionFieldTypeRsp, but got %T", value)
		}
	}
	rsp, err = i.physicalModelService.BatchGetIndicatorDimensionFieldType(ctx, req)
	if err != nil {
		return
	}
	i.localCache.Set(ctx, key, rsp, 5*time.Minute)
	return
}

func (i *IndicatorCommonService) GetIndicatorClass(ctx context.Context, req *indicator_common.GetIndicatorClassRequest) (*indicator_common.GetIndicatorClassResponse, error) {
	result := new(indicator_common.GetIndicatorClassResponse)
	result.RequestId = trace.GetTraceId(ctx)
	result.Data = make([]*indicator_common.ClassItem, 0)
	result.ErrCode = 1
	result.ErrMsg = "成功"

	var err error
	if req.TenantCode != "" {
		tenantProjectRelInfo, err := i.dapCommonService.GetTenantProjectRel(ctx, req.TenantCode)
		if err != nil {
			return result, err
		}
		if tenantProjectRelInfo.CustomProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CustomProjectCode
		} else if tenantProjectRelInfo.CommonProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CommonProjectCode
		}
	}

	event := fast.StartEvent(pkgFast.CallApi, req.ProjectCode, consts.ADMIN)
	fastRecordReq := &IndicatorClassBizParams{
		Project:    req.ProjectCode,
		Tenant:     req.TenantCode,
		ReqTime:    time.Now(),
		ReqParams:  req,
		RespParams: result,
	}
	defer func() {
		if err != nil {
			fastRecordReq.Result = "调用失败"
		} else {
			fastRecordReq.Result = "调用成功"
		}
		event.EndEvent(ctx, err)
	}()
	event.RecordBizParams(GenerateGetIndicatorClassBizParams(ctx, fastRecordReq))

	var wrapper *common.IndicatorRepoWrapper
	wrapper, err = i.getIndicatorRepoWrapper(ctx, req.ProjectCode)

	codes := make([]string, 0)
	if req.Id != "" {
		codes = append(codes, req.Id)
	}
	releaseIndicatorModelList, err := wrapper.BatchGetIndicator(ctx, codes, string(global.Prod), int(base.OnShelfApplyStatus))
	if err != nil {
		return result, err
	}

	//获取主题列表
	subjectList, err := wrapper.GetAllSubjectList(ctx)
	if err != nil {
		return result, err
	}
	subjectMap := make(map[string]*mysql.Subject, 0)
	for _, v := range subjectList {
		subjectMap[v.ID] = v
	}

	for _, releaseIndicatorMode := range releaseIndicatorModelList {
		viewContent := proto.ViewContentNew{}
		err = json.Unmarshal([]byte(releaseIndicatorMode.ViewContent), &viewContent)
		if err != nil {
			return result, err
		}
		code, name := viewContent.Dependence.CodeId, viewContent.Dependence.Name
		subjectInfo := subjectMap[releaseIndicatorMode.SubjectId]

		indicatorModelItem := &indicator_common.ClassItem{
			Id:   releaseIndicatorMode.Code,
			Name: releaseIndicatorMode.Name,
			Parent: &indicator_common.ClassItem{
				Id:   code,
				Name: name,
				Parent: &indicator_common.ClassItem{
					Id:   subjectInfo.ID,
					Name: subjectInfo.Name,
				},
			},
		}
		result.Data = append(result.Data, indicatorModelItem)
	}
	return result, nil
}

func (i *IndicatorCommonService) GetPhysicalModelList(ctx context.Context, wrapper *common.IndicatorRepoWrapper, multiCode string) ([]*indicator_common.ModelInfo, []*indicator_common.LinkInfo, error) {
	data := make([]*indicator_common.ModelInfo, 0)
	linkData := make([]*indicator_common.LinkInfo, 0)

	physicalModeAndSubjectList, linkStr, err := wrapper.GetPhysicalModelAndSubjectListWithoutAds(ctx, multiCode, string(global.Prod), int(base.OnShelfApplyStatus))
	if err != nil {
		return data, linkData, err
	}

	Link := gjson.Parse(linkStr).Array()
	for _, linkInfo := range Link {
		joinField := make([]*indicator_common.JoinField, 0)
		fields := linkInfo.Get("join_fields").Array()
		for _, field := range fields {
			joinField = append(joinField, &indicator_common.JoinField{
				Left:  field.Get("left_field").Get("name").String(),
				Right: field.Get("right_value").Get("name").String(),
				// 兼容老接口，老接口只会返回and，新结构无法解析成老接口
				LogicalRelation: "and",
				Operator:        field.Get("operator").String(),
			})
		}
		linkData = append(linkData, &indicator_common.LinkInfo{
			FromId:           linkInfo.Get("left_node_id").String(),
			ToId:             linkInfo.Get("right_node_id").String(),
			JoinType:         linkInfo.Get("join_type").String(),
			JoinQuantityType: linkInfo.Get("join_quantity_type").String(),
			JoinFields:       joinField,
		})
	}

	modelIdList := make([]string, 0)
	for _, info := range physicalModeAndSubjectList {
		data = append(data, &indicator_common.ModelInfo{
			LinkId:      info.LinkID,
			Code:        info.Code,
			Name:        info.Name,
			TableName:   info.PhysicalModelTableName,
			SubjectId:   info.SubjectId,
			SubjectName: info.SubjectName,
			Category:    info.Category,
		})
		modelIdList = append(modelIdList, info.ID)
	}
	physicalModelFieldList, err := wrapper.BatchGetPhysicalModelFieldListById(ctx, modelIdList)
	if err != nil {
		return data, linkData, err
	}

	for index, info := range physicalModeAndSubjectList {
		for _, fieldInfo := range physicalModelFieldList {
			if info.ID == fieldInfo.PhysicalModelId {
				if fieldInfo.FieldBusinessType == common.FieldBusinessTypeDerive { //过滤衍生维度
					continue
				}
				data[index].FieldList = append(data[index].FieldList, &indicator_common.PhysicalModelFieldInfo{
					Name:       fieldInfo.Name,
					NameCn:     fieldInfo.NameCn,
					ParentName: fieldInfo.ParentField,
					FieldType:  common.GetFieldType(fieldInfo.FieldType),
				})
			}
		}
	}
	return data, linkData, nil
}

func (i *IndicatorCommonService) GetModelList(ctx context.Context, req *indicator_common.GetModelListRequest) (*indicator_common.GetModelListResponse, error) {
	result := new(indicator_common.GetModelListResponse)
	result.RequestId = trace.GetTraceId(ctx)
	result.Data = make([]*indicator_common.ModelInfo, 0)
	result.LinkData = make([]*indicator_common.LinkInfo, 0)
	result.ErrCode = errcode.ErrorCodeSuccess
	result.ErrMsg = "成功"

	if req.TenantCode != "" {
		tenantProjectRelInfo, err := i.dapCommonService.GetTenantProjectRel(ctx, req.TenantCode)
		if err != nil {
			return result, err
		}
		if tenantProjectRelInfo.CustomProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CustomProjectCode
		} else if tenantProjectRelInfo.CommonProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CommonProjectCode
		}
	}

	if req.MultiCode == "" {
		return result, errors.New("多维模型code不能为空")
	}

	wrapper, err := i.getIndicatorRepoWrapper(ctx, req.ProjectCode)
	if err != nil {
		return result, err
	}

	physicalModelList, linkData, err := i.GetPhysicalModelList(ctx, wrapper, req.MultiCode)
	if err != nil {
		return result, err
	}
	if len(physicalModelList) != 0 {
		result.Data = append(result.Data, physicalModelList...)
	}
	if len(physicalModelList) != 0 && len(linkData) != 0 {
		result.LinkData = append(result.LinkData, linkData...)
	}

	return result, nil
}

func (i *IndicatorCommonService) GetMultiModelList(ctx context.Context, req *indicator_common.GetMultiModelListRequest) (*indicator_common.GetMultiModelListResponse, error) {
	result := new(indicator_common.GetMultiModelListResponse)
	result.RequestId = trace.GetTraceId(ctx)
	result.Data = make([]*indicator_common.GetMultiModelListResp, 0)
	result.ErrCode = errcode.ErrorCodeSuccess
	result.ErrMsg = "成功"

	if req.TenantCode != "" {
		tenantProjectRelInfo, err := i.dapCommonService.GetTenantProjectRel(ctx, req.TenantCode)
		if err != nil {
			return result, err
		}
		if tenantProjectRelInfo.CustomProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CustomProjectCode
		} else if tenantProjectRelInfo.CommonProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CommonProjectCode
		}
	}

	wrapper, err := i.getIndicatorRepoWrapper(ctx, req.ProjectCode)
	if err != nil {
		return result, err
	}

	subjectIdList := make([]string, 0)
	if req.SubjectId != "" {
		subjectIdList = strings.Split(req.SubjectId, ",")
	}
	subjectList, err := wrapper.GetSubjectListByIDs(ctx, subjectIdList)
	if err != nil {
		return result, err
	}

	multiModeAndSubjectList, err := wrapper.GetMultiModelAndSubjectList(ctx, string(global.Prod), int(base.OnShelfApplyStatus))
	if err != nil {
		return result, err
	}

	for _, subjectInfo := range subjectList {
		info := &indicator_common.GetMultiModelListResp{
			SubjectId:   subjectInfo.ID,
			SubjectName: subjectInfo.Name,
		}
		for _, multiInfo := range multiModeAndSubjectList {
			if multiInfo.SubjectID == info.SubjectId {
				info.MultiModelList = append(info.MultiModelList, &indicator_common.MultiModelInfo{
					Code:   multiInfo.Code,
					Name:   multiInfo.Name,
					NameCn: multiInfo.NameCn,
				})
			}
		}
		if len(info.MultiModelList) == 0 {
			continue
		}
		result.Data = append(result.Data, info)
	}

	return result, nil
}

func (i *IndicatorCommonService) GetModelDetailResult(ctx context.Context, req *ModelDetailQueryReq) (*ModelDetailQueryResp, error) {
	resp := &ModelDetailQueryResp{
		DetailList: make([]*indicator_common.ModelDetailInfo, 0),
	}
	if req.UserCode != "" {
		var err error
		var rsp *psProto.AddPermissionRsp
		rsp, err = i.permissionService.AddPermission(ctx, &psProto.AddPermissionReq{
			SQL:         req.Sql,
			ProjectCode: req.ProjectCode,
			TenantCode:  req.TenantCode,
			AuthParams: &psProto.AuthParams{
				UserCode: req.UserCode,
			},
		})
		if err != nil {
			return resp, err
		}
		req.Sql = rsp.SQL
	}

	data, _, _, err := common.QuerySqlByDataSourceMeta(ctx, req.Resource, req.ProjectCode, req.Sql)
	if err != nil {
		return resp, err
	}

	if data.Data == nil {
		return resp, nil
	}

	jsonData, err := json.Marshal(data.Data)
	if err != nil {
		return resp, err
	}

	dataV := gjson.ParseBytes(jsonData).Array()
	for _, v := range dataV {
		if req.SqlType == SqlTypeDetailCount {
			val := v.Get("total").Int()
			resp.Total = int32(val)
			break
		}
		FieldInfo := &indicator_common.ModelDetailInfo{}
		item := make(map[string]string, 0)
		for _, FieldInfo := range req.FieldList { //查询指定字段值
			name := FieldInfo.FieldName
			if FieldInfo.Alias != "" {
				name = FieldInfo.Alias
			}
			val := v.Get(name).String()
			item[name] = val
		}

		if len(item) == 0 { //为空，说明查询全部字段
			m := v.Map()
			for k, val := range m {
				item[k] = val.String()
			}
		}

		FieldInfo.Item = item
		resp.DetailList = append(resp.DetailList, FieldInfo)
	}

	return resp, nil
}

func (i *IndicatorCommonService) FillModelTableName(ctx context.Context, wrapper *common.IndicatorRepoWrapper, req *indicator_common.QueryModelDetailRequest) error {
	if !req.QueryCondition.IsMultiModel {
		modelInfo, err := wrapper.GetPhysicalModelByCode(ctx, req.QueryCondition.ModelCode, string(global.Prod))
		if err != nil {
			return err
		}
		for index, _ := range req.QueryCondition.FieldList {
			req.QueryCondition.FieldList[index].TableName = modelInfo.PhysicalModelTableName
		}
		for index, _ := range req.QueryCondition.FilterList {
			req.QueryCondition.FilterList[index].TableName = modelInfo.PhysicalModelTableName
		}
		for index, _ := range req.QueryCondition.SortList {
			req.QueryCondition.SortList[index].TableName = modelInfo.PhysicalModelTableName
		}
	} else {
		if len(req.QueryCondition.FieldList) == 0 {
			return errors.New("参数错误,多维模型查询字段为空")
		}
		physicalModelCode := make([]string, 0)
		for _, v := range req.QueryCondition.FieldList {
			physicalModelCode = append(physicalModelCode, v.FieldModelCode)
		}
		for _, v := range req.QueryCondition.FilterList {
			physicalModelCode = append(physicalModelCode, v.FieldModelCode)
		}
		for _, v := range req.QueryCondition.SortList {
			physicalModelCode = append(physicalModelCode, v.FieldModelCode)
		}
		physicalModelList, err := wrapper.BatchGetPhysicalModelInfoByCode(ctx, physicalModelCode, string(global.Prod))
		if err != nil {
			return err
		}
		physicalModelMap := make(map[string]string, 0)
		for _, v := range physicalModelList {
			physicalModelMap[v.Code] = v.PhysicalModelTableName
		}

		for index, v := range req.QueryCondition.FieldList {
			req.QueryCondition.FieldList[index].TableName = physicalModelMap[v.FieldModelCode]
		}
		for index, v := range req.QueryCondition.FilterList {
			req.QueryCondition.FilterList[index].TableName = physicalModelMap[v.FieldModelCode]
		}
		for index, v := range req.QueryCondition.SortList {
			req.QueryCondition.SortList[index].TableName = physicalModelMap[v.FieldModelCode]
		}

	}
	return nil
}

func (i *IndicatorCommonService) QueryModelDetail(ctx context.Context, req *indicator_common.QueryModelDetailRequest) (*indicator_common.QueryModelDetailResponse, error) {
	result := new(indicator_common.QueryModelDetailResponse)
	result.RequestId = trace.GetTraceId(ctx)
	result.Data = &indicator_common.ModelDetailData{}
	result.Data.List = make([]*indicator_common.ModelDetailInfo, 0)
	result.ErrCode = errcode.ErrorCodeSuccess
	result.ErrMsg = "成功"

	var err error
	if req.TenantCode != "" {
		tenantProjectRelInfo, err := i.dapCommonService.GetTenantProjectRel(ctx, req.TenantCode)
		if err != nil {
			return result, err
		}
		if tenantProjectRelInfo.CustomProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CustomProjectCode
		} else if tenantProjectRelInfo.CommonProjectCode != "" {
			req.ProjectCode = tenantProjectRelInfo.CommonProjectCode
		}
	}

	if req.QueryCondition == nil {
		return result, errors.New("查询参数为空")
	}
	if req.QueryCondition.ModelCode == "" {
		return result, errors.New("模型code为空")
	}

	event := fast.StartEvent(pkgFast.CallApi, req.ProjectCode, consts.ADMIN)
	fastRecordReq := &QueryModelDetailReq{
		Project:        req.ProjectCode,
		Tenant:         req.TenantCode,
		ReqParams:      req,
		RespParams:     result,
		ReqTime:        time.Now(),
		ExecuteSql:     []string{},
		SqlCostTimeMap: map[string]string{},
	}
	event.RecordBizParams(GenerateQueryModelDetailBizParams(ctx, fastRecordReq))
	defer func() {
		if err != nil {
			fastRecordReq.Result = "调用失败"
		} else {
			fastRecordReq.Result = "调用成功"
		}
		event.EndEvent(ctx, err)
	}()

	var wrapper *common.IndicatorRepoWrapper
	if wrapper, err = i.getIndicatorRepoWrapper(ctx, req.ProjectCode); err != nil {
		return result, err
	}

	err = i.FillModelTableName(ctx, wrapper, req)
	if err != nil {
		return result, err
	}

	//获取查询资源
	pr, err := i.dapCommonService.GetStorageResourceV3(ctx, req.ProjectCode, req.TenantCode)
	if err != nil {
		return result, err
	}

	projectResourceType := entities.ProjectResourceType(pr.ResourceType)
	queryEngineType, err := common.GetEngineTypeNew(projectResourceType)
	if err != nil {
		return result, err
	}

	transformer := NewTransformer(req, projectResourceType)
	err = transformer.PreCheckAndPrepareData(ctx, wrapper.GetRepo())
	if err != nil {
		return result, err
	}

	//构建明细查询sql
	var buildCtx = context2.NewDetailBuildContext(ctx, transformer, transformer,
		context2.WithDetailFilter(transformer),
		context2.WithDetailSort(transformer),
		context2.WithDetailLimit(transformer),
		context2.WithDetailGlobalFilter(transformer),
	)
	adsViewBuilder := ads_view.NewBuilderForDetail(buildCtx, projectResourceType)
	sql, err := adsViewBuilder.ToSQL()
	if err != nil {
		err = errors.Wrapf(err, "生成明细sql失败")
		return result, err
	}

	//构建明细查询总数sql
	var buildCountCtx = context2.NewDetailBuildContext(ctx, transformer, transformer,
		context2.WithDetailFilter(transformer))
	adsViewBuilder = ads_view.NewBuilderForDetail(buildCountCtx, projectResourceType)
	sqlCount, err := adsViewBuilder.ToSQL()
	if err != nil {
		err = errors.Wrapf(err, "生成明细sql失败")
		return result, err
	}
	sqlCount = fmt.Sprintf("select count(*) as total from ( %s ) x", sqlCount)

	sqlMap := make(map[string]string, 0)
	sqlMap[SqlTypeDetail] = sql
	sqlMap[SqlTypeDetailCount] = sqlCount

	result.Data.Sql = sql
	fastRecordReq.ExecuteSql = append(fastRecordReq.ExecuteSql, sql)
	eg := errgroup.WithContext(ctx)
	for sqlType, sql := range sqlMap {
		sqlType := sqlType
		sql := sql
		eg.Go(func(ctx context.Context) error {
			modelDetailReq := &ModelDetailQueryReq{
				SqlType:     sqlType,
				Sql:         sql,
				EngineType:  queryEngineType,
				ProjectCode: req.ProjectCode,
				FieldList:   req.QueryCondition.FieldList,
				Resource:    pr,
				UserCode:    req.UserCode,
				TenantCode:  req.TenantCode,
			}
			//获取查询结果
			modelDetailResult, err := i.GetModelDetailResult(ctx, modelDetailReq)
			if err != nil {
				return err
			}
			if sqlType == SqlTypeDetailCount {
				result.Data.Total = modelDetailResult.Total
				return nil
			} else {
				result.Data.List = modelDetailResult.DetailList
			}
			return nil
		})
	}

	if err = eg.Wait(); err != nil {
		return result, err
	}
	return result, nil
}
