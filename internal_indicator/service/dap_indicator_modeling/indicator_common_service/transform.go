package indicator_common_service

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	baseProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	common2 "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

var _ injection.DependenceModel = (*Transformer)(nil)
var _ injection.DetailDefinition = (*Transformer)(nil)
var _ injection.DetailSort = (*Transformer)(nil)
var _ injection.QueryLimit = (*Transformer)(nil)

type Transformer struct {
	*injection.DefaultDepModelImpl
	req          *indicator_common.QueryModelDetailRequest
	logger       *logger.Logger
	resourceType entities.ProjectResourceType
	*injection.DefaultIndicatorFilterImpl
	viewContent *proto.ViewContentNew // 应用表ViewContent
}

func NewTransformer(req *indicator_common.QueryModelDetailRequest, resourceType entities.ProjectResourceType) *Transformer {
	return &Transformer{
		req:          req,
		logger:       global.Logger,
		resourceType: resourceType,
	}
}

// PreCheckAndPrepareData 对查询结构进行检查并从数据库获取必要数据
func (s *Transformer) PreCheckAndPrepareData(ctx context.Context, repo *indicator_repository.IndicatorRepository) error {
	if s.req.QueryCondition.ModelCode == "" {
		return errors.New("模型code不能为空")
	}
	var modelCategory baseProto.ModelCategory
	if s.req.QueryCondition.IsMultiModel {
		modelCategory = baseProto.MultiDimCate
	}
	r := indicator_cache.NewIndicatorCache(s.req.ProjectCode)
	s.DefaultDepModelImpl = injection.NewDefaultDepModelImpl(&rpc_call.Dependence{
		CodeId:   s.req.QueryCondition.ModelCode,
		Category: modelCategory,
	}, nil, injection.WithLoadCache(r))

	cache := indicator_cache.NewIndicatorCache(s.req.TenantCode)
	var err error
	if s.req.QueryCondition.IsMultiModel == true {
		s.viewContent, err = s.getAdsModelViewContent(ctx, repo, cache)
		if err != nil {
			return err
		}
	}

	s.DefaultIndicatorFilterImpl = injection.NewDefaultIndicatorFilterImpl(s.viewContent, nil)
	return s.DefaultDepModelImpl.PreLoad(ctx, repo, string(global.Prod))
}

func (s *Transformer) getAdsModelViewContent(ctx context.Context, repo *indicator_repository.IndicatorRepository, cache indicator_cache.IndicatorModelCache) (viewContent *proto.ViewContentNew, err error) {
	allIndicator, err := repo.BatchGetIndicator(ctx, nil, string(global.Prod), 1)
	if err != nil {
		return nil, err
	}

	var indicator *mysql.Indicator
	//找到关联这个多维的指标
	for _, indi := range allIndicator {
		if indi.MultiDimModelCode == s.req.QueryCondition.ModelCode {
			indicator = indi
			break
		}
	}

	if indicator == nil {
		err = errors.New(fmt.Sprintf("找不到依赖当前model_code【%s】 的汇总模型", s.req.QueryCondition.ModelCode))
		return nil, err
	}

	code := indicator.Code
	indicatorModel, err := cache.GetProdAdsViewByCode(ctx, code)
	if err != nil {
		indicatorModel, err = repo.GetIndicatorInfoByCode(ctx, code, string(global.Prod))
		if err != nil {
			err = errors.Wrapf(err, "获取应用表视图模型<%s>失败", code)
			return
		}

		// 未查询到记录
		if indicatorModel == nil || indicatorModel.Code == "" {
			err = errors.New("获取应用表视图模型失败, 模型code为空")
			return
		}
		defer func() {
			cache.SetProdAdsViewByCode(ctx, code, indicatorModel)
		}()
	}

	err = json.Unmarshal([]byte(indicatorModel.ViewContent), &viewContent)
	if err != nil {
		err = errors.Wrapf(err, "解析应用视图ViewContent失败")
		return nil, err
	}

	//fields, err := repo.GetAllIndicatorFieldInfoByIndicatorId(ctx, indicator.ID)
	//if err != nil {
	//	errMsg := fmt.Sprintf("获取指标字段错误,模型id:%s", indicator.ID)
	//	err = errors.Wrapf(err, errMsg)
	//	return
	//}

	//for _, field := range fields {
	//	if len(field.Code) > 0 {
	//		s.IndicatorFieldMap[field.Code] = field
	//	} else {
	//		s.DimFieldMap[field.Name] = field
	//	}
	//}

	return viewContent, nil
}

// ============================== DetailDefinitionImpl ==============================

func (s *Transformer) GetAdditionalField(ctx context.Context) ([]*proto.Field, error) {
	result := []*proto.Field{}
	for _, fieldInfo := range s.req.QueryCondition.FieldList {
		result = append(result, &proto.Field{
			ModelField: proto.ModelField{
				Table: fieldInfo.TableName,
				Field: fieldInfo.FieldName,
				Alias: fieldInfo.Alias,
			},
		})
	}
	return result, nil
}

// ============================== DetailSortImpl ==============================

func (s *Transformer) GetDetailSort(ctx context.Context) ([]*proto.Sort, error) {
	result := []*proto.Sort{}
	for _, sortInfo := range s.req.QueryCondition.SortList {
		direction := proto.Direction_Asc
		if sortInfo.OrderType == "desc" {
			direction = proto.Direction_Desc
		}
		result = append(result, &proto.Sort{
			Field: &proto.Field{
				ModelField: proto.ModelField{
					Table: sortInfo.TableName,
					Field: sortInfo.FieldName,
				},
			},
			Direction: direction,
		})
	}
	return result, nil
}

// ============================== QueryLimitImpl ==============================

const DEFAULT_LIMIT = 500

func (s *Transformer) GetLimit(ctx context.Context) (*proto.Limit, error) {
	result := new(proto.Limit)
	if s.req.QueryCondition.LimitInfo == nil {
		return result, nil
	}
	result.Limit = int64(s.req.QueryCondition.LimitInfo.Limit)
	result.Offset = int64(s.req.QueryCondition.LimitInfo.Offset)
	if result.Limit <= 0 {
		result.Limit = DEFAULT_LIMIT
	}

	return result, nil
}

// ============================== DetailFilterImpl ==============================

func (s *Transformer) GetDetailFilter(ctx context.Context) ([]*proto.Filter, error) {
	result := make([]*proto.Filter, 0)
	filter := proto.Filter{}
	for _, filterInfo := range s.req.QueryCondition.FilterList {
		subFilter := &proto.Filter{}
		subFilter.Field = &proto.Field{
			ModelField: proto.ModelField{
				Field: filterInfo.FieldName,
				Table: filterInfo.TableName,
			},
		}

		if proto.AssociateType(filterInfo.AssociatePre) == proto.AND {
			subFilter.Associate = proto.AND
		} else if proto.AssociateType(filterInfo.AssociatePre) == proto.OR {
			subFilter.Associate = proto.OR
		} else {
			subFilter.Associate = proto.NoAssociate
		}
		subFilter.Operate = proto.OperateType(filterInfo.Operate)
		subFilter.Value.Value = filterInfo.Val
		subFilter.Value.Type = proto.ValueType(common2.ValueTypes[int(filterInfo.ValType)])
		filter.SubFilter = append(filter.SubFilter, subFilter)
	}
	if len(filter.SubFilter) != 0 {
		result = append(result, &filter)
	}

	return result, nil
}

//func (s *Transformer) GetGlobalFilter(ctx context.Context) (map[string][]*proto.Filter, error) {
//	return s.GetGlobalFilter(ctx)
//}
