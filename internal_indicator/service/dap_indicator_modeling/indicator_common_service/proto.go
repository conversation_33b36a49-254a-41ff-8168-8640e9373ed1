package indicator_common_service

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type ModelDetailQueryReq struct {
	SqlType         string
	Sql             string
	EngineType      entities.ProjectResourceType
	ProjectCode     string
	FieldList       []*indicator_common.FieldInfo
	TablePermission map[string]string
	TenantCode      string
	Resource        *proto.ResourceInfo
	UserCode        string
	UserID          string
	NeedRowFilter   bool
}

type ModelDetailQueryResp struct {
	Total      int32
	DetailList []*indicator_common.ModelDetailInfo
}

type IndicatorDependenceModelFieldReq struct {
	ModelCodeList         []string
	TenantCode            string
	ProjectCode           string
	TenantStandardProject string
	IsIndividual          bool
}
type ContentMapReq struct {
	AdsViewCodeList []string
	Repo            *indicator_repository.IndicatorRepository
}
