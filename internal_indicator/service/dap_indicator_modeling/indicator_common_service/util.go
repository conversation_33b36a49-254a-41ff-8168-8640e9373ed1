package indicator_common_service

import (
	"context"
	//"encoding/json"
	"fmt"
	json "github.com/json-iterator/go"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
	"time"

	"github.com/pkg/errors"
	baseModel "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/ads_common_service"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errcode"
	db_utils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/saas_db"
)

type getAnalyticalFieldInfosReq struct {
	projectCode   string
	fieldCode     string
	viewContent   string
	modelName     string
	modelNameEn   string
	dimensionList []*indicator_repository.IndicatorModelAndFieldInfo
}

func (i *IndicatorCommonService) getAnalyticalFieldInfos(ctx context.Context, req *getAnalyticalFieldInfosReq) ([]*indicator_common.AnalyticalFieldInfo, error) {
	sctx, sp := trace_utils.StartSpanWithContext(ctx, "getAnalyticalFieldInfos")
	sp.Name("method: getAnalyticalFieldInfos")
	defer sp.End()

	localCacheBaseKey := "getAnalyticalFieldInfos"
	value, found := i.localCache.Get(ctx, fmt.Sprintf("%s_%s_%s", localCacheBaseKey, req.projectCode, req.fieldCode))
	if found {
		return value.([]*indicator_common.AnalyticalFieldInfo), nil
	}

	analyticalFieldInfos := make([]*indicator_common.AnalyticalFieldInfo, 0)
	if req.viewContent == "" {
		return analyticalFieldInfos, nil
	}

	timeCostFn := trace_utils.TraceTimeCost(sctx, "Unmarshal req.viewContent")
	viewContentNew := proto.ViewContentNew{}
	err := json.Unmarshal([]byte(req.viewContent), &viewContentNew)
	timeCostFn()
	if err != nil {
		return analyticalFieldInfos, err
	}

	dimViewDataMap := make(map[string]*baseModel.FieldViewModel, 0)
	for _, dimension := range req.dimensionList {
		dimViewData := &baseModel.FieldViewModel{}
		timeCostFn = trace_utils.TraceTimeCost(sctx, "Unmarshal dimension.DimViewData")
		json.Unmarshal([]byte(dimension.DimViewData), dimViewData)
		timeCostFn()
		viewContentNew.Dimensions = append(viewContentNew.Dimensions, proto.Dimension{
			FieldCode: dimension.FieldCode,
			Alias:     dimension.Name,
			PropName:  dimension.Name,
			NameCn:    dimension.NameCn,
			ObjName:   dimension.ModelNameEn,
			ViewModel: dimViewData,
		})
		dimViewDataMap[dimension.FieldCode] = dimViewData
	}
	viewContentBytes, _ := json.Marshal(viewContentNew)
	service := ads_common_service.NewAdsCommonService()
	rsp, err := service.GetPublicDimTable(sctx, &ads_common.GetPublicDimTableReq{
		ProjectCode:    req.projectCode,
		IndicatorCodes: []string{req.fieldCode},
		ViewContent:    string(viewContentBytes),
	})

	if rsp.ErrCode != errcode.ErrorCodeSuccess {
		return nil, errors.New(rsp.ErrMsg)
	}
	if rsp.Data == nil {
		return analyticalFieldInfos, nil
	}
	for _, dimension := range rsp.Data.Dimensions {
		analyticalFieldInfos = append(analyticalFieldInfos, &indicator_common.AnalyticalFieldInfo{
			Table:     req.modelNameEn,
			TableCn:   req.modelName,
			Name:      dimension.NameCn,
			EnName:    dimension.Name,
			FieldCode: dimension.Code,
			Type:      -1,
		})
	}
	i.localCache.Set(ctx, fmt.Sprintf("%s_%s_%s", localCacheBaseKey, req.projectCode, req.fieldCode), analyticalFieldInfos, 5*time.Minute)
	return analyticalFieldInfos, nil
}

func (i *IndicatorCommonService) getIndicatorRepoWrapper(ctx context.Context, projectCode string) (
	*common.IndicatorRepoWrapper, error) {
	db, err := db_utils.GetProjectDB(ctx, projectCode)
	if err != nil {
		return nil, err
	}
	repo := indicator_repository.NewIndicatorRepository(db, projectCode)
	cache := indicator_cache.NewIndicatorCache(projectCode)
	return common.NewIndicatorRepoWrapper(repo, cache), nil
}
