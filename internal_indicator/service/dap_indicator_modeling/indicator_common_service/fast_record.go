package indicator_common_service

import (
	"context"
	fastPkg "gitlab.mypaas.com.cn/dmp/gopkg/bigdata/fast"
	"gitlab.mypaas.com.cn/fast/tracker-go/trace"
	"strings"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/fast/event_report/base"
)

type IndicatorListBizParams struct {
	Project    string
	Tenant     string
	ReqParams  interface{}
	RespParams interface{}
	ReqTime    time.Time
	Result     string
}

type IndicatorClassBizParams struct {
	Project    string
	Tenant     string
	ReqParams  interface{}
	RespParams interface{}
	ReqTime    time.Time
	Result     string
}

type QueryModelDetailReq struct {
	Project          string
	Tenant           string
	ReqParams        interface{}
	RespParams       interface{}
	ReqTime          time.Time
	ExecuteSql       []string
	SqlExecStartTime time.Time
	SqlExecEndTime   time.Time
	Result           string
	SqlCostTimeMap   map[string]string
}

func GenerateGetIndicatorClassBizParams(ctx context.Context, req *IndicatorClassBizParams) base.BizParamsGenerator {
	return func() map[string]interface{} {
		res := fastPkg.ApiCommonBizParam{
			TraceID:       trace.GetTraceId(ctx),
			TenantCode:    req.Tenant,
			ApiName:       "获取指标类目列表",
			ApiType:       fastPkg.IndicatorCardApi,
			RequestMethod: "GET",
			LogTime:       time.Now().Format("2006-01-02 15:04:05.000000"),
			RequestPath:   "/openapi/indicator/indicator_common/class_list",
			RequestParams: req.ReqParams,
			RequestTime:   req.ReqTime.Format("2006-01-02 15:04:05.000000"),
			ResponseTime:  time.Now().Format("2006-01-02 15:04:05.000000"),
			CostTime:      time.Now().Sub(req.ReqTime).Milliseconds(),
		}
		return res.ToFlatMap()
	}
}

func GenerateGetIndicatorListBizParams(ctx context.Context, req *IndicatorListBizParams) base.BizParamsGenerator {
	return func() map[string]interface{} {
		res := fastPkg.ApiCommonBizParam{
			TraceID:       trace.GetTraceId(ctx),
			TenantCode:    req.Tenant,
			ApiName:       "获取指标列表",
			ApiType:       fastPkg.IndicatorCardApi,
			RequestMethod: "GET",
			LogTime:       time.Now().Format("2006-01-02 15:04:05.000000"),
			RequestPath:   "/openapi/indicator/indicator_common/indicator_list",
			RequestParams: req.ReqParams,
			RequestTime:   req.ReqTime.Format("2006-01-02 15:04:05.000000"),
			ResponseTime:  time.Now().Format("2006-01-02 15:04:05.000000"),
			CostTime:      time.Now().Sub(req.ReqTime).Milliseconds(),
		}
		return res.ToFlatMap()
	}
}

func GenerateQueryModelDetailBizParams(ctx context.Context, req *QueryModelDetailReq) base.BizParamsGenerator {
	return func() map[string]interface{} {
		res := fastPkg.ApiCommonBizParam{
			TraceID:        trace.GetTraceId(ctx),
			TenantCode:     req.Tenant,
			ApiName:        "模型取数",
			ApiType:        fastPkg.IndicatorCardApi,
			RequestMethod:  "POST",
			LogTime:        time.Now().Format("2006-01-02 15:04:05.000000"),
			RequestPath:    "/openapi/indicator/indicator_common/query_model_detail",
			RequestParams:  req.ReqParams,
			RequestTime:    req.ReqTime.Format("2006-01-02 15:04:05.000000"),
			ResponseTime:   time.Now().Format("2006-01-02 15:04:05.000000"),
			CostTime:       time.Now().Sub(req.ReqTime).Milliseconds(),
			ExecuteSql:     strings.Join(req.ExecuteSql, ","),
			ExecuteSqlTime: req.SqlExecEndTime.Sub(req.SqlExecStartTime).Milliseconds(),
			Extra: map[string]interface{}{
				"sql_cost_time": req.SqlCostTimeMap,
			},
		}
		return res.ToFlatMap()
	}
}
