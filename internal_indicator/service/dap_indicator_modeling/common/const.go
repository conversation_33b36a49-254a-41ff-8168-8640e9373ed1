package common

import "gitlab.mypaas.com.cn/dmp/gopkg/build_plan"


const (
	TimeTypeYesDay      = 1 //昨日
	TimeTypeLastDays    = 2 //过去N天
	TimeTypeThisWeek    = 3 //本周
	TimeTypeThisMonth   = 4 //本月
	TimeTypeThisQuarter = 5 //本季
	TimeTypeThisYear    = 6 //本年
)

const (
	FieldTypeOther  = 0
	FieldTypeString = 1
	FieldTypeInt    = 2
	FieldTypeTime   = 3
	FieldTypFloat   = 4
)

const (
	//最小时间统计粒度
	EmptyType   = 0 //空
	TotalType   = 1 //累计
	DayType     = 2
	WeekType    = 3
	MonthType   = 4
	QuarterType = 5
	YearType    = 6
)

const (
	ChartTypeIndicator         = 1 //指标
	ChartTypeIndicatorTrend    = 2 //指标趋势
	ChartTypeIndicatorPercent  = 3 //指标占比
	ChartTypeDimensionAnalysis = 4 //维度分析
	ChartTypeIndicatorRank     = 5 //指标排行
	ChartTypeIndicatorBatch    = 6
)

type BusinessFieldType string

const (
	BusinessFieldTypeText             BusinessFieldType = "text"                // 文本
	BusinessFieldTypeMultiText        BusinessFieldType = "multi_text"          // 多行文本
	BusinessFieldTypeMultiTextNoLimit BusinessFieldType = "multi_text_no_limit" // 多行文本(无限制)
	BusinessFieldTypeInt              BusinessFieldType = "int"                 //整型
	BusinessFieldTypeBigInt           BusinessFieldType = "bigint"              //长整型
	BusinessFieldTypeDecimal          BusinessFieldType = "decimal"             //数值
	BusinessFieldTypeDate             BusinessFieldType = "date"                //日期
	BusinessFieldTypeDateTime         BusinessFieldType = "datetime"            //时间
	BusinessFieldTypeTimestamp        BusinessFieldType = "timestamp"           //时间戳
	BusinessFieldTypeGuid             BusinessFieldType = "guid"                //guid
	BusinessFieldTypeGuids            BusinessFieldType = "guids"               //guid数组
)

var FieldTypes = map[BusinessFieldType]int32{
	BusinessFieldTypeText:             FieldTypeString,
	BusinessFieldTypeMultiText:        FieldTypeString,
	BusinessFieldTypeMultiTextNoLimit: FieldTypeString,
	BusinessFieldTypeGuid:             FieldTypeString,
	BusinessFieldTypeGuids:            FieldTypeString,
	BusinessFieldTypeInt:              FieldTypeInt,
	BusinessFieldTypeBigInt:           FieldTypeInt,
	BusinessFieldTypeDateTime:         FieldTypeTime,
	BusinessFieldTypeDate:             FieldTypeTime,
	BusinessFieldTypeTimestamp:        FieldTypeTime,
	BusinessFieldTypeDecimal:          FieldTypFloat,
}

var ValueTypes = map[int]build_plan.ValueType{
	FieldTypeOther:  build_plan.ValueType_StringValue,
	FieldTypeString: build_plan.ValueType_StringValue,
	FieldTypeInt:    build_plan.ValueType_IntegerValue,
	FieldTypeTime:   build_plan.ValueType_StringValue,
	FieldTypFloat:   build_plan.ValueType_FloatValue,
}

const (
	OrderTypeDesc = "desc"
	OrderTypeAsc  = "asc"
)

const (
	FieldBusinessTypeNormal = 0
	FieldBusinessTypeDerive = 1
)
