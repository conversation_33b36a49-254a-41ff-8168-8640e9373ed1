package common

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/copy_helper"
	"time"

	dapCommonProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"

	baseModels "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	resource "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/time_utils"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_card"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/engine"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/engine/iface"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

type IndicatorBuildSqlInfo struct {
	IndicatorFieldCn             string
	IndicatorField               string
	IndicatorFieldAlias          string
	IndicatorTable               string //后面作废掉
	IndicatorTimeField           string
	IndicatorTimeTable           string
	IndicatorId                  string
	IndicatorCode                string
	IndicatorFieldCode           string
	IndicatorBusinessCode        string
	IndicatorModelCode           string
	IndicatorType                int32
	IndicatorMinimumTimeParticle int32
	OrgCode                      string
	Project                      string
	EngineType                   resource.ProjectResourceType
	ProjectResourceType          entities.ProjectResourceType
	ViewName                     string
	ViewContent                  string
	Repo                         *indicator_repository.IndicatorRepository
	IndicatorFilters             []*indicator_card.IndicatorFilter
	CalculateConditionList       []*indicator_card.IndicatorCalculateCondition
	FilterIndicatorInfoMap       map[string]*indicator_repository.IndicatorModelAndFieldInfo
	DimViewDataMap               map[string]*baseModels.FieldViewModel
}

func GetEngineTypeNew(projectResourceType entities.ProjectResourceType) (resource.ProjectResourceType, error) {
	switch projectResourceType {
	case entities.StarRocksSaaSResourceType:
		return resource.StarRocksSaaSResourceType, nil
	case entities.StarRocksResourceType:
		return resource.StarRocksResourceType, nil
	case entities.RDSResourceType:
		return resource.RDSResourceType, nil
	case entities.RDSSaaSResourceType:
		return resource.RDSSaaSResourceType, nil
	case entities.DamengSaaSResourceType:
		return resource.DamengSaaSResourceType, nil
	default:
		return resource.RDSResourceType, fmt.Errorf("未知的查询资源类型: %s", projectResourceType)
	}

}

func QuerySqlByDataSourceMeta(ctx context.Context, queryResource *dapCommonProto.ResourceInfo, project string, sql string) (*iface.SyncQueryResponse, time.Duration, string, error) {
	engineManager := engine.NewEngineManager()
	queryEngine, err := engineManager.GetQueryEngine()
	if err != nil {
		return nil, 0, sql, err
	}
	defer time_utils.TimeCost(ctx, fmt.Sprintf("engineType:%s, QuerySqlByDataSourceMeta:%s", queryResource.ResourceType, sql))()
	start := time.Now()
	resp, err := queryEngine.SyncQuery(ctx, iface.SyncQueryRequest{ProjectCode: project, Sql: sql, Resource: queryResource})
	end := time.Now()
	if err != nil {
		return nil, end.Sub(start), sql, err
	}
	return &resp, end.Sub(start), sql, nil
}

func GetTimeDateRange(timeType int32, dayNum int) []string {
	dateRange := []string{}
	switch timeType {
	case TimeTypeYesDay:
		dateRange = time_utils.GetYesterDayDateRange()
	case TimeTypeLastDays:
		dateRange = time_utils.GetLastDaysDateRang(dayNum)
	case TimeTypeThisWeek:
		dateRange = time_utils.GetCurWeekDateRange()
	case TimeTypeThisMonth:
		dateRange = time_utils.GetCurMonthDateRange()
	case TimeTypeThisQuarter:
		dateRange = time_utils.GetCurQuarterDateRange()
	case TimeTypeThisYear:
		dateRange = time_utils.GetCurYearDateRange()
	}
	return dateRange
}

func GetViewContentNew(viewContent string) (*proto.ViewContentNew, error) {
	viewContentNew := &proto.ViewContentNew{}
	err := json.Unmarshal([]byte(viewContent), viewContentNew) // 注意 这里的结构体要用指针, 原因 值类型,引用类型的问题
	if err != nil {
		return nil, err
	}
	return viewContentNew, nil
}

func GetCustomFieldDimension(fieldCode string, alias string, customFieldDimensions []rpc_call.Prop) (customFieldDimension rpc_call.Prop) {
	for _, v := range customFieldDimensions {
		if v.FieldCode == fieldCode {
			v.Alias = alias
			return v
		}
	}
	return
}

func GetFieldType(strFieldType string) int32 {
	fieldType, ok := FieldTypes[BusinessFieldType(strFieldType)]
	if ok {
		return fieldType
	}
	return FieldTypeOther
}

func FieldViewModelMapToDimViewData(viewModel *baseModels.FieldViewModel) *proto.DimViewData {
	if viewModel == nil {
		return nil
	}
	return &proto.DimViewData{
		GetValueType:        mapDimViewDataType(viewModel.CalcType),
		FieldAssignData:     mapDimViewDataFieldAssign(viewModel.DirectMappingRule),
		CondTagData:         mapDimViewDataCondTag(viewModel.ConditionalMappingRule),
		BuiltinFuncData:     mapDimViewDataBuiltinFunc(viewModel.BuiltinFuncRule),
		AdvanceDatetimeData: mapDimViewDataAdvanceDate(viewModel.AdvanceDatetimeData),
	}
}

func mapDimViewDataType(calcType baseModels.CalcType) proto.DimGetDataType {
	var ans = proto.FieldAssign
	switch calcType {
	case baseModels.DirectMapping:
		ans = proto.FieldAssign
	case baseModels.ConditionalMapping:
		ans = proto.CondTag
	case baseModels.BuiltinFunc:
		ans = proto.BuiltinFunction
	case baseModels.AdvanceDatetime:
		ans = proto.AdvanceDatetime
	}
	return ans
}

func mapDimViewDataFieldAssign(rule baseModels.DirectMappingRule) proto.FieldAssignData {
	return proto.FieldAssignData{
		QuoteTableName:       rule.DataSourceTableName,
		QuoteTableNameCn:     rule.DataSourceTableNameCn,
		QuoteFieldName:       rule.DataSourceFieldName,
		QuoteFieldNameCn:     rule.DataSourceFieldNameCn,
		QuoteFieldType:       rule.DataSourceFieldType,
		IsAutoCompletionDate: rule.IsAutoCompletionDate,
	}
}

func mapDimViewDataCondTag(rule baseModels.ConditionalMappingRule) proto.CondTagData {
	var ans = proto.CondTagData{
		QuoteTableName:   rule.DataSourceTableName,
		QuoteTableNameCN: rule.DataSourceTableNameCn,
		QuoteFieldName:   rule.DataSourceFieldName,
		QuoteFieldNameCN: rule.DataSourceFieldNameCn,
		QuoteFieldType:   rule.DataSourceFieldType,
		GroupList:        make([]proto.Group, 0),
	}
	for _, group := range rule.GroupList {
		var where []proto.WhereInfo
		copy_helper.MustCopy(&where, &group.Where.Conditions)
		var item = proto.Group{
			GroupID:   group.Id,
			GroupName: group.Name,
			GroupType: proto.GroupType(group.Type),
			Where: struct {
				Relation proto.AssociateType `json:"relation"`
				Info     []proto.WhereInfo   `json:"info"`
			}{
				Relation: proto.AssociateType(group.Where.Relation),
				Info:     where,
			},
		}
		ans.GroupList = append(ans.GroupList, item)
	}
	return ans
}

func mapDimViewDataAdvanceDate(rule baseModels.AdvanceDatetimeData) proto.AdvanceDatetimeData {
	var ans = proto.AdvanceDatetimeData{}
	copy_helper.MustCopy(&ans, &rule)
	return ans
}

func mapDimViewDataBuiltinFunc(rule baseModels.BuiltinFuncRule) proto.BuiltinFuncData {
	var builtinFunc = proto.BuiltinFuncType(rule.BuiltinFunc)
	return proto.BuiltinFuncData{
		QuoteTableName:       rule.DataSourceTableName,
		QuoteTableNameCn:     rule.DataSourceTableNameCn,
		QuoteFieldName:       rule.DataSourceFieldName,
		QuoteFieldNameCn:     rule.DataSourceFieldNameCn,
		QuoteFieldType:       rule.DataSourceFieldType,
		IsAutoCompletionDate: rule.IsAutoCompletionDate,
		BuiltinFunc:          builtinFunc,
	}
}
