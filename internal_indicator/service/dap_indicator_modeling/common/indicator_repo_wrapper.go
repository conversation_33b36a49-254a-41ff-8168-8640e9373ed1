package common

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/di_helper"
	"strings"
	"time"
)

const LocalCacheExpiration = 5 * time.Minute

type IndicatorRepoWrapper struct {
	repo       *indicator_repository.IndicatorRepository
	cache      *indicator_cache.IndicatorCache
	localCache *cache.LocalCache
}

func NewIndicatorRepoWrapper(repo *indicator_repository.IndicatorRepository,
	indicatorCache *indicator_cache.IndicatorCache) *IndicatorRepoWrapper {
	return &IndicatorRepoWrapper{
		repo:       repo,
		cache:      indicatorCache,
		localCache: di_helper.LocalCache(),
	}
}

func (w *IndicatorRepoWrapper) GetRepo() *indicator_repository.IndicatorRepository {
	return w.repo
}

func (w *IndicatorRepoWrapper) GetAllSubjectList(ctx context.Context) (ans []*mysql.Subject, err error) {
	// 首先获取缓存
	if ans, err = w.cache.GetSubject(ctx, []string{}); err == nil {
		return
	}
	ans, err = w.repo.GetAllSubjectList(ctx)
	if err != nil {
		return
	}
	// 设置缓存
	w.cache.SetSubject(ctx, []string{}, ans)
	return
}

func (w *IndicatorRepoWrapper) GetSubjectListByIDs(ctx context.Context, subjectIDs []string) (ans []*mysql.Subject, err error) {
	// 首先获取缓存
	if ans, err = w.cache.GetSubject(ctx, subjectIDs); err == nil {
		return
	}
	ans, err = w.repo.GetSubjectListByIDs(ctx, subjectIDs)
	if err != nil {
		return
	}
	// 设置缓存
	w.cache.SetSubject(ctx, subjectIDs, ans)
	return
}

func (w *IndicatorRepoWrapper) GetPhysicalModelAndSubjectListWithoutAds(ctx context.Context, multiCode string,
	environment string, applyStatus int) (models []*mysql.PhysicalModelAndSubject, linkData string, err error) {
	if models, linkData, err = w.cache.GetPhysicalModelWithSubjectListOfMultiDim(ctx,
		multiCode, environment, applyStatus); err == nil {
		return
	}
	models, linkData, err = w.repo.GetPhysicalModelAndSubjectListWithoutAds(ctx, multiCode, environment, applyStatus)
	if err != nil {
		return
	}
	w.cache.SetPhysicalModelWithSubjectListOfMultiDim(ctx, multiCode, environment, applyStatus, models, linkData)
	return
}

func (w *IndicatorRepoWrapper) GetPhysicalModelByCode(ctx context.Context, code string, environment string) (ans *mysql.PhysicalModel, err error) {
	var models []*mysql.PhysicalModel
	if models, err = w.cache.GetPhysicalModelByCodes(ctx, []string{code}, environment); err == nil && len(models) > 0 {
		ans = models[0]
		return
	}
	ans, err = w.repo.GetPhysicalModelByCode(ctx, code, environment)
	if err != nil {
		return
	}
	w.cache.SetPhysicalModelByCodes(ctx, []string{code}, environment, []*mysql.PhysicalModel{ans})
	return
}

func (w *IndicatorRepoWrapper) BatchGetPhysicalModelInfoByCode(ctx context.Context, codes []string, environment string) (ans []*mysql.PhysicalModel, err error) {
	if ans, err = w.cache.GetPhysicalModelByCodes(ctx, codes, environment); err == nil {
		return
	}
	ans, err = w.repo.BatchGetPhysicalModelInfoByCode(ctx, codes, environment)
	if err != nil {
		return
	}
	w.cache.SetPhysicalModelByCodes(ctx, codes, environment, ans)
	return
}

func (w *IndicatorRepoWrapper) BatchGetPhysicalModelFieldListById(ctx context.Context, ids []string) (ans []*mysql.PhysicalModelField, err error) {
	if ans, err = w.cache.GetPhysicalModelFieldsByIds(ctx, ids); err == nil {
		return
	}
	ans, err = w.repo.BatchGetPhysicalModelFieldListById(ctx, ids)
	if err != nil {
		return
	}
	w.cache.SetPhysicalModelFieldsByIds(ctx, ids, ans)
	return
}

func (w *IndicatorRepoWrapper) GetMultiModelAndSubjectList(ctx context.Context, environment string, applyStatus int) (ans []*mysql.MultiModelAndSubject, err error) {
	if ans, err = w.cache.GetMultiModelAndSubjectList(ctx, environment, applyStatus); err == nil {
		return
	}
	ans, err = w.repo.GetMultiModelAndSubjectList(ctx, environment, applyStatus)
	if err != nil {
		return
	}
	w.cache.SetMultiModelAndSubjectList(ctx, environment, applyStatus, ans)
	return
}

// =====================指标的统一做本地缓存=======================

func (w *IndicatorRepoWrapper) GetIndicatorModelAndFieldList(ctx context.Context, modelCodes []string,
	environment string) (ans []*indicator_repository.IndicatorModelAndFieldInfo, err error) {
	// 内部做了本地缓存，这里不再设置多余的redis缓存
	ans, err = w.repo.GetIndicatorModelAndFieldList(ctx, modelCodes, environment)
	return
}

func (w *IndicatorRepoWrapper) GetIndicatorModelCodeByIndicatorCodes(ctx context.Context, indicatorCodes []string,
	environment string) (ans []string, err error) {
	var suffix = strings.Join(indicatorCodes, ",")
	if len(suffix) < 1 {
		suffix = "all"
	}
	var key = fmt.Sprintf("GetIndicatorModelCodeByIndicatorCodes_%s_%s_%s", w.repo.Project, environment, suffix)
	value, found := w.localCache.Get(ctx, key)
	if found {
		var ok bool
		if ans, ok = value.([]string); ok {
			return
		}
	}
	ans, err = w.repo.GetIndicatorModelCodeByIndicatorCodes(ctx, indicatorCodes, environment)
	if err != nil {
		return
	}
	w.localCache.Set(ctx, key, ans, LocalCacheExpiration)
	return
}

func (w *IndicatorRepoWrapper) BatchGetIndicator(ctx context.Context, codes []string, environment string,
	applyStatus int) (ans []*mysql.Indicator, err error) {
	var suffix = strings.Join(codes, ",")
	if len(suffix) < 1 {
		suffix = "all"
	}
	var key = fmt.Sprintf("BatchGetIndicator_%s_%s_%s", w.repo.Project, environment, suffix)
	value, found := w.localCache.Get(ctx, key)
	if found {
		ans = make([]*mysql.Indicator, 0)
		if deCodeErr := json.Unmarshal(value.([]byte), &ans); deCodeErr == nil {
			return
		} else {
			global.Logger.Error("BatchGetIndicator unmarshal error", deCodeErr)
		}
	}
	ans, err = w.repo.BatchGetIndicator(ctx, codes, environment, applyStatus)
	if err != nil {
		return
	}
	if ansBytes, enCodeErr := json.Marshal(ans); enCodeErr == nil {
		w.localCache.Set(ctx, key, ansBytes, LocalCacheExpiration)
	} else {
		global.Logger.Error("BatchGetIndicator marshal error", enCodeErr)
	}
	return
}

func (w *IndicatorRepoWrapper) GetIndicatorModelAndFieldInfoByCode(ctx context.Context, code string,
	environment string) (ans *indicator_repository.IndicatorModelAndFieldInfo, err error) {
	var key = fmt.Sprintf("GetIndicatorModelAndFieldInfoByCode_%s_%s_%s", w.repo.Project,
		environment, code)
	value, found := w.localCache.Get(ctx, key)
	if found {
		ans = &indicator_repository.IndicatorModelAndFieldInfo{}
		if deCodeErr := json.Unmarshal(value.([]byte), ans); deCodeErr == nil {
			return
		} else {
			global.Logger.Error("GetIndicatorModelAndFieldInfoByCode unmarshal error", deCodeErr)
		}
	}
	ans, err = w.repo.GetIndicatorModelAndFieldInfoByCode(ctx, code, environment)
	if err != nil {
		return
	}
	if ansBytes, enCodeErr := json.Marshal(ans); enCodeErr == nil {
		w.localCache.Set(ctx, key, ansBytes, LocalCacheExpiration)
	} else {
		global.Logger.Error("GetIndicatorModelAndFieldInfoByCode marshal error", enCodeErr)
	}
	return
}

func (w *IndicatorRepoWrapper) GetIndicatorModelCodeByCodesAndKeyWords(ctx context.Context, codes []string, keywords []string, environment string) (ans []string, err error) {
	var keywordStr = strings.Join(keywords, ",")
	if len(keywordStr) < 1 {
		keywordStr = "null"
	}
	var codeStr = strings.Join(codes, ",")
	if len(codeStr) < 1 {
		codeStr = "all"
	}
	var key = fmt.Sprintf("GetIndicatorModelCodeByCodesAndKeyWords_%s_%s_%s_%s", w.repo.Project, environment, keywordStr, codeStr)
	value, found := w.localCache.Get(ctx, key)
	if found {
		var ok bool
		if ans, ok = value.([]string); ok {
			return
		}
	}
	ans, err = w.repo.GetIndicatorModelCodeByCodesAndKeyWords(ctx, codes, keywords, environment)
	if err != nil {
		return
	}
	w.localCache.Set(ctx, key, ans, LocalCacheExpiration)
	return
}

func (w *IndicatorRepoWrapper) GetIndicatorListByCodes(ctx context.Context, codes []string, environment string) (ans []*mysql.Indicator, err error) {
	var codeStr = strings.Join(codes, ",")
	if len(codeStr) < 1 {
		codeStr = "all"
	}
	var key = fmt.Sprintf("GetIndicatorListByCodes_%s_%s_%s", w.repo.Project, environment, codeStr)
	value, found := w.localCache.Get(ctx, key)
	if found {
		ans = make([]*mysql.Indicator, 0)
		if deCodeErr := json.Unmarshal(value.([]byte), &ans); deCodeErr == nil {
			return
		} else {
			global.Logger.Error("GetIndicatorListByCodes unmarshal error", deCodeErr)
		}
	}
	ans, err = w.repo.GetIndicatorListByCodes(ctx, codes, environment)
	if err != nil {
		return
	}
	if ansBytes, enCodeErr := json.Marshal(ans); enCodeErr == nil {
		w.localCache.Set(ctx, key, ansBytes, LocalCacheExpiration)
	} else {
		global.Logger.Error("GetIndicatorListByCodes marshal error", enCodeErr)
	}
	return
}
