{"query": {"dimensions": [{"alias": "id", "expression": {"field_content": {"field_name": "id", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}, {"alias": "datax_code", "expression": {"field_content": {"field_name": "datax_code", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}, {"alias": "t<PERSON><PERSON><PERSON>", "expression": {"field_content": {"field_name": "t<PERSON><PERSON><PERSON>", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}], "filter": {}, "having_filter": {}, "indicators": [], "limit": {"limit": 150, "offset": null}, "sorts": [{"ordering": "Asc", "query_item": {"alias": "", "expression": {"field_content": {"field_name": "tinteger", "field_type": "Measure", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}}, {"ordering": "Desc", "query_item": {"alias": "id", "expression": {"field_content": {"alias_name": "id", "field_type": "<PERSON><PERSON>", "op_params": [{"type": "Value", "value_content": {"type": "StringValue", "value": "101"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1500"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "008"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1499"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "009"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1498"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "0"}}], "op_type": "CaseWhenEqual"}, "type": "Field"}}}]}, "mode": "Detail", "engine": "StarRocks", "pulsar_code": "model_zn", "class_id": "f6d01af9-bef4-4d97-9d0c-e9776cdff692", "project_code": "model_zn", "model_code": "f6d01af9-bef4-4d97-9d0c-e9776cdff692", "with_variables": null}