{"query": {"indicators": [{"alias": "SaleAmount", "expression": {"type": "Field", "field_content": {"field_type": "Indicator", "code": "130456bc-9aa8-3518-8e68-7b01fda68e0b"}}}], "dimensions": [{"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_ys_proj<PERSON><PERSON><PERSON>rowbizdata", "field_name": "BuCode", "op_type": "NoFieldOpType"}}, "alias": "BuCode"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_ys_proj<PERSON><PERSON><PERSON>rowbizdata", "field_name": "ProjName", "op_type": "NoFieldOpType"}}, "alias": "ProjName"}], "filter": {}, "having_filter": {}, "sorts": [], "limit": {}, "table_name": "dws_ys_proj<PERSON><PERSON><PERSON>rowbizdata", "mode": "Agg"}, "engine": "StarRocks_SaaS", "tenant_code": "shuxin15", "project_code": "sprint_1_5_hi", "with_variables": null}