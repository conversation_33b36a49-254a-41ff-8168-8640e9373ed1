{"query": {"dimensions": [{"alias": "dim_ykj_rental_charge_wd_corp_name", "expression": {"field_content": {"field_name": "wd_corp_name", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "dim_ykj_rental_charge"}, "type": "Field"}}], "filter": {}, "having_filter": {}, "indicators": [{"alias": "ind4", "expression": {"field_content": {"code": "346c7b6d-4fc2-aad2-ed8f-32184a5b65ec", "field_type": "Indicator"}, "type": "Field"}}], "limit": {}, "sorts": [{"query_item": {"alias": "ind2", "expression": {"field_content": {"code": "1edbb0b6-c679-091b-b8ba-487b33a2a8af", "field_type": "Indicator"}, "type": "Field"}}, "ordering": "Desc"}, {"query_item": {"alias": "dim_ykj_rental_charge_wd_corp_name", "expression": {"field_content": {"field_name": "wd_corp_name", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "dim_ykj_rental_charge"}, "type": "Field"}}, "ordering": "Asc"}]}, "mode": "Agg", "engine": "StarRocksSaaS", "pulsar_code": "cdp_api_test", "class_id": "3e63b913-8c58-11ed-9e93-0255ac100302", "project_code": "alpha", "model_code": "", "with_variables": [{"name": "支付开始时间", "range_value_content": null, "scope_type": "single", "single_value_content": {"value": "1970-01-01"}}, {"name": "支付结束时间", "range_value_content": null, "scope_type": "single", "single_value_content": {"value": "2110-01-01"}}]}