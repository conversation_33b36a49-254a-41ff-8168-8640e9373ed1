{"query": {"dimensions": [], "filter": {}, "having_filter": {}, "indicators": [{"alias": "order_test1_tsmallint_avg", "expression": {"field_content": {"code": "e0170b3e-5cb3-4b55-b7ee-36d5d0fd90e4", "field_type": "Indicator"}, "type": "Field"}}], "limit": {"limit": 150, "offset": null}, "sorts": [{"ordering": "Desc", "query_item": {"alias": "", "expression": {"field_content": {"code": "e266879b-d90b-410e-ada8-7a7ef618c151", "field_type": "Indicator"}, "type": "Field"}}}]}, "mode": "Agg", "engine": "StarRocks", "pulsar_code": "model_zn", "class_id": "e6d4fe05-d84f-11ed-bea2-0255ac100068", "project_code": "model_zn", "model_code": "", "with_variables": null}