{"query": {"indicators": [{"expression": {"type": "Field", "fieldContent": {"fieldType": "Indicator", "code": "04f89696-267b-478d-b04c-e2a31f2984a2"}}, "alias": "sr_model_of_snow_unit_sold_sum"}], "dimensions": [{"expression": {"type": "Field", "fieldContent": {"fieldType": "Dimension", "tableName": "dws_sales_fact", "fieldName": "item_key"}}, "alias": "dws_sales_fact_item_key"}, {"expression": {"type": "Field", "fieldContent": {"fieldType": "Dimension", "tableName": "dim_location", "fieldName": "location_key"}}, "alias": "dim_location_location_key"}], "havingFilter": {"type": "Predicate", "subject": {"type": "Field", "fieldContent": {"fieldType": "Indicator", "code": "04f89696-267b-478d-b04c-e2a31f2984a2"}}, "predicate": {"type": "Comparison", "comparison": {"op": "Gt", "right": {"type": "Value", "valueContent": {"type": "IntegerValue", "value": "0"}}}}}, "sorts": [{"queryItem": {"expression": {"type": "Field", "fieldContent": {"fieldType": "Dimension", "tableName": "dim_location", "fieldName": "location_key"}}, "alias": "dim_location_location_key"}, "ordering": "Asc"}, {"queryItem": {"expression": {"type": "Field", "fieldContent": {"fieldType": "<PERSON><PERSON>", "aliasName": "<PERSON><PERSON><PERSON>", "opType": "CaseWhenEqual", "opParams": [{"type": "Value", "valueContent": {"type": "StringValue", "value": "深圳"}}, {"type": "Value", "valueContent": {"type": "IntegerValue", "value": "1"}}, {"type": "Value", "valueContent": {"type": "IntegerValue", "value": "100"}}]}}}, "ordering": "Asc"}], "limit": {"limit": 150}}, "mode": "Agg", "projectCode": "test", "pulsarCode": "model_zn", "classId": "0d1288b9-f87a-11ec-a9cd-fa163ef88daf", "engine": "Presto", "env": "<PERSON>"}