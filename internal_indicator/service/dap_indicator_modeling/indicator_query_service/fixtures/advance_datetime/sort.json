{"project_code": "sprint_1_5", "tenant_code": "shuxin15", "engine": "StarRocks_SaaS", "query": {"indicators": [{"alias": "dws_sprint7_view_1_Ind2", "expression": {"type": "Field", "field_content": {"field_type": "Indicator", "code": "189b30df-47fd-3150-aabc-237869bf90e5"}}}], "dimensions": [{"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_1", "field_name": "city_name", "op_type": "NoFieldOpType"}}, "alias": "dws_sprint7_view_1_city_name"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_1", "field_name": "prov_name", "op_type": "NoFieldOpType"}}, "alias": "dws_sprint7_view_1_prov_name"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_1", "field_name": "sjls", "op_type": "DatetimeFormatYear"}}, "alias": "year_dws_sprint7_view_1_sjls"}], "filter": {}, "having_filter": {}, "sorts": [{"query_item": {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_1", "field_name": "sjls", "op_type": "DatetimeFormatYear"}}, "alias": "year_dws_sprint7_view_1_sjls"}, "ordering": "Desc"}, {"query_item": {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_1", "field_name": "prov_name", "op_type": "NoFieldOpType"}}, "alias": "dws_sprint7_view_1_prov_name"}, "ordering": "Desc"}, {"query_item": {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_1", "field_name": "city_name", "op_type": "NoFieldOpType"}}, "alias": "dws_sprint7_view_1_city_name"}, "ordering": "Desc"}], "limit": {"offset": null, "limit": 150}, "table_name": "dws_sprint7_view_1", "mode": "Agg"}}