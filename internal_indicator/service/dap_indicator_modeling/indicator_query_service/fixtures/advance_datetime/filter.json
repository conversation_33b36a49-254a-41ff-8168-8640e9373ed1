{"query": {"table_name": "dws_sprint7_view_2", "mode": "Agg", "indicators": [{"expression": {"type": "Field", "field_content": {"field_type": "Indicator", "code": "5eba799d-836b-361c-a981-c15919aa5934"}}, "alias": "dws_sprint7_view_2_Ind1"}], "dimensions": [{"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_2", "field_name": "auction_class_name"}}, "alias": "dws_sprint7_view_2_auction_class_name"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_2", "field_name": "auction_name"}}, "alias": "dws_sprint7_view_2_auction_name"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_2", "field_name": "city_name"}}, "alias": "dws_sprint7_view_2_city_name"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_2", "field_name": "order_type"}}, "alias": "dws_sprint7_view_2_order_type"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_2", "field_name": "prov_name"}}, "alias": "dws_sprint7_view_2_prov_name"}, {"expression": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_2", "field_name": "gj_time_1", "op_type": "DatetimeFormatDay"}}, "alias": "day_dws_sprint7_view_2_gj_time_1"}], "filter": {"type": "FilterGroup", "associate_type": "And", "sub_filters": [{"type": "FilterGroup", "associate_type": "And", "sub_filters": [{"type": "Predicate", "subject": {"type": "Field", "field_content": {"field_type": "Dimension", "table_name": "dws_sprint7_view_2", "field_name": "gj_time_1", "op_type": "DatetimeFormatDay"}}, "predicate": {"type": "Between", "between": {"lower": {"type": "Value", "value_content": {"type": "StringValue", "value": "2023-01-01"}}, "upper": {"type": "Value", "value_content": {"type": "StringValue", "value": "2023-12-31"}}}}, "associate_type": "And"}]}]}, "having_filter": {}, "limit": {}}, "project_code": "sprint_1_5", "tenant_code": "shuxin15", "engine": "StarRocks_SaaS"}