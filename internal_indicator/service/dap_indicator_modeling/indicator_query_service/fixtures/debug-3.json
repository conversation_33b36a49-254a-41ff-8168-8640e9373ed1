{"query": {"dimensions": [{"alias": "dim_orgnaziton_org_id", "expression": {"field_content": {"field_name": "org_id", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "dim_orgnaziton"}, "type": "Field"}}], "filter": {}, "having_filter": {}, "indicators": [{"alias": "view_indi_order_test_indi_other1", "expression": {"field_content": {"code": "0ac8a891-c6c4-4061-bf51-027352077c18", "field_type": "Indicator"}, "type": "Field"}}], "limit": {"limit": 150, "offset": null}, "sorts": [{"ordering": "Asc", "query_item": {"alias": "", "expression": {"field_content": {"field_name": "amount_money", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "dws_return_money"}, "type": "Field"}}}]}, "mode": "Agg", "engine": "StarRocks", "pulsar_code": "model_zn", "class_id": "f9c9b298-dd96-11ed-aa2c-0255ac1000c7", "project_code": "model_zn", "model_code": "", "with_variables": null}