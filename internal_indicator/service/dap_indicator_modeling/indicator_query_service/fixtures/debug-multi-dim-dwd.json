{"query": {"dimensions": [{"alias": "dim_orgnazion_superior_org", "expression": {"field_content": {"field_name": "superior_org", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "dim_orgnazion"}, "type": "Field"}}], "filter": {}, "having_filter": {}, "indicators": [{"alias": "view_it_return_test_indi4", "expression": {"field_content": {"code": "640e75c3-48ae-409e-b42d-9f1951c333e6", "field_type": "Indicator"}, "type": "Field"}}], "limit": {"limit": 150, "offset": null}, "sorts": [{"ordering": "Asc", "query_item": {"alias": "", "expression": {"field_content": {"field_name": "datax_code", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "dim_orgnazion"}, "type": "Field"}}}]}, "mode": "Agg", "engine": "StarRocks", "pulsar_code": "model_api_autotesting", "class_id": "20d475e1-bf0f-11ed-8121-0255ac100072", "project_code": "model_api_autotest", "model_code": "", "with_variables": null}