{"query": {"dimensions": [{"alias": "test_all_types_id", "expression": {"field_content": {"field_name": "id", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}, {"alias": "test_all_types_project_id", "expression": {"field_content": {"field_name": "project_id", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}, {"alias": "test_all_types_tvarchar20", "expression": {"field_content": {"field_name": "tvarchar20", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}, {"alias": "test_all_types_tvarchar", "expression": {"field_content": {"field_name": "t<PERSON><PERSON><PERSON>", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}], "filter": {}, "having_filter": {}, "indicators": [{"alias": "order_test1_tsmallint_avg", "expression": {"field_content": {"code": "e0170b3e-5cb3-4b55-b7ee-36d5d0fd90e4", "field_type": "Indicator"}, "type": "Field"}}], "limit": {}, "sorts": [{"ordering": "Desc", "query_item": {"alias": "test_all_types_id", "expression": {"field_content": {"alias_name": "test_all_types_id", "field_type": "<PERSON><PERSON>", "op_params": [{"type": "Value", "value_content": {"type": "StringValue", "value": "005"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1500"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "010"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1499"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "004"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1498"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "006"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1497"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "007"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1496"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "008"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1495"}}, {"type": "Value", "value_content": {"type": "StringValue", "value": "009"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "1494"}}, {"type": "Value", "value_content": {"type": "IntegerValue", "value": "0"}}], "op_type": "CaseWhenEqual"}, "type": "Field"}}}, {"ordering": "Desc", "query_item": {"alias": "", "expression": {"field_content": {"code": "eabd070e-97e0-4e92-a424-3af7f1fb8138", "field_type": "Indicator"}, "type": "Field"}}}, {"ordering": "Asc", "query_item": {"alias": "test_all_types_tvarchar20", "expression": {"field_content": {"field_name": "tvarchar20", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}}, {"ordering": "Asc", "query_item": {"alias": "test_all_types_tvarchar", "expression": {"field_content": {"field_name": "t<PERSON><PERSON><PERSON>", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}}]}, "mode": "Agg", "engine": "StarRocks", "pulsar_code": "model_zn", "class_id": "e6d4fe05-d84f-11ed-bea2-0255ac100068", "project_code": "model_zn", "model_code": "", "with_variables": null}