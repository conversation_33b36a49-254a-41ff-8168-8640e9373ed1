{"query": {"dimensions": [{"alias": "test_all_types_id", "expression": {"field_content": {"field_name": "id", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}], "filter": {}, "having_filter": {}, "indicators": [], "limit": {"limit": 150, "offset": null}, "sorts": [{"ordering": "Asc", "query_item": {"expression": {"field_content": {"field_name": "tvarchar20", "field_type": "Dimension", "op_type": "NoFieldOpType", "table_name": "test_all_types"}, "type": "Field"}}}]}, "mode": "Agg", "engine": "StarRocks", "pulsar_code": "model_zn", "class_id": "e6d4fe05-d84f-11ed-bea2-0255ac100068", "project_code": "model_zn", "model_code": "", "with_variables": null}