package indicator_query_service

import (
	"context"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/test_helper"
	"io/ioutil"
	"testing"

	"github.com/golang/protobuf/jsonpb"
	"github.com/stretchr/testify/assert"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
)

const path = "./fixtures/"

func TestGenQueryDataSql(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	req := &indicator_query.QueryDataRequest{}
	bs, err := ioutil.ReadFile(path + "debug.json")

	at.Nil(err)
	err = jsonpb.UnmarshalString(string(bs), req)
	at.Nil(err)

	service := NewIndicatorQueryService()
	rsp, err := service.GenQueryDataSql(ctx, req)
	at.Nil(err)
	fmt.Printf("Sql: %s\n", rsp.Data.Sql)
}

func TestGenQueryDataSqlAdvanceDatetime(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	req := &indicator_query.QueryDataRequest{}
	bs, err := ioutil.ReadFile(path + "advance_datetime/sort.json")

	at.Nil(err)
	err = jsonpb.UnmarshalString(string(bs), req)
	at.Nil(err)

	service := NewIndicatorQueryService()
	rsp, err := service.GenQueryDataSql(ctx, req)
	at.Nil(err)
	fmt.Printf("Sql: %s\n", rsp.Data.Sql)
}

func TestSmoke(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	fixtures := [][]string{
		// 标准+自定义(cdp_api_test + zdtest + dws_dws_01)
		{"smoke/standard_custom/01_agg_ads_table_dw.json", "SELECT COUNT(dws_dws_01_expand.field_date) AS `field_date` FROM `dws_dws_01_expand` LIMIT 150"},
		{"smoke/standard_custom/01_agg_count_ads_table_dw.json", "select count(*) as total from ( SELECT COUNT(dws_dws_01_expand.field_date) AS `field_date` FROM `dws_dws_01_expand` ) x"},
		{"smoke/standard_custom/01_detail.json", "SELECT `dws_dws_01_expand`.`field_varchar` AS `field_varchar`, `dws_dws_01_expand`.`field_varchar_ext` AS `field_varchar_ext` FROM `dws_dws_01_expand` LIMIT 150"},
		{"smoke/standard_custom/01_detail_count.json", "select count(*) as total from ( SELECT `dws_dws_01_expand`.`field_varchar` AS `field_varchar`, `dws_dws_01_expand`.`field_varchar_ext` AS `field_varchar_ext` FROM `dws_dws_01_expand` ) x"},

		// 标准+标准(cdp_api_test + zdtest1 + dws_dws_02/dim_dim_02)
		{"smoke/standard_standard/01_agg_ads_table_dw.json", "SELECT COUNT(dws_dws_02.field_varchar) AS `field_date` FROM `dws_dws_02` LIMIT 150"},
		{"smoke/standard_standard/01_agg_count_ads_table_dw.json", "select count(*) as total from ( SELECT COUNT(dws_dws_02.field_varchar) AS `field_date` FROM `dws_dws_02` ) x"},
		{"smoke/standard_standard/01_detail.json", "SELECT `dim_dim_02`.`field_varchar` AS `field_varchar` FROM `dim_dim_02` LIMIT 150"},
		{"smoke/standard_standard/01_detail_count.json", "select count(*) as total from ( SELECT `dim_dim_02`.`field_varchar` AS `field_varchar` FROM `dim_dim_02` ) x"},

		// 个性化+自定义(zdtest1 + zdtest1 + dws_dws_01)
		{"smoke/individual_custom/01_agg_ads_table_dw.json", "SELECT COUNT(dws_dws_01_expand.field_date) AS `field_date` FROM `dws_dws_01_expand` LIMIT 150"},
		{"smoke/individual_custom/01_agg_count_ads_table_dw.json", "select count(*) as total from ( SELECT COUNT(dws_dws_01_expand.field_date) AS `field_date` FROM `dws_dws_01_expand` ) x"},
		{"smoke/individual_custom/01_detail.json", "SELECT `dws_dws_01_expand`.`field_varchar` AS `field_varchar` FROM `dws_dws_01_expand` LIMIT 150"},
		{"smoke/individual_custom/01_detail_count.json", "select count(*) as total from ( SELECT `dws_dws_01_expand`.`field_varchar` AS `field_varchar`, `dws_dws_01_expand`.`field_varchar_ext` AS `field_varchar_ext` FROM `dws_dws_01_expand` ) x"},

		// 个性化+标准(zdtest1 + zdtest1 + dws_dws_02/dim_dim_02)
		{"smoke/individual_standard/01_agg_ads_table_dw.json", "SELECT COUNT(dws_dws_02.field_varchar) AS `field_date` FROM `dws_dws_02` LIMIT 150"},
		{"smoke/individual_standard/01_agg_count_ads_table_dw.json", "select count(*) as total from ( SELECT COUNT(dws_dws_02.field_varchar) AS `field_date` FROM `dws_dws_02` ) x"},
		{"smoke/individual_standard/01_detail.json", "SELECT `dim_dim_02`.`field_varchar` AS `field_varchar` FROM `dim_dim_02` LIMIT 150"},
		{"smoke/individual_standard/01_detail_count.json", "select count(*) as total from ( SELECT `dim_dim_02`.`field_varchar` AS `field_varchar` FROM `dim_dim_02` ) x"},

		//// 个性化+自有(zdtest1 + zdtest1 + dws_dws_00)
		{"smoke/individual_self/01_agg_ads_view_dw.json", "SELECT COUNT(dws_dws_00.field_varchar) AS `field_date` FROM `dws_dws_00` LIMIT 150"},
		{"smoke/individual_self/01_agg_count_ads_view_dw.json", "select count(*) as total from ( SELECT COUNT(dws_dws_00.field_varchar) AS `field_date` FROM `dws_dws_00` ) x"},
		{"smoke/individual_self/01_detail_count.json", "select count(*) as total from ( SELECT `dws_dws_00`.`field_varchar` AS `field_varchar` FROM `dws_dws_00` ) x"},
		{"smoke/individual_self/01_detail.json", "SELECT `dws_dws_00`.`field_varchar` AS `field_varchar` FROM `dws_dws_00` LIMIT 150"},
	}

	service := NewIndicatorQueryService()
	for idx, fixture := range fixtures {
		fmt.Printf("%d: %s\n", idx, fixture)
		req := &indicator_query.QueryDataRequest{}
		bs, err := ioutil.ReadFile(path + fixture[0])
		at.Nil(err)
		err = jsonpb.UnmarshalString(string(bs), req)
		at.Nil(err)

		rsp, err := service.GenQueryDataSql(ctx, req)
		at.Nil(err)

		at.Equal(rsp.Data.Sql, fixture[1])
		fmt.Printf("Sql: %s\n", rsp.Data.Sql)
	}
}
