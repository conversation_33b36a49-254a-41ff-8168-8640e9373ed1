package indicator_query_service

import (
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	db_utils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/saas_db"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	adsContext "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/context"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errcode"
)

type IndicatorQueryService struct {
}

func NewIndicatorQueryService() *IndicatorQueryService {
	return &IndicatorQueryService{}
}

func (s *IndicatorQueryService) GenQueryDataSql(ctx context.Context, req *indicator_query.QueryDataRequest) (rsp *indicator_query.GenQueryDataSqlResponse, err error) {
	global.Logger.ProtoTrace("[IndicatorQueryService.GenQueryDataSql]recv request: ", req)
	rsp = new(indicator_query.GenQueryDataSqlResponse)
	rsp.ErrCode = errcode.ErrorCodeSuccess
	rsp.ErrMsg = "成功"
	rsp.Data = new(indicator_query.GenQueryDataSqlResponseData)

	db, err := db_utils.GetProjectDB(ctx, req.ProjectCode)
	if err != nil {
		err = errors.Wrapf(err, "无法连接租户<%s>数据库", req.ProjectCode)
		return
	}

	err = ColSumReqParamsAdapt(req)
	if err != nil {
		err = errors.Wrapf(err, "列汇总请求参数适配失败")
		return
	}

	repo := indicator_repository.NewIndicatorRepository(db, req.ProjectCode)
	indicator, err := repo.GetIndicatorByTableName(ctx, req.Query.TableName, string(global.Prod))
	if err != nil {
		return
	}
	if indicator.ID == "" {
		err = errors.Errorf("通过表名[%s]查询指标定义失败", req.Query.TableName)
		return
	}

	cache := indicator_cache.NewIndicatorCache(req.ProjectCode)

	// 生成Transformer, 实现指标构建计划需要用到的 DependenceModel, IndicatorDefinition, IndicatorAggregate, IndicatorFilter
	transformer := NewTransformer(req, entities.ProjectResourceType(req.Engine))
	err = transformer.PreCheckAndPrepareData(ctx, repo, cache)
	if err != nil {
		return
	}
	transformer.SetDevMode(indicator.DevMode)

	var adsViewBuilder *ads_view.Builder
	switch indicator.DevMode {
	case string(base.DevModeTypeDetail):
		adsViewBuilder = ads_view.NewBuilderForDetail(adsContext.NewDetailBuildContext(ctx, transformer, transformer,
			adsContext.WithDetailFilter(transformer),
			adsContext.WithDetailSort(transformer),
			adsContext.WithDetailLimit(transformer),
			adsContext.WithDetailModelRelation(transformer),
		), entities.ProjectResourceType(req.Engine))

	case string(base.DevModeTypeConfig), "":
		// 生成构建计划
		switch req.Query.Mode {
		case indicator_query.QueryMode_Detail:
			adsViewBuilder = ads_view.NewBuilderForDetail(adsContext.NewDetailBuildContext(ctx, transformer, transformer,
				adsContext.WithDetailFilter(transformer),
				adsContext.WithDetailSort(transformer),
				adsContext.WithDetailLimit(transformer),
			), entities.ProjectResourceType(req.Engine))
		case indicator_query.QueryMode_DetailCount:
			adsViewBuilder = ads_view.NewBuilderForDetail(adsContext.NewDetailBuildContext(ctx, transformer, transformer,
				adsContext.WithDetailFilter(transformer),
			), entities.ProjectResourceType(req.Engine))
		case indicator_query.QueryMode_Agg:
			adsViewBuilder = ads_view.NewBuilder(adsContext.NewBuildContext(ctx, transformer, transformer,
				adsContext.WithGlobalFilter(transformer),
				adsContext.WithAggregate(transformer),
				adsContext.WithFilter(transformer),
				adsContext.WithSort(transformer),
				adsContext.WithLimit(transformer),
				adsContext.WithVariables(transformer),
			), entities.ProjectResourceType(req.Engine))
		case indicator_query.QueryMode_Count:
			adsViewBuilder = ads_view.NewBuilder(adsContext.NewBuildContext(ctx, transformer, transformer,
				adsContext.WithGlobalFilter(transformer),
				adsContext.WithAggregate(transformer),
				adsContext.WithFilter(transformer),
				adsContext.WithVariables(transformer),
			), entities.ProjectResourceType(req.Engine))
		case indicator_query.QueryMode_ColSubTotal, indicator_query.QueryMode_ColTotal:
			adsViewBuilder = ads_view.NewBuilder(adsContext.NewBuildContext(ctx, transformer, transformer,
				adsContext.WithGlobalFilter(transformer),
				adsContext.WithAggregate(transformer),
				adsContext.WithFilter(transformer),
				adsContext.WithSort(transformer),
				adsContext.WithLimit(transformer),
				adsContext.WithVariables(transformer),
			), entities.ProjectResourceType(req.Engine))
		default:
			err = errors.Errorf("不支持的QueryMode<%s>", req.Query.Mode.String())
			return
		}
	default:
		err = errors.Errorf("不支持的指标开发模式<%s>", indicator.DevMode)
		return
	}

	// 生成sql
	sql, err := adsViewBuilder.ToSQL()
	if err != nil {
		err = errors.Wrapf(err, "生成明细sql失败")
		return rsp, err
	}

	if req.Query.Mode == indicator_query.QueryMode_Count || req.Query.Mode == indicator_query.QueryMode_DetailCount {
		sql = fmt.Sprintf("select count(*) as total from ( %s ) x", sql)
	}

	rsp.Data.Sql = sql
	global.Logger.ProtoTrace("[IndicatorQueryService.GenQueryDataSql]send response: ", rsp)
	return rsp, nil
}

func ColSumReqParamsAdapt(req *indicator_query.QueryDataRequest) error {
	if req.Query.Mode == indicator_query.QueryMode_ColSubTotal || req.Query.Mode == indicator_query.QueryMode_ColTotal {

		colSumDims := []*indicator_query.QueryItem{}
		colSumIndicators := []*indicator_query.QueryItem{}

		err := copier.Copy(&colSumDims, req.Query.Dimensions)
		if err != nil {
			return err
		}
		err = copier.Copy(&colSumIndicators, req.Query.Indicators)
		if err != nil {
			return err
		}

		sumIndicatorCount := map[string]int{}
		repeatedIndicators := map[string]struct{}{}
		for _, indicator := range colSumIndicators {
			sumIndicatorCount[indicator.Expression.FieldContent.Code]++
		}
		for k, v := range sumIndicatorCount {
			if v > 1 {
				repeatedIndicators[k] = struct{}{}
			}
		}

		dimMap := map[string]struct{}{}
		for _, dim := range colSumDims {
			dimMap[dim.Alias] = struct{}{}
		}

		indicatorMap := map[string]*indicator_query.QueryItem{}
		for _, indicator := range colSumIndicators {
			indicatorMap[indicator.Expression.FieldContent.Code] = indicator
		}

		req.Query.Dimensions = req.Query.AllDimensions
		for i, d := range req.Query.Dimensions {
			if _, ok := dimMap[d.Alias]; ok {
				req.Query.Dimensions[i].IsColSum = true
			}
		}

		//if len(req.Query.Indicators) <= len(req.Query.AllIndicators) {
		//	req.Query.Indicators = req.Query.AllIndicators
		//	for i, d := range req.Query.AllIndicators {
		//		if indicator, ok := indicatorMap[d.Expression.FieldContent.Code]; ok {
		//			req.Query.Indicators[i].IsColSum = true
		//			//别名用
		//			req.Query.Indicators[i].Alias = indicator.Alias
		//		}
		//	}
		//} else {
		//	for i, d := range req.Query.Indicators {
		//		if _, ok := indicatorMap[d.Expression.FieldContent.Code]; ok {
		//			req.Query.Indicators[i].IsColSum = true
		//		}
		//	}
		//}

		//如果indicators中一个code用两次，那么就以indicators的这个指标code为准
		//1、先找出indicators中重复的指标code
		//2、从all_indicators中找出非1中的重复指标code，加入到最终的indicators（别名要用indicators中的，如果出现）
		//3、直接把indicators中重复的指标code加入到最终的indicators
		mergeIndicators := []*indicator_query.QueryItem{}
		for _, indicator := range req.Query.AllIndicators {
			//不重复的直接加入最终指标集合
			if _, ok := repeatedIndicators[indicator.Expression.FieldContent.Code]; !ok {

				//如果是列小记指标还要更换别名
				if in, ok1 := indicatorMap[indicator.Expression.FieldContent.Code]; ok1 {
					indicator.IsColSum = true
					//别名用
					indicator.Alias = in.Alias
				}
				mergeIndicators = append(mergeIndicators, indicator)
			}
		}

		//列小记指标重复出现select的直接加入mergeindicator
		for _, indicator := range req.Query.Indicators {
			if _, ok := repeatedIndicators[indicator.Expression.FieldContent.Code]; ok {
				indicator.IsColSum = true
				mergeIndicators = append(mergeIndicators, indicator)
			}
		}

		req.Query.Indicators = mergeIndicators

	}
	return nil
}
