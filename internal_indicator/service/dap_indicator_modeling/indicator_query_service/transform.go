package indicator_query_service

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"sort"
	"strconv"
	"strings"

	"github.com/samber/lo"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/logger"

	"gorm.io/gorm"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/model/mysql"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/dimension"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

var _ injection.DependenceModel = (*Transformer)(nil)
var _ injection.QueryLimit = (*Transformer)(nil)

var _ injection.IndicatorAggregate = (*Transformer)(nil)
var _ injection.IndicatorFilter = (*Transformer)(nil)
var _ injection.IndicatorDefinition = (*Transformer)(nil)
var _ injection.IndicatorSort = (*Transformer)(nil)

var _ injection.DetailDefinition = (*Transformer)(nil)
var _ injection.DetailFilter = (*Transformer)(nil)
var _ injection.DetailSort = (*Transformer)(nil)
var _ injection.DetailModelRelation = (*Transformer)(nil)

type Transformer struct {
	*injection.DefaultDepModelImpl
	defaultFilter *injection.DefaultIndicatorFilterImpl
	req           *indicator_query.QueryDataRequest // 数见查询请求
	logger        *logger.Logger
	mode          indicator_query.QueryMode
	// context
	viewContent       *proto.ViewContentNew // 应用表ViewContent
	dwTableMap        map[string]bool       // 查询中用到的事实表
	IndicatorFieldMap map[string]mysql.IndicatorField
	DimFieldMap       map[string]mysql.IndicatorField
	resourceType      entities.ProjectResourceType
	factory           *dimension.Factory
	devMode           string
}

func NewTransformer(req *indicator_query.QueryDataRequest, resourceType entities.ProjectResourceType) *Transformer {
	return &Transformer{
		req:               req,
		mode:              req.Query.Mode,
		logger:            global.Logger,
		dwTableMap:        make(map[string]bool),
		IndicatorFieldMap: make(map[string]mysql.IndicatorField),
		DimFieldMap:       make(map[string]mysql.IndicatorField),
		factory:           dimension.NewFactory(),
		resourceType:      resourceType,
	}
}

func (s *Transformer) isDetailMode() bool {
	switch s.mode {
	case indicator_query.QueryMode_Detail, indicator_query.QueryMode_DetailCount:
		return true
	default:
		return false
	}
}

func (s *Transformer) SetDevMode(devMode string) {
	s.devMode = devMode
}

// PreCheckAndPrepareData 对查询结构进行检查并从数据库获取必要数据
func (s *Transformer) PreCheckAndPrepareData(ctx context.Context, repo *indicator_repository.IndicatorRepository, cache indicator_cache.IndicatorModelCache) error {
	if s.isDetailMode() {
		return s.loadDetailData(ctx, repo, cache)
	} else {
		return s.loadAggData(ctx, repo, cache)
	}
}

func (s *Transformer) loadDetailData(ctx context.Context, repo *indicator_repository.IndicatorRepository, cache indicator_cache.IndicatorModelCache) (err error) {
	if s.req.Query.TableName == "" {
		return errors.New("模型英文名不能为空")
	}
	// 获取应用表模型ViewContent
	s.viewContent, err = s.getAdsModelViewContent(ctx, repo, cache)
	if err != nil {
		return
	}

	// 从数据库中取相关数据
	if s.viewContent.Dependence == nil {
		return errors.New("服务器内部错误, 应用表依赖模型不存在")
	}

	// todo: 需要更替依赖模型
	s.DefaultDepModelImpl = injection.NewDefaultDepModelImpl(s.viewContent.Dependence, nil,
		injection.WithLoadCache(cache),
	)
	s.defaultFilter = nil
	return s.DefaultDepModelImpl.PreLoad(ctx, repo, string(global.Prod))
}

func (s *Transformer) loadAggData(ctx context.Context, repo *indicator_repository.IndicatorRepository, cache indicator_cache.IndicatorModelCache) (err error) {
	// 获取应用表模型ViewContent
	s.viewContent, err = s.getAdsModelViewContent(ctx, repo, cache)
	if err != nil {
		return
	}

	// 收集、修复查询的维度和指标
	// _, _, err = s.collectAndFixIndicatorsAndFields(ctx)
	// if err != nil {
	// 	return
	// }

	// 从数据库中取相关数据
	if s.viewContent.Dependence == nil {
		return errors.New("服务器内部错误, 应用表依赖模型不存在")
	}
	s.DefaultDepModelImpl = injection.NewDefaultDepModelImpl(s.viewContent.Dependence,
		&s.viewContent.Dependence.RelationalModel,
		injection.WithLoadCache(cache),
	)
	s.defaultFilter = injection.NewDefaultIndicatorFilterImpl(s.viewContent, s.req.WithVariables)
	return s.DefaultDepModelImpl.PreLoad(ctx, repo, string(global.Prod))
}

// ============================== IndicatorDefinitionImpl ==============================

func (s *Transformer) GetDefinitions(ctx context.Context) ([]*proto.Definition, error) {
	result := []*proto.Definition{}
	if s.isDetailMode() {
		return result, nil
	}
	for _, queryIndicator := range s.req.Query.Indicators {
		definition, err := s.genIndicatorFromViewContent(queryIndicator.Alias, queryIndicator.Expression.FieldContent.Code)
		if err != nil {
			return nil, err
		}
		//definition.IsColSum = queryIndicator.IsColSum
		// s.setIndicatorDependDimDw(definition)
		result = append(result, definition)
	}

	global.Logger.JsonTrace("[IndicatorQuery] GetDefinitions Result: ", result)
	return result, nil
}

// ============================== DetailDefinitionImpl ==============================

func (s *Transformer) GetAdditionalField(ctx context.Context) ([]*proto.Field, error) {
	result := []*proto.Field{}
	queryItems := []*indicator_query.QueryItem{}
	queryItems = append(queryItems, s.req.Query.Indicators...)
	queryItems = append(queryItems, s.req.Query.Dimensions...)
	for _, queryItem := range queryItems {
		field := &proto.Field{
			ModelField: proto.ModelField{
				Table: queryItem.Expression.FieldContent.TableName,
				Field: queryItem.Expression.FieldContent.FieldName,
				Alias: queryItem.Alias,
			},
			TimeFormat: proto.TimeFormat{
				TimeFormatType: s.queryTimeFormatToSqlBuildTimeFormat(queryItem.Expression.FieldContent.OpType),
			},
			DimDefinition: common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(queryItem.Expression.FieldContent.FieldName)),
		}
		var dimensionIns = s.factory.GetDimension(field, s.resourceType)
		refFields, err := dimensionIns.GetRefFields(ctx)
		if err != nil {
			return nil, err
		}
		if len(refFields) == 0 {
			continue
		}
		ModelField := refFields[0]
		ModelField.Alias = queryItem.Alias
		result = append(result, &proto.Field{
			ModelField: ModelField,
		})
	}

	return result, nil
}

func (s *Transformer) getShowOrderField(item *indicator_query.Sort) (*proto.Field, error) {
	field := new(proto.Field)
	// 非自定义排序直接使用QueryItem的Alias
	if item.QueryItem.Alias != "" {
		field.Alias = item.QueryItem.Alias
	}
	// 自定义排序从Expression中提取CaseWhen进行转换
	if item.QueryItem.Expression != nil &&
		item.QueryItem.Expression.FieldContent != nil &&
		item.QueryItem.Expression.FieldContent.AliasName != "" {
		// 优先使用expression中的aliasName
		field.Alias = item.QueryItem.Expression.FieldContent.AliasName
		if item.QueryItem.Expression.FieldContent.OpType == indicator_query.FieldOpType_CaseWhenEqual {
			var err error
			field.CustomizeValueOrder, err = s.caseWhenEqualToCustomizeValueOrder(item.QueryItem.Expression.FieldContent.OpParams)
			if err != nil {
				return field, err
			}
		}
		field.DimDefinition = common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(item.QueryItem.Expression.FieldContent.FieldName))
	} else if item.QueryItem.Expression != nil &&
		item.QueryItem.Expression.FieldContent != nil {
		field.Table = item.QueryItem.Expression.FieldContent.TableName
		field.Field = item.QueryItem.Expression.FieldContent.FieldName
		field.DimDefinition = common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(item.QueryItem.Expression.FieldContent.FieldName))
	}
	return field, nil
}

func (s *Transformer) getNotShowOrderField(item *indicator_query.Sort) (*proto.Field, error) {
	field := new(proto.Field)
	if len(s.dwTableMap) >= 2 { //如果查询的指标是多维多事实的，这里直接返回报错
		return nil, errors.New("多维多事实指标不支持排序")
	}
	if item.QueryItem.Expression == nil || item.QueryItem.Expression.FieldContent == nil {
		return nil, errors.New("排序字段表达式为空")
	}
	if item.QueryItem.Expression.FieldContent.FieldType == indicator_query.FieldType_Dimension || item.QueryItem.Expression.FieldContent.FieldType == indicator_query.FieldType_Measure { //字段
		field.Table = item.QueryItem.Expression.FieldContent.TableName
		field.Field = item.QueryItem.Expression.FieldContent.FieldName
		field.DimDefinition = common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(item.QueryItem.Expression.FieldContent.FieldName))

		// s.setFieldDependDimDw(field.Table)
		// if len(s.dwTableMap) >= 2 { //如果排序的字段是多维多事实的，这里直接返回报错
		// 	return nil, errors.New("多维多事实指标不支持排序")
		// }
	} else if item.QueryItem.Expression.FieldContent.FieldType == indicator_query.FieldType_Indicator { //指标
		definition, err := s.genIndicatorFromViewContent(item.QueryItem.Expression.FieldContent.AliasName, item.QueryItem.Expression.FieldContent.Code)
		if err != nil {
			return nil, err
		}
		// s.setIndicatorDependDimDw(definition)
		// if len(s.dwTableMap) >= 2 { //如果排序的指标是多维多事实的，这里直接返回报错
		// 	return nil, errors.New("多维多事实指标不支持排序")
		// }
		if definition.FormulaType == proto.CustomFormulaType {
			field.SourceExpress = definition.SqlExpress
		} else {
			field.Table = definition.Table
			field.Field = definition.Field
			field.SourceExpress = fmt.Sprintf("%s(%s.%s)", definition.Function, definition.Table, definition.Field)
		}

		field.DimDefinition = common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(item.QueryItem.Expression.FieldContent.FieldName))
	}

	return field, nil
}

func setSortItemIsShowType(query *indicator_query.Query) {
	itemMap := make(map[string]bool, 0)
	for _, item := range query.Dimensions {
		if item.Expression != nil &&
			item.Expression.FieldContent != nil {
			itemMap[fmt.Sprintf("%s.%s", item.Expression.FieldContent.TableName, item.Expression.FieldContent.FieldName)] = true
		}

	}
	for _, item := range query.Indicators {
		if item.Expression != nil &&
			item.Expression.FieldContent != nil {
			itemMap[item.Expression.FieldContent.Code] = true
		}
	}
	for index, item := range query.Sorts {
		if item.QueryItem.Expression != nil &&
			item.QueryItem.Expression.FieldContent != nil &&
			item.QueryItem.Expression.FieldContent.AliasName != "" { //自定义排序额外处理
			query.Sorts[index].ShowType = 1
			continue
		}

		if item.QueryItem.Expression != nil &&
			item.QueryItem.Expression.FieldContent != nil {
			if _, ok := itemMap[item.QueryItem.Expression.FieldContent.Code]; ok {
				query.Sorts[index].ShowType = 1
				continue
			}
			if _, ok := itemMap[fmt.Sprintf("%s.%s", item.QueryItem.Expression.FieldContent.TableName, item.QueryItem.Expression.FieldContent.FieldName)]; ok {
				query.Sorts[index].ShowType = 1
				continue
			}
		}
	}
}

// ============================== IndicatorSortImpl ==============================

func (s *Transformer) GetSort(ctx context.Context) ([]*proto.Sort, error) {
	result := make([]*proto.Sort, 0)
	setSortItemIsShowType(s.req.Query)
	for i, item := range s.req.Query.Sorts {
		var sortExpr = new(proto.Sort)
		sortExpr.Direction = s.queryOrderingToSortDirection(item.Ordering)
		var err error
		if item.QueryItem == nil {
			return result, errors.Errorf("第 [%d]个 排序字段表达式为空", i)
		}

		if item.QueryItem.Expression == nil || item.QueryItem.Expression.FieldContent.FieldType != indicator_query.FieldType_Indicator {
			sortExpr.ExpType = proto.FieldExp
			if item.ShowType == 1 {
				sortExpr.Field, err = s.getShowOrderField(item)
				if err != nil {
					return result, err
				}
			} else {
				sortExpr.Field, err = s.getNotShowOrderField(item)
				if err != nil {
					return result, err
				}
			}
		} else {
			var expr = item.QueryItem.Expression
			if expr.Type != indicator_query.ExpressionType_Field {
				return result, errors.Errorf("暂不支持非字段类型表达式<%s>转换为字段", expr.Type.String())
			}
			sortExpr.ExpType = proto.DefinitionExp
			sortExpr.Definition, err = s.genIndicatorFromViewContent(item.QueryItem.Alias, expr.FieldContent.Code)
			if err != nil {
				return result, err
			}
		}
		result = append(result, sortExpr)
	}
	global.Logger.JsonTrace("[IndicatorQuery] GetSort Result: ", result)
	return result, nil
}

// ============================== DetailSortImpl ==============================

func (s *Transformer) GetDetailSort(ctx context.Context) ([]*proto.Sort, error) {
	result := []*proto.Sort{}
	setSortItemIsShowType(s.req.Query)
	for _, item := range s.req.Query.Sorts {
		field := new(proto.Field)
		var err error
		if item.ShowType == 1 {
			field, err = s.getShowOrderField(item)
			if err != nil {
				return nil, err
			}
		} else {
			field, err = s.getNotShowOrderField(item)
			if err != nil {
				return nil, err
			}
		}
		field.DimDefinition = common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(item.QueryItem.Expression.FieldContent.FieldName))
		var dimensionIns = s.factory.GetDimension(field, s.resourceType)
		refFields, err := dimensionIns.GetRefFields(ctx)
		if err != nil {
			return nil, err
		}
		if len(refFields) == 0 {
			continue
		}
		ModelField := refFields[0]
		result = append(result, &proto.Sort{
			Field: &proto.Field{
				ModelField: ModelField,
			},
			Direction: s.queryOrderingToSortDirection(item.Ordering),
		})
	}

	global.Logger.JsonTrace("[IndicatorQuery] GetSort Result: ", result)
	return result, nil
}

func (s *Transformer) GetDetailModelRelation(ctx context.Context) ([]*proto.DetailModelRelationInfo, error) {
	list := make([]*proto.DetailModelRelationInfo, 0)
	for _, v := range s.viewContent.DetailRelationList {
		list = append(list, &proto.DetailModelRelationInfo{
			TableName: v.TableName,
			Code:      v.CodeId,
		})
	}
	return list, nil
}

type condValuePair struct {
	Condition *indicator_query.Expression
	Value     int
}
type condValuePairs []*condValuePair

func (s condValuePairs) Len() int           { return len(s) }
func (s condValuePairs) Less(i, j int) bool { return s[i].Value < s[j].Value }
func (s condValuePairs) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }

func (s *Transformer) caseWhenEqualToCustomizeValueOrder(opParams []*indicator_query.Expression) ([]proto.ValueContent, error) {
	if len(opParams) <= 1 {
		err := errors.Errorf("opParams长度【%d】错误", len(opParams))
		return nil, err
	}
	opParams = opParams[:len(opParams)-1] // 舍弃else的值
	if len(opParams)&1 != 0 {
		err := errors.Errorf("opParams长度【%d】错误", len(opParams))
		return nil, err
	}

	pairs := make(condValuePairs, len(opParams)/2)
	for idx := range pairs {
		condParam := opParams[idx*2]
		valueParam := opParams[idx*2+1]
		if valueParam.ValueContent.Type != indicator_query.ValueType_IntegerValue {
			err := errors.Errorf("opParams参数类型【%s】错误", valueParam.ValueContent.Type.String())
			return nil, err
		}
		rank, err := strconv.Atoi(valueParam.ValueContent.Value)
		if err != nil {
			err := errors.Errorf("opParams参数【%s】无法转成int值", valueParam.ValueContent.Value)
			return nil, err
		}

		pairs[idx] = &condValuePair{
			Condition: condParam,
			Value:     rank,
		}
	}

	sort.Sort(pairs)
	order := []proto.ValueContent{}
	for _, pair := range pairs {
		condition, err := s.queryExpressionToValue(pair.Condition)
		if err != nil {
			return nil, err
		}
		order = append(order, condition)
	}
	return order, nil
}

// ============================== QueryLimitImpl ==============================

func (s *Transformer) GetLimit(ctx context.Context) (*proto.Limit, error) {
	result := new(proto.Limit)
	//不做limit限制
	if s.req.Query.Limit.Limit == -1 {
		return result, nil
	}

	if s.req.Query.Limit != nil {
		result.Limit = int64(s.req.Query.Limit.Limit)
		result.Offset = int64(s.req.Query.Limit.Offset)
	}

	if result.Limit <= 0 || result.Limit > global.AppConfig.DmpApi.GetMaxIndicatorQueryLimit() {
		result.Limit = global.AppConfig.DmpApi.GetMaxIndicatorQueryLimit()
	}
	global.Logger.JsonTrace("[IndicatorQuery] GetLimit Result: ", result)
	return result, nil
}

func (s *Transformer) GetGlobalFilter(ctx context.Context) (map[string][]*proto.Filter, error) {
	if s.isDetailMode() {
		return nil, nil
	}
	return s.defaultFilter.GetGlobalFilter(ctx)
}

func (s *Transformer) GetVariableContext(ctx context.Context) (*injection.VariableContext, error) {
	if s.isDetailMode() {
		return nil, nil
	}
	return s.defaultFilter.GetVariableContext(ctx)
}

// ============================== IndicatorFilterImpl ==============================

func (s *Transformer) GetFilter(ctx context.Context) ([]*proto.Filter, error) {
	// 生成应用表筛选器
	var adsFilters []*proto.Filter
	var err error
	// 生成dmp筛选器
	dmpFilters, err := s.queryFilterToSqlBuildFilter(s.req.Query.Filter, true, indicator_query.AssociateType_NoAssociateType)
	if err != nil {
		return nil, err
	}

	var result []*proto.Filter
	if len(adsFilters) == 0 {
		result = dmpFilters
	} else if len(dmpFilters) == 0 {
		result = adsFilters
	} else {
		adsFilter := &proto.Filter{SubFilter: adsFilters}
		dmpFilter := &proto.Filter{SubFilter: dmpFilters, Associate: proto.AND}
		result = []*proto.Filter{{SubFilter: []*proto.Filter{adsFilter, dmpFilter}}}
	}

	global.Logger.JsonTrace("[IndicatorQuery] GetFilter Result: ", result)
	return result, nil
}

// ============================== DetailFilterImpl ==============================

func (s *Transformer) GetDetailFilter(ctx context.Context) ([]*proto.Filter, error) {
	// 生成应用表筛选器
	var adsFilters []*proto.Filter
	var err error

	mapFilter, err := s.GetGlobalFilter(ctx)
	if err != nil {
		return nil, err
	}
	isFirst := true
	for _, filter := range mapFilter {
		if len(adsFilters) != 0 {
			isFirst = false
		}
		newFilter := &proto.Filter{
			SubFilter: filter,
		}
		if !isFirst {
			newFilter.Associate = proto.AND
		}
		adsFilters = append(adsFilters, newFilter)
	}

	// 生成dmp筛选器
	dmpFilters, err := s.queryFilterToSqlBuildFilter(s.req.Query.Filter, true, indicator_query.AssociateType_NoAssociateType)
	if err != nil {
		return nil, err
	}

	var result []*proto.Filter
	if len(adsFilters) == 0 {
		result = dmpFilters
	} else if len(dmpFilters) == 0 {
		result = adsFilters
	} else {
		adsFilter := &proto.Filter{SubFilter: adsFilters}
		dmpFilter := &proto.Filter{SubFilter: dmpFilters, Associate: proto.AND}
		result = []*proto.Filter{{SubFilter: []*proto.Filter{adsFilter, dmpFilter}}}
	}

	global.Logger.JsonTrace("[IndicatorQuery] GetFilter Result: ", result)
	return result, nil
}

// ============================== IndicatorAggregateImpl ==============================
//func (s *Transformer) GetExpandTableOption() *sql_build.TenantExpandTableOption {
//	isReplace := false
//	if s.req.Engine != indicator_query.QueryEngine_Presto {
//		isReplace = true
//	}
//
//	return &sql_build.TenantExpandTableOption{IsReplace: isReplace}
//}

// func (s *Transformer) setFieldDependDimDw(table string) {
// 	multiDimDepModels := s.DefaultDepModelImpl.GetMultiDimDepModel()
// 	if category, ok := multiDimDepModels[table]; ok && (category == string(base_proto.DWDModelCategory) || category == string(base_proto.DWSModelCategory)) {
// 		s.dwTableMap[table] = true
// 	}
// }

// ============================== IndicatorAggregateImpl ==============================

func (s *Transformer) GetAggregate(ctx context.Context) (result *proto.Aggregate, err error) {
	result = new(proto.Aggregate)
	if s.isDetailMode() {
		return
	}

	//如果是列汇总，那么就遍历AllDimensions，此处会返回表格所有用到的维度，而Dimensions存的只是当前列汇总的维度
	//dims := []*indicator_query.QueryItem{}
	//if s.req != nil && (s.req.Mode == indicator_query.QueryMode_ColTotal || s.req.Mode == indicator_query.QueryMode_ColSubTotal) {
	//	dims = s.req.Query.AllDimensions
	//} else {
	//	dims = s.req.Query.Dimensions
	//}

	for _, dimension := range s.req.Query.Dimensions {
		result.Fields = append(result.Fields, &proto.Field{
			ModelField: proto.ModelField{
				Table: dimension.Expression.FieldContent.TableName,
				Field: dimension.Expression.FieldContent.FieldName,
				Alias: dimension.Alias,
			},
			TimeFormat: proto.TimeFormat{
				TimeFormatType: s.queryTimeFormatToSqlBuildTimeFormat(dimension.Expression.FieldContent.OpType),
			},
			IsColSum:      dimension.IsColSum,
			DimDefinition: common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(dimension.Expression.FieldContent.FieldName)),
		})
		// s.setFieldDependDimDw(dimension.Expression.FieldContent.TableName)
	}
	result.Having, err = s.queryFilterToSqlBuildFilter(s.req.Query.HavingFilter, true, indicator_query.AssociateType_NoAssociateType)
	if err != nil {
		return
	}
	global.Logger.JsonTrace("[IndicatorQuery] GetAggregate Result: ", result)
	return
}

// ============================== Internal ==============================
func (s *Transformer) queryOrderingToSortDirection(ordering indicator_query.Ordering) proto.Direction {
	switch ordering {
	case indicator_query.Ordering_Desc, indicator_query.Ordering_NoOrdering:
		return proto.Direction_Desc
	case indicator_query.Ordering_Asc:
		return proto.Direction_Asc
	default:
		return proto.Direction_Desc
	}
}

func (s *Transformer) queryFilterToSqlBuildFilter(filter *indicator_query.Filter, firstFilter bool, parentAssociateType indicator_query.AssociateType) ([]*proto.Filter, error) {
	newFilter := new(proto.Filter)
	result := []*proto.Filter{}

	if !firstFilter && parentAssociateType != indicator_query.AssociateType_NoAssociateType {
		newFilter.Associate = s.queryAssociateToSqlBuildAssociate(parentAssociateType)
	} else {
		newFilter.Associate = proto.NoAssociate
	}

	if filter == nil {
		return result, nil
	}
	if filter.Type == indicator_query.FilterType_NoFilterType {
		return result, nil
	}
	if filter.Type == indicator_query.FilterType_Predicate {
		var err error
		switch filter.Predicate.Type {
		case indicator_query.PredicateType_Like:
			if err = s.fillupFilterLeftExp(newFilter, filter.Subject); err != nil {
				return nil, err
			}
			if filter.Predicate.Like.Not {
				// TODO(zhud04): 支持not like
				return nil, errors.New("暂不支持Not Like操作符")
			} else {
				newFilter.Operate = proto.Like
			}
			newFilter.Value, err = s.queryExpressionToValue(filter.Predicate.Like.Value)
			if err != nil {
				return nil, err
			}
		case indicator_query.PredicateType_InList:
			if err = s.fillupFilterLeftExp(newFilter, filter.Subject); err != nil {
				return nil, err
			}
			if filter.Predicate.InList.Not {
				newFilter.Operate = proto.NotContain
			} else {
				newFilter.Operate = proto.Contain
			}
			newFilter.Value, err = s.queryInListToValue(filter.Predicate.InList.Values)
		case indicator_query.PredicateType_Comparison:
			if err = s.fillupFilterLeftExp(newFilter, filter.Subject); err != nil {
				return nil, err
			}
			if filter.Predicate.Comparison.Op == indicator_query.ComparisonOpType_IsNull {
				newFilter.Operate = proto.IsEmpty
			} else if filter.Predicate.Comparison.Op == indicator_query.ComparisonOpType_IsNotNull {
				newFilter.Operate = proto.IsNotEmpty
			} else {
				newFilter.Operate, err = s.queryComparisonOpToSqlBuildOp(filter.Predicate.Comparison.Op)
				if err != nil {
					return nil, err
				}
				newFilter.Value, err = s.queryExpressionToValue(filter.Predicate.Comparison.Right)
				if err != nil {
					return nil, err
				}
			}
		case indicator_query.PredicateType_Between:
			subFilter1 := new(proto.Filter)
			subFilter1.Associate = proto.NoAssociate
			if err = s.fillupFilterLeftExp(subFilter1, filter.Subject); err != nil {
				return nil, err
			}
			subFilter1.Operate = proto.Gte
			subFilter1.Value, err = s.queryExpressionToValue(filter.Predicate.Between.Lower)
			if err != nil {
				return nil, err
			}
			subFilter2 := new(proto.Filter)
			subFilter2.Associate = proto.AND
			if err = s.fillupFilterLeftExp(subFilter2, filter.Subject); err != nil {
				return nil, err
			}
			subFilter2.Operate = proto.Lte
			subFilter2.Value, err = s.queryExpressionToValue(filter.Predicate.Between.Upper)
			if err != nil {
				return nil, err
			}
			newFilter.SubFilter = []*proto.Filter{subFilter1, subFilter2}
		}
		result = append(result, newFilter)
		return result, nil
	}
	for idx, subFilter := range filter.SubFilters {
		sqlBuildSubFilter, err := s.queryFilterToSqlBuildFilter(subFilter, idx == 0, filter.AssociateType)
		if err != nil {
			return nil, err
		}
		newFilter.SubFilter = append(newFilter.SubFilter, sqlBuildSubFilter...)
	}
	result = append(result, newFilter)
	return result, nil
}

func (s *Transformer) fillupFilterLeftExp(filter *proto.Filter, expr *indicator_query.Expression) (err error) {
	if expr.Type != indicator_query.ExpressionType_Field {
		err = errors.Errorf("暂不支持非字段类型表达式<%s>转换为字段", expr.Type.String())
		return
	}
	if expr.FieldContent.FieldType == indicator_query.FieldType_Indicator {
		filter.LeftExpType = proto.DefinitionExp
		filter.Definition, err = s.genIndicatorFromViewContent("", expr.FieldContent.Code)
		if err != nil {
			return
		}
	} else {
		filter.LeftExpType = proto.FieldExp
		filter.Field, err = s.queryExpressionToSqlBuildField(expr)
		if err != nil {
			return
		}
	}

	return
}

func (s *Transformer) queryInListToValue(exprs []*indicator_query.Expression) (proto.ValueContent, error) {
	result := proto.ValueContent{}
	if len(exprs) == 0 {
		return result, errors.New("InList值列表长度不能为0")
	}
	encodeValues := []string{}
	for _, expr := range exprs {
		if expr.Type != indicator_query.ExpressionType_Value {
			return result, errors.Errorf("InList值列表参数类型<%s>必须为值", expr.Type.String())
		}
		switch expr.ValueContent.Type {
		case indicator_query.ValueType_NullValue:
			encodeValues = append(encodeValues, "NULL")
		case indicator_query.ValueType_FloatValue, indicator_query.ValueType_IntegerValue:
			encodeValues = append(encodeValues, expr.ValueContent.Value)
		case indicator_query.ValueType_StringValue:
			encodeValues = append(encodeValues, fmt.Sprintf("'%s'", strings.ReplaceAll(expr.ValueContent.Value, "'", "\\'")))
		}
	}

	return proto.ValueContent{
		Type:       proto.ValueType_ExpressArray,
		ValueArray: encodeValues,
	}, nil
}

func (s *Transformer) queryExpressionToValue(expr *indicator_query.Expression) (proto.ValueContent, error) {
	result := proto.ValueContent{}
	if expr.Type != indicator_query.ExpressionType_Value {
		return result, errors.New("暂不支持非值表达式作为条件的右值")
	}
	var valueType proto.ValueType
	switch expr.ValueContent.Type {
	case indicator_query.ValueType_NullValue:
		valueType = proto.ValueType_NullValue
	case indicator_query.ValueType_FloatValue:
		valueType = proto.ValueType_FloatValue
	case indicator_query.ValueType_IntegerValue:
		valueType = proto.ValueType_IntegerValue
	case indicator_query.ValueType_StringValue:
		valueType = proto.ValueType_StringValue
	default:
		return result, errors.Errorf("不支持的值类型<%s>", expr.ValueContent.Type.String())
	}
	return proto.ValueContent{
		Type:  valueType,
		Value: expr.ValueContent.Value,
	}, nil
}

func (s *Transformer) queryComparisonOpToSqlBuildOp(opType indicator_query.ComparisonOpType) (proto.OperateType, error) {
	switch opType {
	case indicator_query.ComparisonOpType_Eq:
		return proto.Equal, nil
	case indicator_query.ComparisonOpType_Gt:
		return proto.Gt, nil
	case indicator_query.ComparisonOpType_Gte:
		return proto.Gte, nil
	case indicator_query.ComparisonOpType_Lt:
		return proto.Lt, nil
	case indicator_query.ComparisonOpType_Lte:
		return proto.Lte, nil
	case indicator_query.ComparisonOpType_Neq:
		return proto.NotEqual, nil
	default:
		return "", errors.Errorf("暂不支持的操作符<%s>", opType.String())
	}
}

func (s *Transformer) queryExpressionToSqlBuildField(expr *indicator_query.Expression) (*proto.Field, error) {
	if expr.Type != indicator_query.ExpressionType_Field {
		return nil, errors.Errorf("暂不支持非字段类型表达式<%s>转换为字段", expr.Type.String())
	}
	// TODO(zhud04): format
	sqlBuildField := &proto.Field{
		ModelField: proto.ModelField{
			Catalog:   "",
			Schema:    "",
			Table:     expr.FieldContent.TableName,
			Field:     expr.FieldContent.FieldName,
			FieldCn:   "",
			FieldType: "",
			Comment:   "",
			Alias:     "",
		},
		TimeFormat: proto.TimeFormat{
			TimeFormatType: s.queryTimeFormatToSqlBuildTimeFormat(expr.FieldContent.OpType),
		},
		CalcFunc:      s.queryAggFuncToSqlBuildAggFunc(expr.FieldContent.OpType),
		DimDefinition: common.FieldViewModelMapToDimViewData(s.getDimDefinitionByName(expr.FieldContent.FieldName)),
	}
	//明细模型的话,直接抽出来进行处理
	if s.devMode == string(base.DevModeTypeDetail) {
		var dimensionIns = s.factory.GetDimension(sqlBuildField, s.resourceType)
		refFields, err := dimensionIns.GetRefFields(context.Background())
		if err != nil {
			return nil, err
		}
		if len(refFields) == 0 {
			return nil, errors.Errorf("维度<%s>不存在", expr.FieldContent.FieldName)
		}
		ModelField := refFields[0]
		sqlBuildField = &proto.Field{
			ModelField: ModelField,
		}
	}
	return sqlBuildField, nil
}

func (s *Transformer) getDimDefinitionByName(name string) *base.FieldViewModel {
	viewData := base.FieldViewModel{}
	if indicatorField, ok := s.DimFieldMap[name]; ok {
		_ = json.Unmarshal([]byte(indicatorField.DimViewData), &viewData)
	}

	return &viewData
}

func (s *Transformer) queryAssociateToSqlBuildAssociate(associateType indicator_query.AssociateType) proto.AssociateType {
	switch associateType {
	case indicator_query.AssociateType_And:
		return proto.AND
	case indicator_query.AssociateType_Or:
		return proto.OR
	default:
		return proto.NoAssociate
	}
}

func (s *Transformer) queryTimeFormatToSqlBuildTimeFormat(opType indicator_query.FieldOpType) proto.TimeFormatType {
	switch opType {
	case indicator_query.FieldOpType_NoFieldOpType:
		return proto.TimeFormatTypeNoType
	case indicator_query.FieldOpType_DatetimeFormatDay:
		return proto.TimeFormatTypeExtractDay
	case indicator_query.FieldOpType_DatetimeFormatWeek:
		return proto.TimeFormatTypeExtractWeek
	case indicator_query.FieldOpType_DatetimeFormatMonth:
		return proto.TimeFormatTypeExtractMonth
	case indicator_query.FieldOpType_DatetimeFormatQuarter:
		return proto.TimeFormatTypeExtractQuater
	case indicator_query.FieldOpType_DatetimeFormatYear:
		return proto.TimeFormatTypeExtractYear
	case indicator_query.FieldOpType_DatetimeFormatHour:
		return proto.TimeFormatTypeExtractHour
	case indicator_query.FieldOpType_DatetimeFormatMinute:
		return proto.TimeFormatTypeExtractMinute
	case indicator_query.FieldOpType_DatetimeFormatSecond:
		return proto.TimeFormatTypeExtractSecond
	default:
		return proto.TimeFormatTypeNoType
	}
}

func (s *Transformer) queryAggFuncToSqlBuildAggFunc(opType indicator_query.FieldOpType) proto.CalcFunction {
	switch opType {
	case indicator_query.FieldOpType_AggMax:
		return proto.Max
	case indicator_query.FieldOpType_AggAvg:
		return proto.Avg
	case indicator_query.FieldOpType_AggCount:
		return proto.Count
	case indicator_query.FieldOpType_AggMin:
		return proto.Min
	case indicator_query.FieldOpType_AggSum:
		return proto.SumFunc
	default:
		return proto.NoFunc
	}
}

func (s *Transformer) genIndicatorFromViewContent(alias string, indicatorCode string) (definition *proto.Definition, err error) {
	helper := injection.NewConvertHelper(injection.WithVarValues(&injection.VariableContext{
		s.viewContent.EmptyVarSelectAll,
		s.viewContent.Variables,
		s.req.WithVariables,
	}))
	field, ok := s.IndicatorFieldMap[indicatorCode]
	if !ok {
		return nil, errors.Errorf("指标<%s>在模型字段中不存在", indicatorCode)
	}

	for idx := range s.viewContent.CustomFieldDimensions {
		indicator := &s.viewContent.CustomFieldDimensions[idx]

		if indicator.FieldCode != field.FieldCode {
			continue
		}
		return helper.IndicatorToDefinition(alias, indicator, s.resourceType)
	}
	return nil, errors.Errorf("指标<%s>在模型字段中不存在", indicatorCode)
}

func (s *Transformer) getAdsModelViewContent(ctx context.Context, repo *indicator_repository.IndicatorRepository, cache indicator_cache.IndicatorModelCache) (viewContent *proto.ViewContentNew, err error) {
	indicator, err := repo.GetIndicatorByTableName(ctx, s.req.Query.TableName, string(global.Prod))
	if err != gorm.ErrRecordNotFound && err != nil {
		errMsg := fmt.Sprintf("通过表名:%s查询指标定义失败", s.req.Query.TableName)
		err = errors.Wrap(err, errMsg)
		return nil, err
	}

	if err == gorm.ErrRecordNotFound || indicator.ID == "" {
		err = errors.Errorf("表名<%s>不存在或已删除", s.req.Query.TableName)
		return
	}

	code := indicator.Code
	indicatorModel, err := cache.GetProdAdsViewByCode(ctx, code)
	if err != nil {
		indicatorModel, err = repo.GetIndicatorInfoByCode(ctx, code, string(global.Prod))
		if err != nil {
			err = errors.Wrapf(err, "获取应用表视图模型<%s>失败", code)
			return
		}

		// 未查询到记录
		if indicatorModel == nil || indicatorModel.Code == "" {
			err = errors.New("获取应用表视图模型失败, 模型code为空")
			return
		}
		defer func() {
			cache.SetProdAdsViewByCode(ctx, code, indicatorModel)
		}()
	}

	err = json.Unmarshal([]byte(indicatorModel.ViewContent), &viewContent)
	if err != nil {
		err = errors.Wrapf(err, "解析应用视图ViewContent失败")
		return nil, err
	}

	fields, err := repo.GetAllIndicatorFieldInfoByIndicatorId(ctx, indicator.ID)
	if err != nil {
		errMsg := fmt.Sprintf("获取指标字段错误,模型id:%s", indicator.ID)
		err = errors.Wrapf(err, errMsg)
		return
	}

	for _, field := range fields {
		if len(field.Code) > 0 {
			s.IndicatorFieldMap[field.Code] = field
		} else {
			s.DimFieldMap[field.Name] = field
		}
	}

	return viewContent, nil
}

func (s *Transformer) collectAndFixIndicatorsAndFields(ctx context.Context) (indicatorCodes []string, tableFields []field, err error) {
	//如果是从租户库进来的，不需要做任何处理
	var fields []field
	var codes []string
	queryItems := []*indicator_query.QueryItem{}
	queryItems = append(queryItems, s.req.Query.Indicators...)
	queryItems = append(queryItems, s.req.Query.Dimensions...)
	for _, queryItem := range queryItems {
		fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFieldContent(ctx, queryItem.Expression.FieldContent)
		if err != nil {
			return
		}
		tableFields = append(tableFields, fields...)
		indicatorCodes = append(indicatorCodes, codes...)
	}
	fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFilter(ctx, s.req.Query.Filter)
	if err != nil {
		return
	}
	tableFields = append(tableFields, fields...)
	indicatorCodes = append(indicatorCodes, codes...)
	fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFilter(ctx, s.req.Query.HavingFilter)
	if err != nil {
		return
	}
	tableFields = append(tableFields, fields...)
	indicatorCodes = append(indicatorCodes, codes...)
	tableFields = s.distinctFields(fields)
	indicatorCodes = lo.Uniq(indicatorCodes)
	return
}

func (s *Transformer) distinctFields(fields []field) (distinctFields []field) {
	fieldSet := map[string]struct{}{}
	for _, f := range fields {
		if _, ok := fieldSet[strings.Join([]string{f.TableName, f.FieldName}, ".")]; ok {
			continue
		}
		fieldSet[strings.Join([]string{f.TableName, f.FieldName}, ".")] = struct{}{}
		distinctFields = append(distinctFields, f)
	}
	return
}

func (s *Transformer) collectAndFixIndicatorsAndFieldsFromFilter(ctx context.Context, filter *indicator_query.Filter) (tableFields []field, indicatorCodes []string, err error) {
	if filter == nil || filter.Type == indicator_query.FilterType_NoFilterType {
		return
	}
	if filter.Type == indicator_query.FilterType_FilterGroup {
		for _, subFilter := range filter.SubFilters {
			var fields []field
			var codes []string
			fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFilter(ctx, subFilter)
			if err != nil {
				return
			}
			tableFields = append(tableFields, fields...)
			indicatorCodes = append(indicatorCodes, codes...)
		}
		return
	}
	return s.collectAndFixIndicatorsAndDimensionsFromPredicate(ctx, filter)
}

func (s *Transformer) collectAndFixIndicatorsAndDimensionsFromPredicate(ctx context.Context, filter *indicator_query.Filter) (tableFields []field, indicatorCodes []string, err error) {
	expr := filter.Subject
	var fields []field
	var codes []string
	if expr.Type == indicator_query.ExpressionType_Field {
		fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFieldContent(ctx, expr.FieldContent)
		if err != nil {
			return
		}
		tableFields = append(tableFields, fields...)
		indicatorCodes = append(indicatorCodes, codes...)
	}
	switch filter.Predicate.Type {
	case indicator_query.PredicateType_Comparison:
		if filter.Predicate.Comparison == nil {
			return nil, nil, errors.New("参数Comparison不能为空")
		}
		if filter.Predicate.Comparison.Right == nil {
			return nil, nil, errors.New("参数Comparison.Right不能为空")
		}
		if filter.Predicate.Comparison.Right.Type == indicator_query.ExpressionType_Field {
			fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFieldContent(ctx, expr.FieldContent)
			if err != nil {
				return
			}
			tableFields = append(tableFields, fields...)
			indicatorCodes = append(indicatorCodes, codes...)
		}
	case indicator_query.PredicateType_Between:
		if filter.Predicate.Between == nil {
			return nil, nil, errors.New("参数Between不能为空")
		}
		if filter.Predicate.Between.Lower == nil || filter.Predicate.Between.Upper == nil {
			return nil, nil, errors.New("Between操作符参数不能为空")
		}
		if filter.Predicate.Between.Lower.Type == indicator_query.ExpressionType_Field {
			fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFieldContent(ctx, expr.FieldContent)
			if err != nil {
				return
			}
			tableFields = append(tableFields, fields...)
			indicatorCodes = append(indicatorCodes, codes...)
		}
		if filter.Predicate.Between.Upper.Type == indicator_query.ExpressionType_Field {
			fields, codes, err = s.collectAndFixIndicatorsAndFieldsFromFieldContent(ctx, expr.FieldContent)
			if err != nil {
				return
			}
			tableFields = append(tableFields, fields...)
			indicatorCodes = append(indicatorCodes, codes...)
		}
	case indicator_query.PredicateType_Like, indicator_query.PredicateType_InList:
		return
	default:
		err = errors.Errorf("不支持的predicate类型<%s>", filter.Predicate.Type.String())
		return
	}
	return
}

func (s *Transformer) collectAndFixIndicatorsAndFieldsFromFieldContent(ctx context.Context, exprField *indicator_query.FieldContent) (fields []field, indicatorCodes []string, err error) {
	switch exprField.FieldType {
	case indicator_query.FieldType_Dimension, indicator_query.FieldType_Measure:
		if exprField.TableName == "" {
			err = errors.Errorf("字段表名不能为空")
			return
		}
		if exprField.FieldName == "" {
			err = errors.Errorf("字段字段名不能为空")
			return
		}
		return []field{{exprField.TableName, exprField.FieldName}}, nil, nil
	case indicator_query.FieldType_Indicator:
		if exprField.Code == "" {
			err = errors.Errorf("指标code不能为空")
			return
		}
		return nil, []string{exprField.Code}, nil
	default:
		err = errors.Errorf("字段类型不能为<%s>", exprField.FieldType.String())
		return
	}
}
