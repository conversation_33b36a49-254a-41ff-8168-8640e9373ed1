package ads_common_service

import (
	"context"
	"encoding/json"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"

	db_utils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/saas_db"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal/models/base"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view"
	context2 "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/context"
	ads_view_indicator "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/indicator"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	"gorm.io/gorm"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/errcode"
)

type AdsCommonService struct {
}

func NewAdsCommonService() *AdsCommonService {
	return &AdsCommonService{}
}

func (s *AdsCommonService) ReplaceVariables(ctx context.Context, req *ads_common.ReplaceVariablesRequest) (rsp *ads_common.ReplaceVariablesResponse, err error) {
	global.Logger.ProtoTrace("[AdsCommonService.ReplaceExprVariables]recv request: ", req)
	rsp = new(ads_common.ReplaceVariablesResponse)
	rsp.ErrCode = errcode.ErrorCodeSuccess
	rsp.ErrMsg = "成功"
	rsp.Data = new(ads_common.ReplaceVariablesResponseData)
	var variables []rpc_call.Variable
	err = json.Unmarshal([]byte(req.GetAdsVariablesString()), &variables)
	if err != nil {
		err = errors.Wrapf(err, "解析应用表变量失败")
		return
	}
	varVals := make([]*indicator_query.VariableValue, 0)
	if req.AdsVariableValuesString != "" {
		err = json.Unmarshal([]byte(req.AdsVariableValuesString), &varVals)
		if err != nil {
			return
		}
	}

	var replacer injection.VariableReplacerI = injection.NewVariableReplacer(true, variables, varVals)
	rsp.Data.Sql, err = replacer.ReplaceExprVariables(req.Sql)
	if err != nil {
		return
	}
	return
}

func (s *AdsCommonService) BatchGenSimpleIndicatorExpression(ctx context.Context, req *ads_common.BatchGenSimpleIndicatorExpressionRequest) (rsp *ads_common.BatchGenSimpleIndicatorExpressionResponse, err error) {
	global.Logger.ProtoTrace("[AdsCommonService.BatchGenSimpleIndicatorExpression]recv request: ", req)
	rsp = new(ads_common.BatchGenSimpleIndicatorExpressionResponse)
	rsp.ErrCode = errcode.ErrorCodeSuccess
	rsp.ErrMsg = "成功"
	rsp.Data = []string{}

	var props []*rpc_call.Prop
	err = json.Unmarshal([]byte(req.GetAdsPropsString()), &props)
	if err != nil {
		err = errors.Wrapf(err, "解析应用表指标失败")
		return
	}

	variables := make([]rpc_call.Variable, 0)
	if req.VariablesStr != "" {
		err = json.Unmarshal([]byte(req.VariablesStr), &variables)
		if err != nil {
			return
		}
	}

	helper := injection.NewConvertHelper(injection.WithBrackets(), injection.WithVarValues(&injection.VariableContext{
		EmptyVarSelectAll: true,
		VarDefs:           variables,
		VarVals:           nil,
	}))
	for _, prop := range props {
		var definition *proto.Definition
		definition, err = helper.IndicatorToDefinition("", prop, entities.ProjectResourceType(req.Resource))
		if err != nil {
			return
		}
		var resourceType = entities.ProjectResourceType(req.Resource)
		if req.Resource == "" {
			resourceType = entities.RDSResourceType
		}
		var exprInfo *proto.IndicatorExprInfo
		exprInfo, err = ads_view_indicator.NewFactory().GetIndicator(definition, resourceType).GenViewExpr(ctx)
		if err != nil {
			return
		}
		rsp.Data = append(rsp.Data, exprInfo.Expr)
	}
	return
}

func (s *AdsCommonService) GenAdsRunSql(ctx context.Context, req *ads_common.GenAdsRunSqlRequest) (rsp *ads_common.GenAdsRunSqlResponse, err error) {
	global.Logger.ProtoTrace("[AdsCommonService.GenAdsRunSql]recv request: ", req)
	rsp = new(ads_common.GenAdsRunSqlResponse)
	rsp.ErrCode = errcode.ErrorCodeSuccess
	rsp.ErrMsg = "成功"
	rsp.Data = new(ads_common.GenAdsRunSqlResponseData)

	var db *gorm.DB

	//资产门户
	db, err = db_utils.GetProjectDB(ctx, req.ProjectCode)
	if err != nil {
		err = errors.Wrapf(err, "无法连接租户<%s>数据库", req.ProjectCode)
		return
	}

	repo := indicator_repository.NewIndicatorRepository(db, req.ProjectCode)

	// 生成sql
	var engine entities.ProjectResourceType
	switch req.Engine {
	case ads_common.QueryEngine_StarRocks:
		engine = entities.StarRocksResourceType
	case ads_common.QueryEngine_StarRocksSaaS:
		engine = entities.StarRocksSaaSResourceType
	case ads_common.QueryEngine_RDSSaaS:
		engine = entities.RDSSaaSResourceType
	case ads_common.QueryEngine_RDS:
		engine = entities.RDSResourceType
	case ads_common.QueryEngine_DamengSaas:
		engine = entities.DamengSaaSResourceType
	default:
		err = errors.Errorf("不支持的查询引擎<%s>", req.Engine.String())
	}
	if err != nil {
		return
	}

	c := indicator_cache.NewIndicatorCache(req.ProjectCode)

	var adsViewBuilder *ads_view.Builder
	switch req.DevMode {
	case string(base.DevModeTypeDetail):
		//构建明细查询sql
		detailTransformer := NewDetailTransformer(req, engine)
		err = detailTransformer.PreCheckAndPrepareData(ctx, repo, c)
		if err != nil {
			return
		}
		detailBuildCtx := context2.NewDetailBuildContext(ctx, detailTransformer, detailTransformer,
			context2.WithDetailFilter(detailTransformer),
			context2.WithDetailSort(detailTransformer),
			context2.WithDetailLimit(detailTransformer),
			context2.WithDetailVariables(detailTransformer),
			context2.WithDetailModelRelation(detailTransformer),
		)
		adsViewBuilder = ads_view.NewBuilderForDetail(detailBuildCtx, engine)
	case string(base.DevModeTypeConfig), "":
		// 生成Transformer, 实现指标构建计划需要用到的 DependenceModel, IndicatorDefinition, IndicatorAggregate, IndicatorFilter
		transformer := NewTransformer(req, engine)
		err = transformer.PreCheckAndPrepareData(ctx, repo, c)
		if err != nil {
			return
		}
		// 生成构建计划
		buildCtx := context2.NewBuildContext(ctx, transformer, transformer,
			context2.WithGlobalFilter(transformer),
			context2.WithAggregate(transformer),
			context2.WithSort(transformer),
			context2.WithLimit(transformer),
			context2.WithVariables(transformer))
		adsViewBuilder = ads_view.NewBuilder(buildCtx, engine)
	default:
		err = errors.Errorf("不支持的开发模式<%s>", req.DevMode)
		return
	}

	var sql string
	sql, err = adsViewBuilder.ToSQL()
	if err != nil {
		err = errors.Wrapf(err, "生成sql失败")
		return
	}
	rsp.Data.Sql = sql
	global.Logger.ProtoTrace("[AdsCommonService.GenAdsRunSql]send response: ", rsp)
	return rsp, nil
}

func (s *AdsCommonService) GetPublicDimTable(ctx context.Context, req *ads_common.GetPublicDimTableReq) (rsp *ads_common.GetPublicDimTableRsp, err error) {
	sctx, sp := trace_utils.StartSpanWithContext(ctx, "GetPublicDimTable")
	sp.Name("method: GetPublicDimTable")
	defer sp.End()

	rsp = &ads_common.GetPublicDimTableRsp{
		ErrMsg:  "成功",
		ErrCode: errcode.ErrorCodeSuccess,
	}
	var db *gorm.DB
	db, err = db_utils.GetProjectDB(sctx, req.ProjectCode)
	if err != nil {
		err = errors.Wrapf(err, "无法连接租户<%s>数据库", req.ProjectCode)
		return
	}

	repo := indicator_repository.NewIndicatorRepository(db, req.ProjectCode)

	var injectAdsViewImpl = NewAnalyticDimImpl(req)
	err = injectAdsViewImpl.PreCheckAndPrepareData(sctx, repo)
	if err != nil {
		return
	}
	var buildCtx = context2.NewBuildContext(sctx, injectAdsViewImpl, injectAdsViewImpl,
		context2.WithDimensionsToBeAnalyzed(injectAdsViewImpl))
	query := ads_view.NewQuery(buildCtx)
	var analyticDimension proto.AnalyticDimension
	analyticDimension, err = query.GetAnalyticDimension(sctx)
	if err != nil {
		return
	}
	rsp.Data = &ads_common.GetPublicDimTableRspData{
		Models:     make([]*ads_common.AnalyzableModel, 0),
		Dimensions: make([]*ads_common.Dimension, 0),
	}
	// 输出可分析的底层表和底层表字段
	for _, model := range analyticDimension.Tables {
		var analyticModel = &ads_common.AnalyzableModel{
			TableName: model.Table,
			Category:  string(model.Type),
			Code:      model.Code,
			Fields:    make([]*ads_common.ModelField, 0),
		}
		if dimensions, ok := analyticDimension.TableFields[model.Table]; ok {
			for _, dim := range dimensions {
				analyticModel.Fields = append(analyticModel.Fields, &ads_common.ModelField{
					Name:      dim.Field,
					NameCn:    dim.FieldCn,
					FieldType: dim.FieldType,
				})
			}
		}
		rsp.Data.Models = append(rsp.Data.Models, analyticModel)
	}
	// 输出可分析的维度定义
	for _, dim := range analyticDimension.Dimensions {
		var analyticDim = &ads_common.Dimension{
			Code:     dim.Id,
			Name:     dim.Field,
			NameCn:   dim.FieldCn,
			ViewName: dim.Table,
		}
		rsp.Data.Dimensions = append(rsp.Data.Dimensions, analyticDim)
	}
	return
}

func (s *AdsCommonService) BatchGetPublicDim(ctx context.Context, req *ads_common.BatchGetPublicDimReq) (rsp *ads_common.BatchGetPublicDimRsp, err error) {
	rsp = &ads_common.BatchGetPublicDimRsp{
		ErrMsg:  "成功",
		ErrCode: errcode.ErrorCodeSuccess,
		Data:    new(ads_common.BatchGetPublicDimRspData),
	}
	publicDimTableRsp := &ads_common.GetPublicDimTableRsp{}
	if req.IsCalcAll {
		publicDimTableRsp, err = s.GetPublicDimTable(ctx, &ads_common.GetPublicDimTableReq{
			ProjectCode:    req.ProjectCode,
			TenantCode:     req.TenantCode,
			ViewContent:    req.ViewContent,
			IndicatorCodes: req.IndicatorCodes,
		})
		if err != nil {
			return
		}
		rsp.Data.PublicDimInfos = append(rsp.Data.PublicDimInfos, &ads_common.PublicDimInfo{
			Dimensions: publicDimTableRsp.Data.Dimensions,
		})
		return
	}

	for _, code := range req.IndicatorCodes {
		dimReq := &ads_common.GetPublicDimTableReq{
			ProjectCode:    req.ProjectCode,
			TenantCode:     req.TenantCode,
			ViewContent:    req.ViewContent,
			IndicatorCodes: []string{code},
		}
		publicDimTableRsp, err = s.GetPublicDimTable(ctx, dimReq)
		if err != nil {
			return
		}
		rsp.Data.PublicDimInfos = append(rsp.Data.PublicDimInfos, &ads_common.PublicDimInfo{
			Code:       dimReq.IndicatorCodes[0],
			Dimensions: publicDimTableRsp.Data.Dimensions,
		})
	}

	return
}
