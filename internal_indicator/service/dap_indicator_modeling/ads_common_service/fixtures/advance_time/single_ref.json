{"ads_view_content_string": "{\"where\":[],\"dimensions\":[{\"id\":\"4932abc3-6da2-11ee-bb71-f5b6160811d1\",\"field_code\":\"4932abcb-6da2-11ee-bb71-f5b6160811d1\",\"alias\":\"dim_str_1\",\"specifier\":\"\",\"prop_name\":\"dim_str_1\",\"name_cn\":\"dim_str_1\",\"obj_name\":\"dws_one_to_many\",\"system_field_type\":\"\",\"dim_view_data_front\":{\"get_value_type\":1,\"field_assign_data\":{\"quote_table_name\":\"dim_a\",\"quote_table_name_cn\":\"\",\"quote_field_name\":\"dim_str_1\",\"quote_field_name_cn\":\"\",\"quote_field_type\":\"\",\"is_auto_completion_date\":0}}},{\"id\":\"4932abc3-6da2-11ee-bb71-f5b6160811d2\",\"field_code\":\"4932abcb-6da2-11ee-bb71-f5b6160811d2\",\"alias\":\"dim_advance_dt_by_table\",\"specifier\":\"\",\"prop_name\":\"dim_advance_dt_by_table\",\"name_cn\":\"dim_advance_dt_by_table\",\"obj_name\":\"dws_one_to_many\",\"system_field_type\":\"\",\"dim_view_data_front\":{\"get_value_type\":4,\"advance_datetime_data\":{\"assign_type\":\"assign_by_table\",\"assign_by_table_content\":{\"quote_fields\":[{\"quote_table_name\":\"dwd_a\",\"quote_field_name\":\"dim_dt_1\"},{\"quote_table_name\":\"dwd_b\",\"quote_field_name\":\"dim_dt_1\"}]}}}}],\"dependence\":[{\"category\":\"multi_dim\",\"name\":\"dm_one_to_many\",\"table_name\":\"dm_one_to_many\",\"code_id\":\"bd3bebab-7807-4e12-ba9a-4f8a96912e60\",\"obj_prefix\":\"\",\"catalog\":\"\",\"relation_list\":[]}],\"customFieldDimensions\":[{\"field_code\":\"52d96f10-838a-11ee-903f-016bd05c8d39\",\"code\":\"\",\"mode\":\"advance\",\"obj_name\":\"\",\"prop_name\":\"\",\"func\":\"\",\"props\":null,\"specifier\":\"\",\"name_cn\":\"advance_n2\",\"field_type\":\"\",\"alias\":\"\",\"description\":\"\",\"prop_raw\":\"sum({dwd_a}.[num_int_2])\",\"growth_name\":\"\",\"ratio_name\":\"\",\"value_comment\":\"\",\"convert_field_type\":\"\",\"convert_field_type_params\":{\"length\":0,\"scale\":0},\"object_id\":\"\",\"length\":0,\"prop_fields\":[{\"business_model_field_id\":\"\",\"field_type\":\"\",\"field_business_type\":0,\"table_name\":\"dwd_a\",\"field_name\":\"num_int_2\",\"field_name_cn\":\"\",\"expand_table_name\":\"\"}],\"value\":null,\"system_field_type\":\"\",\"conditions\":[],\"agg_info\":{\"tmpl\":\"${agg_0}\",\"sub_expr_agg_infos\":{\"${agg_0}\":{\"sql_expr\":\"sum(dwd_a.num_int_2)\",\"prop_fields\":[{\"business_model_field_id\":\"\",\"field_type\":\"\",\"field_business_type\":0,\"table_name\":\"dwd_a\",\"field_name\":\"num_int_2\",\"field_name_cn\":\"\",\"expand_table_name\":\"\"}]}}},\"calc_ratio\":false,\"indicator_business_code\":\"\",\"time_value_type\":0}],\"variables\":[]}", "engine": "RDS", "project_code": "sprint_1_5"}