{"ads_view_content_string": "{\n        \"where\": [],\n        \"dimensions\": [\n            {\n                \"id\": \"3e9e1f69-6a84-11ee-9b3f-e7ef903b1633\",\n                \"field_code\": \"3e9e1f6b-6a84-11ee-9b3f-e7ef903b1633\",\n                \"alias\": \"cond_tag\",\n                \"specifier\": \"\",\n                \"prop_name\": \"cond_tag\",\n                \"name_cn\": \"条件标签\",\n                \"obj_name\": \"dws_cond_tag_smoke\",\n                \"dim_view_data_front\": {\n                    \"get_value_type\": 0,\n                    \"field_assign_data\": {\n                        \"quote_table_name\": \"\",\n                        \"quote_table_name_cn\": \"\",\n                        \"quote_field_name\": \"\",\n                        \"quote_field_name_cn\": \"\",\n                        \"quote_field_type\": \"\"\n                    },\n                    \"cond_tag_data\": {\n                        \"id\": \"\",\n                        \"name\": \"\",\n                        \"name_cn\": \"\",\n                        \"field_type\": \"\",\n                        \"quote_field_id\": \"e7afd55e-f1a8-365b-b242-a031bfca160b\",\n                        \"quote_table_name\": \"dwd_hz_soldinfos\",\n                        \"quote_table_name_cn\": \"销售事实\",\n                        \"quote_field_name\": \"SoldAmount\",\n                        \"quote_field_name_cn\": \"销售金额\",\n                        \"quote_field_type\": \"decimal\",\n                        \"query_type\": 0,\n                        \"builtin_func\": 1,\n                        \"group_list\": [\n                            {\n                                \"builtin_func\": 0,\n                                \"group_id\": \"5395141e-6a84-11ee-9b3f-e7ef903b1633\",\n                                \"group_name\": \"有钱人\",\n                                \"group_type\": 2,\n                                \"where\": {\n                                    \"relation\": \"AND\",\n                                    \"info\": [\n                                        {\n                                            \"operate\": \">=\",\n                                            \"val\": \"\",\n                                            \"val_type\": \"\",\n                                            \"range_right_val\": \"\",\n                                            \"range_left_val\": \"\",\n                                            \"fixed_val\": \"100000\",\n                                            \"field\": \"\",\n                                            \"time_interval_val\": {\n                                                \"operate\": \"\",\n                                                \"val\": \"\"\n                                            }\n                                        }\n                                    ]\n                                }\n                            },\n                            {\n                                \"builtin_func\": 0,\n                                \"group_id\": \"6615bb22-6a84-11ee-9b3f-e7ef903b1633\",\n                                \"group_name\": \"中产\",\n                                \"group_type\": 2,\n                                \"where\": {\n                                    \"relation\": \"AND\",\n                                    \"info\": [\n                                        {\n                                            \"operate\": \"<\",\n                                            \"val\": \"\",\n                                            \"val_type\": \"\",\n                                            \"range_right_val\": \"\",\n                                            \"range_left_val\": \"\",\n                                            \"fixed_val\": \"100000\",\n                                            \"field\": \"\",\n                                            \"time_interval_val\": {\n                                                \"operate\": \"\",\n                                                \"val\": \"\"\n                                            }\n                                        },\n                                        {\n                                            \"operate\": \">=\",\n                                            \"val\": \"\",\n                                            \"val_type\": \"\",\n                                            \"range_right_val\": \"\",\n                                            \"range_left_val\": \"\",\n                                            \"fixed_val\": \"10000\",\n                                            \"field\": \"\",\n                                            \"time_interval_val\": {\n                                                \"operate\": \"\",\n                                                \"val\": \"\"\n                                            }\n                                        }\n                                    ]\n                                }\n                            },\n                            {\n                                \"builtin_func\": 0,\n                                \"group_id\": \"53951420-6a84-11ee-9b3f-e7ef903b1633\",\n                                \"group_name\": \"默认分组\",\n                                \"group_type\": 1,\n                                \"where\": {\n                                    \"relation\": \"\",\n                                    \"info\": null\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        ],\n        \"dependence\": [\n            {\n                \"category\": \"dwd\",\n                \"name\": \"销售事实\",\n                \"table_name\": \"dwd_hz_soldinfos\",\n                \"code_id\": \"fc5a69c2-0077-468e-9af3-d11ee0d4109c\",\n                \"obj_prefix\": \"\",\n                \"catalog\": \"\",\n                \"relation_list\": []\n            }\n        ],\n        \"customFieldDimensions\": [\n            {\n                \"field_code\": \"77dafb9a-6a84-11ee-9b3f-e7ef903b1633\",\n                \"code\": \"\",\n                \"mode\": \"simple\",\n                \"obj_name\": \"dwd_hz_soldinfos\",\n                \"prop_name\": \"SoldAmount\",\n                \"func\": \"\",\n                \"props\": null,\n                \"specifier\": \"sum\",\n                \"name_cn\": \"销售额\",\n                \"field_type\": \"\",\n                \"alias\": \"\",\n                \"description\": \"\",\n                \"prop_raw\": \"SUM(  `{dwd_hz_soldinfos}`.`[SoldAmount]`)\",\n                \"growth_name\": \"\",\n                \"ratio_name\": \"\",\n                \"value_comment\": \"\",\n                \"convert_field_type\": \"\",\n                \"convert_field_type_params\": {\n                    \"length\": 0,\n                    \"scale\": 0\n                },\n                \"object_id\": \"\",\n                \"length\": 0,\n                \"prop_fields\": [\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_hz_soldinfos\",\n                        \"field_name\": \"SoldAmount\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    }\n                ],\n                \"value\": null,\n                \"system_field_type\": \"\",\n                \"conditions\": [],\n                \"agg_info\": null,\n                \"calc_ratio\": false,\n                \"indicator_business_code\": \"\",\n                \"time_value_type\": 0\n            }\n        ],\n        \"variables\": []\n    }", "engine": "RDS", "project_code": "sprint_1_5"}