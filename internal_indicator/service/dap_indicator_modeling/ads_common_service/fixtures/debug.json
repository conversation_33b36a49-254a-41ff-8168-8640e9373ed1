{"ads_view_content_string": "{\n        \"global_filter\": [\n            {\n                \"code\": \"\",\n                \"name\": \"\",\n                \"table_name\": \"dim_s_building\",\n                \"filter_id\": \"115e60d8-b8f5-11ee-9070-87f79b8abd98\",\n                \"where\": [\n                    {\n                        \"left\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"operator\": \"\",\n                        \"right\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"logical_relation\": \"\",\n                        \"conditions\": [\n                            {\n                                \"left\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"dim_s_building\",\n                                    \"prop_name\": \"IsBld\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"int\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"\",\n                                    \"system_field_type\": \"BIGINT\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"operator\": \"=\",\n                                \"right\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"\",\n                                    \"prop_name\": \"\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"1\",\n                                    \"system_field_type\": \"\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"logical_relation\": \"\",\n                                \"conditions\": []\n                            }\n                        ]\n                    }\n                ]\n            }\n        ],\n        \"where\": null,\n        \"dimensions\": [\n            {\n                \"alias\": \"公司名称\",\n                \"id\": \"8b74b9fa-b8f5-11ee-af15-fa163e6b4c93\",\n                \"name_cn\": \"公司名称\",\n                \"dim_view_data_front\": {\n                    \"get_value_type\": 1,\n                    \"field_assign_data\": {\n                        \"quote_table_name\": \"dim_s_building\",\n                        \"quote_table_name_cn\": \"楼栋\",\n                        \"quote_field_name\": \"BUName\",\n                        \"quote_field_name_cn\": \"公司名称\",\n                        \"quote_field_type\": \"text\",\n                        \"is_auto_completion_date\": 0\n                    },\n                    \"cond_tag_data\": {\n                        \"id\": \"\",\n                        \"name\": \"\",\n                        \"name_cn\": \"\",\n                        \"field_type\": \"\",\n                        \"quote_field_id\": \"35d7ee8c-15b2-3f04-8092-3cbaff48cca6\",\n                        \"quote_table_name\": \"dim_s_building\",\n                        \"quote_table_name_cn\": \"楼栋\",\n                        \"quote_field_name\": \"BUName\",\n                        \"quote_field_name_cn\": \"公司名称\",\n                        \"quote_field_type\": \"text\",\n                        \"query_type\": 0,\n                        \"builtin_func\": 1,\n                        \"group_list\": []\n                    },\n                    \"builtin_func_data\": {\n                        \"quote_table_name\": \"\",\n                        \"quote_table_name_cn\": \"\",\n                        \"quote_field_name\": \"\",\n                        \"quote_field_name_cn\": \"\",\n                        \"quote_field_type\": \"\",\n                        \"builtin_func\": 0,\n                        \"is_auto_completion_date\": 0\n                    },\n                    \"advance_datetime_data\": {\n                        \"assign_type\": \"\",\n                        \"assign_by_table_content\": {\n                            \"quote_fields\": null\n                        },\n                        \"assign_by_num_content\": {\n                            \"relation_num_groups\": null\n                        },\n                        \"expand\": {\n                            \"minimum_unit\": 0,\n                            \"is_auto_completion_date\": 0\n                        }\n                    }\n                },\n                \"obj_name\": \"dws_s_salegmvandreturnnodate\",\n                \"system_field_type\": \"\",\n                \"prop_name\": \"BUName\"\n            },\n            {\n                \"alias\": \"项目分期名称\",\n                \"id\": \"8b74ba23-b8f5-11ee-af15-fa163e6b4c93\",\n                \"name_cn\": \"项目分期名称\",\n                \"dim_view_data_front\": {\n                    \"get_value_type\": 1,\n                    \"field_assign_data\": {\n                        \"quote_table_name\": \"dim_s_building\",\n                        \"quote_table_name_cn\": \"楼栋\",\n                        \"quote_field_name\": \"ProjName\",\n                        \"quote_field_name_cn\": \"项目分期名称\",\n                        \"quote_field_type\": \"text\",\n                        \"is_auto_completion_date\": 0\n                    },\n                    \"cond_tag_data\": {\n                        \"id\": \"\",\n                        \"name\": \"\",\n                        \"name_cn\": \"\",\n                        \"field_type\": \"\",\n                        \"quote_field_id\": \"2545277c-789f-3870-ac47-a0309e7727ea\",\n                        \"quote_table_name\": \"dim_s_building\",\n                        \"quote_table_name_cn\": \"楼栋\",\n                        \"quote_field_name\": \"ProjName\",\n                        \"quote_field_name_cn\": \"项目分期名称\",\n                        \"quote_field_type\": \"text\",\n                        \"query_type\": 0,\n                        \"builtin_func\": 1,\n                        \"group_list\": []\n                    },\n                    \"builtin_func_data\": {\n                        \"quote_table_name\": \"\",\n                        \"quote_table_name_cn\": \"\",\n                        \"quote_field_name\": \"\",\n                        \"quote_field_name_cn\": \"\",\n                        \"quote_field_type\": \"\",\n                        \"builtin_func\": 0,\n                        \"is_auto_completion_date\": 0\n                    },\n                    \"advance_datetime_data\": {\n                        \"assign_type\": \"\",\n                        \"assign_by_table_content\": {\n                            \"quote_fields\": null\n                        },\n                        \"assign_by_num_content\": {\n                            \"relation_num_groups\": null\n                        },\n                        \"expand\": {\n                            \"minimum_unit\": 0,\n                            \"is_auto_completion_date\": 0\n                        }\n                    }\n                },\n                \"obj_name\": \"dws_s_salegmvandreturnnodate\",\n                \"system_field_type\": \"\",\n                \"prop_name\": \"ProjName\"\n            }\n        ],\n        \"dependence\": [\n            {\n                \"category\": \"multi_dim\",\n                \"name\": \"销售累计汇总宽表-楼栋汇总指标(无日期区间指标)\",\n                \"table_name\": \"dm_s_sale_building_nodate\",\n                \"code_id\": \"3cea2a83-5591-4f5c-b46e-01178f244aba\",\n                \"obj_prefix\": \"\",\n                \"catalog\": \"\",\n                \"relation_list\": []\n            }\n        ],\n        \"customFieldDimensions\": [\n            {\n                \"field_code\": \"d859bd22-8205-11ee-9e63-3dbc9e668e00\",\n                \"code\": \"\",\n                \"mode\": \"simple\",\n                \"obj_name\": \"dwd_s_fee\",\n                \"prop_name\": \"RmbYe\",\n                \"func\": \"\",\n                \"props\": null,\n                \"specifier\": \"sum\",\n                \"name_cn\": \"本年应收未收未到期房款合计\",\n                \"field_type\": \"\",\n                \"alias\": \"\",\n                \"description\": \"\",\n                \"prop_raw\": \"SUM(  CASE  WHEN ((`{dwd_s_fee}`.`[TradeStatus]` = '激活') AND ((`{dwd_s_fee}`.`[IsFk]` = 1) OR (`{dwd_s_fee}`.`[ItemType]` = '补充协议款')) AND (((cast(date_format(`{dwd_s_fee}`.`[LastDate]`,'%Y-%m-%d %H:%i:%S') as datetime)  >= date_trunc('year', now()) ) AND (cast(date_format(`{dwd_s_fee}`.`[LastDate]`,'%Y-%m-%d %H:%i:%S') as datetime)  < DATE_ADD(date_trunc('year', now()) ,INTERVAL 1 year))) AND (date_format(`{dwd_s_fee}`.`[LastDate]`, '%Y-%m-%d') >= date_format(now(), '%Y-%m-%d')))) THEN  `{dwd_s_fee}`.`[RmbYe]` ELSE NULL END)\",\n                \"growth_name\": \"\",\n                \"ratio_name\": \"\",\n                \"value_comment\": \"\",\n                \"convert_field_type\": \"\",\n                \"convert_field_type_params\": {\n                    \"length\": 0,\n                    \"scale\": 0\n                },\n                \"object_id\": \"\",\n                \"length\": 0,\n                \"prop_fields\": [\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_s_fee\",\n                        \"field_name\": \"TradeStatus\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    },\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_s_fee\",\n                        \"field_name\": \"IsFk\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    },\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_s_fee\",\n                        \"field_name\": \"ItemType\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    },\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_s_fee\",\n                        \"field_name\": \"LastDate\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    },\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_s_fee\",\n                        \"field_name\": \"RmbYe\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    }\n                ],\n                \"value\": null,\n                \"system_field_type\": \"\",\n                \"conditions\": [\n                    {\n                        \"left\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"operator\": \"\",\n                        \"right\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"logical_relation\": \"\",\n                        \"conditions\": [\n                            {\n                                \"left\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"dwd_s_fee\",\n                                    \"prop_name\": \"TradeStatus\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"multi_text\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"\",\n                                    \"system_field_type\": \"STRING\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"operator\": \"=\",\n                                \"right\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"\",\n                                    \"prop_name\": \"\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"激活\",\n                                    \"system_field_type\": \"\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"logical_relation\": \"\",\n                                \"conditions\": []\n                            }\n                        ]\n                    },\n                    {\n                        \"left\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"operator\": \"\",\n                        \"right\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"logical_relation\": \"AND\",\n                        \"conditions\": [\n                            {\n                                \"left\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"dwd_s_fee\",\n                                    \"prop_name\": \"IsFk\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"int\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"\",\n                                    \"system_field_type\": \"BIGINT\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"operator\": \"=\",\n                                \"right\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"\",\n                                    \"prop_name\": \"\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"1\",\n                                    \"system_field_type\": \"\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"logical_relation\": \"\",\n                                \"conditions\": []\n                            },\n                            {\n                                \"left\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"dwd_s_fee\",\n                                    \"prop_name\": \"ItemType\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"multi_text\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"\",\n                                    \"system_field_type\": \"STRING\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"operator\": \"=\",\n                                \"right\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"\",\n                                    \"prop_name\": \"\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"补充协议款\",\n                                    \"system_field_type\": \"\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"logical_relation\": \"OR\",\n                                \"conditions\": []\n                            }\n                        ]\n                    },\n                    {\n                        \"left\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"operator\": \"\",\n                        \"right\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"logical_relation\": \"AND\",\n                        \"conditions\": [\n                            {\n                                \"left\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"dwd_s_fee\",\n                                    \"prop_name\": \"LastDate\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"datetime\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"\",\n                                    \"system_field_type\": \"DATETIME\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"operator\": \"FROM_YEAR\",\n                                \"right\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"\",\n                                    \"prop_name\": \"\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": 0,\n                                    \"system_field_type\": \"\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 1\n                                },\n                                \"logical_relation\": \"\",\n                                \"conditions\": []\n                            },\n                            {\n                                \"left\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"dwd_s_fee\",\n                                    \"prop_name\": \"LastDate\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"datetime\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"\",\n                                    \"system_field_type\": \"DATETIME\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 0\n                                },\n                                \"operator\": \">=\",\n                                \"right\": {\n                                    \"field_code\": \"\",\n                                    \"code\": \"\",\n                                    \"mode\": \"\",\n                                    \"obj_name\": \"\",\n                                    \"prop_name\": \"\",\n                                    \"func\": \"\",\n                                    \"props\": [],\n                                    \"specifier\": \"\",\n                                    \"name_cn\": \"\",\n                                    \"field_type\": \"\",\n                                    \"alias\": \"\",\n                                    \"description\": \"\",\n                                    \"prop_raw\": \"\",\n                                    \"growth_name\": \"\",\n                                    \"ratio_name\": \"\",\n                                    \"value_comment\": \"\",\n                                    \"convert_field_type\": \"\",\n                                    \"convert_field_type_params\": {\n                                        \"length\": 0,\n                                        \"scale\": 0\n                                    },\n                                    \"object_id\": \"\",\n                                    \"length\": 0,\n                                    \"prop_fields\": null,\n                                    \"value\": \"2\",\n                                    \"system_field_type\": \"\",\n                                    \"conditions\": null,\n                                    \"agg_info\": null,\n                                    \"calc_ratio\": false,\n                                    \"indicator_business_code\": \"\",\n                                    \"time_value_type\": 1\n                                },\n                                \"logical_relation\": \"AND\",\n                                \"conditions\": []\n                            }\n                        ]\n                    }\n                ],\n                \"agg_info\": null,\n                \"calc_ratio\": false,\n                \"indicator_business_code\": \"\",\n                \"time_value_type\": 0\n            }\n        ],\n        \"variables\": []\n    }", "engine": "StarRocks", "project_code": "sprint_1_5_hi"}