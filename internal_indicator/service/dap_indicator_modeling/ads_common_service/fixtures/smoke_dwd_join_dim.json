{"ads_view_content_string": "{\n        \"where\": [],\n        \"dimensions\": [\n            {\n                \"id\": \"47469fbd-6a81-11ee-9b3f-e7ef903b1633\",\n                \"field_code\": \"47469fc3-6a81-11ee-9b3f-e7ef903b1633\",\n                \"alias\": \"building\",\n                \"specifier\": \"\",\n                \"prop_name\": \"building\",\n                \"name_cn\": \"楼栋\",\n                \"obj_name\": \"dws_dwd_related_dim_smoke\",\n                \"dim_view_data_front\": {\n                    \"get_value_type\": 0,\n                    \"field_assign_data\": {\n                        \"quote_table_name\": \"\",\n                        \"quote_table_name_cn\": \"\",\n                        \"quote_field_name\": \"\",\n                        \"quote_field_name_cn\": \"\",\n                        \"quote_field_type\": \"\"\n                    },\n                    \"cond_tag_data\": {\n                        \"id\": \"\",\n                        \"name\": \"\",\n                        \"name_cn\": \"\",\n                        \"field_type\": \"\",\n                        \"quote_field_id\": \"483cb053-63f7-3b8c-aaad-83ae4e9dad9e\",\n                        \"quote_table_name\": \"dim_building_month\",\n                        \"quote_table_name_cn\": \"楼栋月份\",\n                        \"quote_field_name\": \"MasterBuildingGUID\",\n                        \"quote_field_name_cn\": \"MasterBuildingGUID\",\n                        \"quote_field_type\": \"text\",\n                        \"query_type\": 0,\n                        \"builtin_func\": 1,\n                        \"group_list\": []\n                    }\n                }\n            },\n            {\n                \"id\": \"52e51387-6a81-11ee-9b3f-e7ef903b1633\",\n                \"field_code\": \"52e5138e-6a81-11ee-9b3f-e7ef903b1633\",\n                \"alias\": \"rq\",\n                \"specifier\": \"\",\n                \"prop_name\": \"rq\",\n                \"name_cn\": \"统计日期\",\n                \"obj_name\": \"dws_dwd_related_dim_smoke\",\n                \"dim_view_data_front\": {\n                    \"get_value_type\": 0,\n                    \"field_assign_data\": {\n                        \"quote_table_name\": \"\",\n                        \"quote_table_name_cn\": \"\",\n                        \"quote_field_name\": \"\",\n                        \"quote_field_name_cn\": \"\",\n                        \"quote_field_type\": \"\"\n                    },\n                    \"cond_tag_data\": {\n                        \"id\": \"\",\n                        \"name\": \"\",\n                        \"name_cn\": \"\",\n                        \"field_type\": \"\",\n                        \"quote_field_id\": \"4bb077ef-96db-3007-b98a-f9f6eb2355ee\",\n                        \"quote_table_name\": \"dim_building_month\",\n                        \"quote_table_name_cn\": \"楼栋月份\",\n                        \"quote_field_name\": \"rq\",\n                        \"quote_field_name_cn\": \"rq\",\n                        \"quote_field_type\": \"date\",\n                        \"query_type\": 0,\n                        \"builtin_func\": 0,\n                        \"group_list\": []\n                    }\n                }\n            }\n        ],\n        \"dependence\": [\n            {\n                \"category\": \"multi_dim\",\n                \"name\": \"事实关联维度\",\n                \"table_name\": \"multi_dim_dwd_related_dim\",\n                \"code_id\": \"a574bb80-9fdc-4343-a37d-9e503b255711\",\n                \"obj_prefix\": \"\",\n                \"catalog\": \"\",\n                \"relation_list\": []\n            }\n        ],\n        \"customFieldDimensions\": [\n            {\n                \"field_code\": \"5d0ab0ca-6a81-11ee-9b3f-e7ef903b1633\",\n                \"code\": \"\",\n                \"mode\": \"simple\",\n                \"obj_name\": \"dwd_hz_soldinfos\",\n                \"prop_name\": \"SoldAmount\",\n                \"func\": \"\",\n                \"props\": null,\n                \"specifier\": \"sum\",\n                \"name_cn\": \"销售额\",\n                \"field_type\": \"\",\n                \"alias\": \"\",\n                \"description\": \"\",\n                \"prop_raw\": \"SUM(  `{dwd_hz_soldinfos}`.`[SoldAmount]`)\",\n                \"growth_name\": \"\",\n                \"ratio_name\": \"\",\n                \"value_comment\": \"\",\n                \"convert_field_type\": \"\",\n                \"convert_field_type_params\": {\n                    \"length\": 0,\n                    \"scale\": 0\n                },\n                \"object_id\": \"\",\n                \"length\": 0,\n                \"prop_fields\": [\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_hz_soldinfos\",\n                        \"field_name\": \"SoldAmount\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    }\n                ],\n                \"value\": null,\n                \"system_field_type\": \"\",\n                \"conditions\": [],\n                \"agg_info\": null,\n                \"calc_ratio\": false,\n                \"indicator_business_code\": \"\",\n                \"time_value_type\": 0\n            }\n        ],\n        \"variables\": []\n    }", "engine": "RDS", "project_code": "sprint_1_5"}