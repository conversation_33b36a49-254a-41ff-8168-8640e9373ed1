{"view_content": "{\n        \"where\": [\n            {\n                \"left\": {\n                    \"field_code\": \"\",\n                    \"code\": \"\",\n                    \"mode\": \"\",\n                    \"obj_name\": \"\",\n                    \"prop_name\": \"\",\n                    \"func\": \"\",\n                    \"props\": [],\n                    \"specifier\": \"\",\n                    \"name_cn\": \"\",\n                    \"field_type\": \"\",\n                    \"alias\": \"\",\n                    \"description\": \"\",\n                    \"prop_raw\": \"\",\n                    \"growth_name\": \"\",\n                    \"ratio_name\": \"\",\n                    \"value_comment\": \"\",\n                    \"convert_field_type\": \"\",\n                    \"convert_field_type_params\": {\n                        \"length\": 0,\n                        \"scale\": 0\n                    },\n                    \"object_id\": \"\",\n                    \"length\": 0,\n                    \"prop_fields\": null,\n                    \"value\": \"\",\n                    \"system_field_type\": \"\",\n                    \"conditions\": null,\n                    \"agg_info\": null,\n                    \"calc_ratio\": false,\n                    \"indicator_business_code\": \"\",\n                    \"time_value_type\": 0\n                },\n                \"operator\": \"\",\n                \"right\": {\n                    \"field_code\": \"\",\n                    \"code\": \"\",\n                    \"mode\": \"\",\n                    \"obj_name\": \"\",\n                    \"prop_name\": \"\",\n                    \"func\": \"\",\n                    \"props\": [],\n                    \"specifier\": \"\",\n                    \"name_cn\": \"\",\n                    \"field_type\": \"\",\n                    \"alias\": \"\",\n                    \"description\": \"\",\n                    \"prop_raw\": \"\",\n                    \"growth_name\": \"\",\n                    \"ratio_name\": \"\",\n                    \"value_comment\": \"\",\n                    \"convert_field_type\": \"\",\n                    \"convert_field_type_params\": {\n                        \"length\": 0,\n                        \"scale\": 0\n                    },\n                    \"object_id\": \"\",\n                    \"length\": 0,\n                    \"prop_fields\": null,\n                    \"value\": \"\",\n                    \"system_field_type\": \"\",\n                    \"conditions\": null,\n                    \"agg_info\": null,\n                    \"calc_ratio\": false,\n                    \"indicator_business_code\": \"\",\n                    \"time_value_type\": 0\n                },\n                \"logical_relation\": \"\",\n                \"conditions\": [\n                    {\n                        \"left\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"dim_pub_address\",\n                            \"prop_name\": \"prov_code\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"text\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"STRING\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"operator\": \"IS NOT NULL\",\n                        \"right\": {\n                            \"field_code\": \"\",\n                            \"code\": \"\",\n                            \"mode\": \"\",\n                            \"obj_name\": \"\",\n                            \"prop_name\": \"\",\n                            \"func\": \"\",\n                            \"props\": [],\n                            \"specifier\": \"\",\n                            \"name_cn\": \"\",\n                            \"field_type\": \"\",\n                            \"alias\": \"\",\n                            \"description\": \"\",\n                            \"prop_raw\": \"\",\n                            \"growth_name\": \"\",\n                            \"ratio_name\": \"\",\n                            \"value_comment\": \"\",\n                            \"convert_field_type\": \"\",\n                            \"convert_field_type_params\": {\n                                \"length\": 0,\n                                \"scale\": 0\n                            },\n                            \"object_id\": \"\",\n                            \"length\": 0,\n                            \"prop_fields\": null,\n                            \"value\": \"\",\n                            \"system_field_type\": \"\",\n                            \"conditions\": null,\n                            \"agg_info\": null,\n                            \"calc_ratio\": false,\n                            \"indicator_business_code\": \"\",\n                            \"time_value_type\": 0\n                        },\n                        \"logical_relation\": \"\",\n                        \"conditions\": []\n                    }\n                ]\n            }\n        ],\n        \"dimensions\": [],\n        \"dependence\": [\n            {\n                \"category\": \"multi_dim\",\n                \"name\": \"三层模型验证-关系集\",\n                \"table_name\": \"dm_three_model_test_1120\",\n                \"code_id\": \"41bacdd1-4f74-4e6f-9c2f-f6cd0495066c\",\n                \"obj_prefix\": \"\",\n                \"catalog\": \"\",\n                \"relation_list\": []\n            }\n        ],\n        \"customFieldDimensions\": [\n            {\n                \"field_code\": \"257c89af-8782-11ee-b443-3d328a8126f1\",\n                \"code\": \"\",\n                \"mode\": \"simple\",\n                \"obj_name\": \"dwd_expect_business_order_detail_di\",\n                \"prop_name\": \"order_id\",\n                \"func\": \"\",\n                \"props\": null,\n                \"specifier\": \"count\",\n                \"name_cn\": \"指标5\",\n                \"field_type\": \"\",\n                \"alias\": \"\",\n                \"description\": \"\",\n                \"prop_raw\": \"COUNT(  `{dwd_expect_business_order_detail_di}`.`[order_id]`)\",\n                \"growth_name\": \"\",\n                \"ratio_name\": \"\",\n                \"value_comment\": \"\",\n                \"convert_field_type\": \"\",\n                \"convert_field_type_params\": {\n                    \"length\": 0,\n                    \"scale\": 0\n                },\n                \"object_id\": \"\",\n                \"length\": 0,\n                \"prop_fields\": [\n                    {\n                        \"business_model_field_id\": \"\",\n                        \"field_type\": \"\",\n                        \"field_business_type\": 0,\n                        \"table_name\": \"dwd_expect_business_order_detail_di\",\n                        \"field_name\": \"order_id\",\n                        \"field_name_cn\": \"\",\n                        \"expand_table_name\": \"\"\n                    }\n                ],\n                \"value\": null,\n                \"system_field_type\": \"\",\n                \"conditions\": [],\n                \"agg_info\": null,\n                \"calc_ratio\": false,\n                \"indicator_business_code\": \"\",\n                \"time_value_type\": 0\n            }\n        ],\n        \"variables\": []\n    }", "project_code": "sprint_1_5"}