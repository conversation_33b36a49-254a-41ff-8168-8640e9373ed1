package ads_common_service

import (
	"context"
	"encoding/json"

	"github.com/golang/protobuf/jsonpb"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	commonUtils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
)

var _ injection.DependenceModel = (*Transformer)(nil)
var _ injection.IndicatorAggregate = (*Transformer)(nil)
var _ injection.GlobalFilter = (*Transformer)(nil)
var _ injection.IndicatorDefinition = (*Transformer)(nil)
var _ injection.IndicatorSort = (*Transformer)(nil)
var _ injection.QueryLimit = (*Transformer)(nil)
var _ injection.Variables = (*Transformer)(nil)

type Transformer struct {
	*injection.DefaultDepModelImpl
	*injection.DefaultIndicatorFilterImpl
	resourceType entities.ProjectResourceType
	req          *ads_common.GenAdsRunSqlRequest // 数见查询请求
	// context
	viewContent *proto.ViewContentNew // 应用表ViewContent
}

func NewTransformer(req *ads_common.GenAdsRunSqlRequest, resourceType entities.ProjectResourceType) *Transformer {
	return &Transformer{
		req:          req,
		resourceType: resourceType,
	}
}

// PreCheckAndPrepareData 对查询结构进行检查并从数据库获取必要数据
func (s *Transformer) PreCheckAndPrepareData(ctx context.Context, repo *indicator_repository.IndicatorRepository, cache indicator_cache.IndicatorModelCache) (err error) {
	withVariables := lo.Map(s.req.GetWithVariablesString(), func(item string, _ int) *indicator_query.VariableValue {
		var v indicator_query.VariableValue
		_ = jsonpb.UnmarshalString(item, &v)
		return &v
	})

	err = json.Unmarshal([]byte(s.req.GetAdsViewContentString()), &s.viewContent)
	if err != nil {
		err = errors.Wrapf(err, "解析应用表ViewContent失败")
		return err
	}

	// 从数据库中取相关数据
	if s.viewContent.Dependence == nil {
		return errors.New("服务器内部错误, 应用表依赖模型不存在")
	}

	// ads不需要缓存
	s.DefaultDepModelImpl = injection.NewDefaultDepModelImpl(s.viewContent.Dependence, &s.viewContent.Dependence.RelationalModel)
	s.DefaultIndicatorFilterImpl = injection.NewDefaultIndicatorFilterImpl(s.viewContent, withVariables)
	return s.DefaultDepModelImpl.PreLoad(ctx, repo, string(global.Prod))
}

// ============================== IndicatorDefinitionImpl ==============================
func (s *Transformer) GetDefinitions(ctx context.Context) ([]*proto.Definition, error) {
	helper := injection.NewConvertHelper(injection.WithUseCast(), injection.WithVarValues(&injection.VariableContext{
		s.viewContent.EmptyVarSelectAll,
		s.viewContent.Variables,
		nil,
	}))
	result := []*proto.Definition{}
	for idx := range s.viewContent.CustomFieldDimensions {
		indicator := &s.viewContent.CustomFieldDimensions[idx]
		definition, err := helper.IndicatorToDefinition(indicator.Alias, indicator, s.resourceType)
		if err != nil {
			return nil, err
		}
		result = append(result, definition)
	}
	global.Logger.JsonTrace("[AdsQuery] GetDefinitions Result: ", result)
	return result, nil
}

// ============================== IndicatorSortImpl ==============================
func (s *Transformer) GetSort(ctx context.Context) ([]*proto.Sort, error) {
	return nil, nil
}

// ============================== IndicatorLimitImpl ==============================
func (s *Transformer) GetLimit(ctx context.Context) (*proto.Limit, error) {
	result := new(proto.Limit)
	global.Logger.JsonTrace("[AdsQuery] GetLimit Result: ", result)
	return result, nil
}

// ============================== IndicatorAggregateImpl ==============================
func (s *Transformer) GetAggregate(ctx context.Context) (*proto.Aggregate, error) {
	result := new(proto.Aggregate)
	for _, dimension := range s.viewContent.Dimensions {
		// 别称设置策略: 优先使用别名, 其次使用中文名, 最后使用字段名
		var alias = dimension.Alias
		if alias == "" {
			alias = dimension.NameCn
		}
		if alias == "" {
			alias = dimension.PropName
		}
		result.Fields = append(result.Fields, &proto.Field{
			ModelField: proto.ModelField{
				Table: dimension.ObjName,
				Field: dimension.PropName,
				Alias: alias,
			},
			CastType: proto.CastType{
				Type:      dimension.ConvertFieldType,
				Length:    dimension.ConvertFieldTypeParams.Length,
				Precision: dimension.ConvertFieldTypeParams.Scale,
			},
			TimeFormat: proto.TimeFormat{
				TimeFormatType: s.viewContentDimensionTimeFormatToSqlBuildTimeFormat(string(dimension.Specifier)),
			},
			SourceExpress:      dimension.ViewExpr,
			RefFields:          common.WrapModelFields(dimension.RefFields),
			IsEmptyPlaceholder: !dimension.IsPublic,
			DimDefinition:      commonUtils.FieldViewModelMapToDimViewData(dimension.ViewModel),
		})
	}
	global.Logger.JsonTrace("[AdsQuery] GetAggregate Result: ", result)
	return result, nil
}

// ============================== Internal ==============================
func (s *Transformer) getDependenceTableName() string {
	return s.viewContent.Dependence.TableName
}

func (s *Transformer) genSqlExpress(indicator *rpc_call.Prop) (expr string) {
	return RegMatchAndReplaceTableAndField(indicator.PropRaw)
}

func (s *Transformer) viewContentDimensionTimeFormatToSqlBuildTimeFormat(specifier string) proto.TimeFormatType {
	switch proto.TimeFormatSpecifier(specifier) {
	case proto.YearTimeFormatSpecifier:
		return proto.TimeFormatTypeExtractYear
	case proto.MonthTimeFormatSpecifier:
		return proto.TimeFormatTypeExtractMonth
	case proto.QuarterTimeFormatSpecifier:
		return proto.TimeFormatTypeExtractQuater
	case proto.DayTimeFormatSpecifier:
		return proto.TimeFormatTypeExtractDay
	default:
		return proto.TimeFormatTypeNoType
	}
}
