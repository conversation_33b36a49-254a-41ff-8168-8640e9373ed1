package ads_common_service

import (
	"fmt"
	"regexp"
)

var customTableExprPattern = regexp.MustCompile(`\{([a-zA-Z0-9_]+?)\}`)
var customFieldExprPattern = regexp.MustCompile(`\[([a-zA-Z0-9_]+?)\]`)

// RegGetTableExpr 解析表达式获取引用表
func RegGetTableExpr(propRaw string) []string {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var matchTableRes []string
	result := customTableExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchTableRes = append(matchTableRes, matchItem[1])
		}
	}
	return matchTableRes
}

// RegGetFieldExpr 解析表达式获取引用字段
func RegGetFieldExpr(propRaw string) []string {
	// 结果是[][]string结构，[]string中第2个为真实匹配到的元素
	var matchFieldRes []string
	result := customFieldExprPattern.FindAllStringSubmatch(propRaw, -1)
	for _, matchItem := range result {
		if len(matchItem) > 1 {
			matchFieldRes = append(matchFieldRes, matchItem[1])
		}
	}
	return matchFieldRes
}

// RegMatchAndReplaceTableAndField 解析表达式并替换为真实字段名
func RegMatchAndReplaceTableAndField(propRaw string) (newPropRaw string) {
	matchRes := RegGetTableExpr(propRaw)
	// 去除花括号，获取到真实表名
	newPropRaw = propRaw
	for _, matchItem := range matchRes {
		patternItem := fmt.Sprintf(`\{(%s)\}`, matchItem)
		reItem := regexp.MustCompile(patternItem)
		newPropRaw = reItem.ReplaceAllString(newPropRaw, matchItem)
	}
	matchRes = RegGetFieldExpr(propRaw)
	// 去除中括号，获取到真实字段名
	for _, matchItem := range matchRes {
		patternItem := fmt.Sprintf(`\[(%s)\]`, matchItem)
		reItem := regexp.MustCompile(patternItem)
		newPropRaw = reItem.ReplaceAllString(newPropRaw, matchItem)
	}
	return
}
