package ads_common_service

import (
	"context"
	"encoding/json"

	"github.com/golang/protobuf/jsonpb"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/indicator_query"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/redis/indicator_cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/dimension"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
	commonUtils "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"
)

var _ injection.DependenceModel = (*DetailTransformer)(nil)
var _ injection.DetailDefinition = (*DetailTransformer)(nil)
var _ injection.DetailSort = (*DetailTransformer)(nil)
var _ injection.QueryLimit = (*DetailTransformer)(nil)
var _ injection.Variables = (*DetailTransformer)(nil)
var _ injection.DetailFilter = (*DetailTransformer)(nil)
var _ injection.DetailModelRelation = (*DetailTransformer)(nil)

type DetailTransformer struct {
	*injection.DefaultDepModelImpl
	*injection.DefaultIndicatorFilterImpl
	resourceType entities.ProjectResourceType
	req          *ads_common.GenAdsRunSqlRequest

	// context
	viewContent *proto.ViewContentNew // 应用表ViewContent
	factory     *dimension.Factory
}

func NewDetailTransformer(req *ads_common.GenAdsRunSqlRequest, resourceType entities.ProjectResourceType) *DetailTransformer {
	return &DetailTransformer{
		req:          req,
		resourceType: resourceType,
		factory:      dimension.NewFactory(),
	}
}

// PreCheckAndPrepareData 对查询结构进行检查并从数据库获取必要数据
func (s *DetailTransformer) PreCheckAndPrepareData(ctx context.Context, repo *indicator_repository.IndicatorRepository, cache indicator_cache.IndicatorModelCache) (err error) {
	withVariables := lo.Map(s.req.GetWithVariablesString(), func(item string, _ int) *indicator_query.VariableValue {
		var v indicator_query.VariableValue
		_ = jsonpb.UnmarshalString(item, &v)
		return &v
	})

	err = json.Unmarshal([]byte(s.req.GetAdsViewContentString()), &s.viewContent)
	if err != nil {
		err = errors.Wrapf(err, "解析应用表ViewContent失败")
		return err
	}

	// 从数据库中取相关数据
	if s.viewContent.Dependence == nil {
		return errors.New("服务器内部错误, 应用表依赖模型不存在")
	}

	s.DefaultDepModelImpl = injection.NewDefaultDepModelImpl(s.viewContent.Dependence,
		&s.viewContent.Dependence.RelationalModel,
		injection.WithLoadCache(cache),
	)
	s.DefaultIndicatorFilterImpl = injection.NewDefaultIndicatorFilterImpl(s.viewContent, withVariables)
	return s.DefaultDepModelImpl.PreLoad(ctx, repo, string(global.Prod))
}

func (s *DetailTransformer) GetDetailModelRelation(ctx context.Context) ([]*proto.DetailModelRelationInfo, error) {
	list := make([]*proto.DetailModelRelationInfo, 0)
	for _, v := range s.viewContent.DetailRelationList {
		list = append(list, &proto.DetailModelRelationInfo{
			TableName: v.TableName,
			Code:      v.CodeId,
		})
	}
	return list, nil
}

// ============================== DetailDefinitionImpl ==============================

func (s *DetailTransformer) GetAdditionalField(ctx context.Context) ([]*proto.Field, error) {
	result := make([]*proto.Field, 0)
	for _, dimension := range s.viewContent.Dimensions {
		alias := dimension.PropName
		var field = &proto.Field{
			ModelField: proto.ModelField{
				Table: dimension.ObjName,
				Field: dimension.PropName,
				Alias: alias,
			},
			CastType: proto.CastType{
				Type:      dimension.ConvertFieldType,
				Length:    dimension.ConvertFieldTypeParams.Length,
				Precision: dimension.ConvertFieldTypeParams.Scale,
			},
			SourceExpress:      dimension.ViewExpr,
			RefFields:          common.WrapModelFields(dimension.RefFields),
			IsEmptyPlaceholder: !dimension.IsPublic,
			DimDefinition:      commonUtils.FieldViewModelMapToDimViewData(dimension.ViewModel),
		}

		var dimensionIns = s.factory.GetDimension(field, s.resourceType)
		refFields, err := dimensionIns.GetRefFields(ctx)
		if err != nil {
			return nil, err
		}
		if len(refFields) == 0 {
			continue
		}
		ModelField := refFields[0]
		ModelField.Alias = alias
		result = append(result, &proto.Field{
			ModelField: ModelField,
		})
	}
	return result, nil
}

// ============================== DetailSortImpl ==============================

func (s *DetailTransformer) GetDetailSort(ctx context.Context) ([]*proto.Sort, error) {
	result := []*proto.Sort{}
	return result, nil
}

// ============================== QueryLimitImpl ==============================

const DEFAULT_LIMIT = 500

func (s *DetailTransformer) GetLimit(ctx context.Context) (*proto.Limit, error) {
	result := new(proto.Limit)
	return result, nil
}

// ============================== DetailFilterImpl ==============================

func (s *DetailTransformer) GetDetailFilter(ctx context.Context) ([]*proto.Filter, error) {
	result := make([]*proto.Filter, 0)
	mapFilter, err := s.GetGlobalFilter(ctx)
	if err != nil {
		return nil, err
	}

	isFirst := true
	for _, filter := range mapFilter {
		if len(result) != 0 {
			isFirst = false
		}
		newFilter := &proto.Filter{
			SubFilter: filter,
		}
		if !isFirst {
			newFilter.Associate = proto.AND
		}
		result = append(result, newFilter)
	}
	return result, nil
}
