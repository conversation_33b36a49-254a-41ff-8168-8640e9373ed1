package ads_common_service

import (
	"context"
	json "github.com/json-iterator/go"
	commonProto "gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common/proto"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/dap_indicator_modeling/common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/cache"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/utils/trace_utils"
	"strings"
	"time"

	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/entities"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/external/dap-common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/repository/mysql/indicator_repository"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/injection"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/service/ads_view/proto"
)

var _ injection.DependenceModel = (*AnalyticDimImpl)(nil)
var _ injection.IndicatorDefinition = (*AnalyticDimImpl)(nil)

type AnalyticDimImpl struct {
	*injection.DefaultDepModelImpl
	req         *ads_common.GetPublicDimTableReq
	viewContent *proto.ViewContentNew // 应用表ViewContent
	dapCommon   dap_common.DapCommonService
}

func NewAnalyticDimImpl(req *ads_common.GetPublicDimTableReq) *AnalyticDimImpl {
	return &AnalyticDimImpl{
		req:       req,
		dapCommon: dap_common.NewDapCommonServiceS(),
	}
}

// PreCheckAndPrepareData 对查询结构进行检查并从数据库获取必要数据
func (s *AnalyticDimImpl) PreCheckAndPrepareData(ctx context.Context, repo *indicator_repository.IndicatorRepository) (err error) {
	sctx, sp := trace_utils.StartSpanWithContext(ctx, "AnalyticDimImpl.PreCheckAndPrepareData")
	sp.Name("method: AnalyticDimImpl.PreCheckAndPrepareData")
	defer sp.End()

	timeCostFn := trace_utils.TraceTimeCost(sctx, "Unmarshal s.req.ViewContent")
	err = json.Unmarshal([]byte(s.req.ViewContent), &s.viewContent)
	timeCostFn()
	if err != nil {
		err = errors.Wrapf(err, "解析应用表ViewContent失败")
		return err
	}
	// 从数据库中取相关数据
	if s.viewContent.Dependence == nil {
		return errors.New("服务器内部错误, 应用表依赖模型不存在")
	}

	s.DefaultDepModelImpl = injection.NewDefaultDepModelImpl(s.viewContent.Dependence, &s.viewContent.Dependence.RelationalModel)
	return s.DefaultDepModelImpl.PreLoad(sctx, repo, string(global.Prod))
}

func (s *AnalyticDimImpl) GetDefinitions(ctx context.Context) ([]*proto.Definition, error) {
	var indicatorCodeSet = hashset.New()
	for _, indicatorCode := range s.req.GetIndicatorCodes() {
		indicatorCodeSet.Add(indicatorCode)
	}
	helper := injection.NewConvertHelper(injection.WithVarValues(&injection.VariableContext{
		EmptyVarSelectAll: s.viewContent.EmptyVarSelectAll,
		VarDefs:           s.viewContent.Variables,
		VarVals:           nil,
	}))
	result := make([]*proto.Definition, 0)

	resource, err := s.GetStorageResources(ctx)
	if err != nil {
		return nil, err
	}

	for idx := range s.viewContent.CustomFieldDimensions {
		indicator := &s.viewContent.CustomFieldDimensions[idx]
		// 如果指定了指标编码，则只分析传入的指标编码; 否则分析所有指标
		if !indicatorCodeSet.Empty() && !indicatorCodeSet.Contains(indicator.FieldCode) {
			continue
		}
		definition, err := helper.IndicatorToDefinition(indicator.Alias, indicator, entities.ProjectResourceType(resource.ResourceType))
		if err != nil {
			return nil, err
		}
		result = append(result, definition)
	}
	return result, nil
}

func (s *AnalyticDimImpl) GetStorageResources(ctx context.Context) (resources *commonProto.ResourceInfo, err error) {
	projectCode := s.req.ProjectCode
	if cacheStore, found := ctx.Value(cache.CtxKeyInstance).(cache.LocalCacheI); found {
		cacheK := strings.Join([]string{"GetStorageResources", projectCode}, ":")
		if v, got := cacheStore.Get(ctx, cacheK); got {
			resources = v.(*commonProto.ResourceInfo)
			return
		}
		defer func() {
			if err == nil {
				cacheStore.Set(ctx, cacheK, resources, 5*time.Second)
			}
		}()
	}
	resources, err = s.dapCommon.GetStorageResources(ctx, projectCode)
	return
}

func (s *AnalyticDimImpl) GetDimensionsToBeAnalyzed(ctx context.Context) ([]*proto.Field, error) {
	var ans = make([]*proto.Field, 0)
	// 注入维度定义
	for _, dim := range s.viewContent.Dimensions {
		ans = append(ans, &proto.Field{
			ModelField: proto.ModelField{
				Table:     dim.ObjName,
				Field:     dim.PropName,
				FieldCn:   dim.NameCn,
				Id:        dim.FieldCode,
				FieldType: dim.FieldType,
			},
			DimDefinition: common.FieldViewModelMapToDimViewData(dim.ViewModel),
		})
	}
	return ans, nil
}
