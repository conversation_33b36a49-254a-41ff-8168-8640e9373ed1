package ads_common_service

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/global"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/pkg/helper/test_helper"
	"io/ioutil"
	"testing"

	"github.com/golang/protobuf/jsonpb"
	"github.com/stretchr/testify/assert"
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/internal_indicator/proto/ads_common"
)

const path = "./fixtures/"

func TestGetAnalyticDimension(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	req := &ads_common.GetPublicDimTableReq{}
	bs, err := ioutil.ReadFile(path + "analytic_dimension/" + "debug.json")
	at.Nil(err)
	err = jsonpb.UnmarshalString(string(bs), req)
	at.Nil(err)

	service := NewAdsCommonService()
	rsp, err := service.GetPublicDimTable(ctx, req)
	at.Nil(err)
	var ans []byte
	ans, err = json.MarshalIndent(rsp.Data, "", "  ")
	at.Nil(err)
	fmt.Printf("Rsp: %s\n", string(ans))
}

func TestGenAdsRunSql(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	req := &ads_common.GenAdsRunSqlRequest{}
	bs, err := ioutil.ReadFile(path + "debug.json")
	at.Nil(err)
	err = jsonpb.UnmarshalString(string(bs), req)
	at.Nil(err)

	service := NewAdsCommonService()
	rsp, err := service.GenAdsRunSql(ctx, req)
	at.Nil(err)
	fmt.Printf("Sql: %s\n", rsp.Data.Sql)
}

func TestGenAdsRunSqlSmoke(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()
	global.AppConfig.Log.Level = "INFO"

	fixtures := []string{
		"smoke_dwd_join_dim.json",
		"smoke_dim_join_dwd.json",
	}
	service := NewAdsCommonService()

	for idx, fixture := range fixtures {
		fmt.Printf("%d: %s\n", idx, fixture)
		req := &ads_common.GenAdsRunSqlRequest{}
		bs, err := ioutil.ReadFile(path + fixture)
		at.Nil(err)
		err = jsonpb.UnmarshalString(string(bs), req)
		at.Nil(err)

		rsp, err := service.GenAdsRunSql(ctx, req)
		at.Nil(err)
		fmt.Printf("Sql: %s\n", rsp.Data.Sql)
	}
}

func TestBatchGenSimpleIndicatorExpression(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	req := &ads_common.BatchGenSimpleIndicatorExpressionRequest{}
	bs, err := ioutil.ReadFile(path + "smoke_expr.json")
	at.Nil(err)
	err = jsonpb.UnmarshalString(string(bs), req)
	at.Nil(err)

	service := NewAdsCommonService()
	rsp, err := service.BatchGenSimpleIndicatorExpression(ctx, req)
	at.Nil(err)
	fmt.Printf("Sql: %s\n", rsp.Data)
}

func TestAdvanceTimeSmoke(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	files := []string{"num_filter", "single_ref", "single_table", "by_num", "by_table", "common_dim", "complex_1"}
	sqls := []string{}
	lo.ForEach(files, func(file string, index int) {
		req := &ads_common.GenAdsRunSqlRequest{}
		bs, err := ioutil.ReadFile(path + fmt.Sprintf("advance_time/%s.json", file))
		at.Nil(err)
		err = jsonpb.UnmarshalString(string(bs), req)
		at.Nil(err)

		service := NewAdsCommonService()
		rsp, err := service.GenAdsRunSql(ctx, req)
		at.Nil(err)
		sqls = append(sqls, rsp.Data.Sql)
	})
	lo.ForEach(sqls, func(sql string, index int) {
		fmt.Println("# ========================")
		fmt.Println(sql)
	})
}

func TestAdvanceTime(t *testing.T) {
	at := assert.New(t)
	ctx := context.Background()
	test_helper.Startup()

	req := &ads_common.GenAdsRunSqlRequest{}
	bs, err := ioutil.ReadFile(path + "advance_time/specifier.json")
	at.Nil(err)
	err = jsonpb.UnmarshalString(string(bs), req)
	at.Nil(err)

	service := NewAdsCommonService()
	rsp, err := service.GenAdsRunSql(ctx, req)
	at.Nil(err)
	fmt.Printf("Sql: %s\n", rsp.Data)
}
