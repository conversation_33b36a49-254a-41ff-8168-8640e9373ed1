package proto

import (
	"gitlab.mypaas.com.cn/bigdata/bigdata-dimensional-modeling/old/oldinternal/rpc_call"
)

type EntityLinkType string

const (
	LeftJoin    EntityLinkType = "left join"    // 以左表为主表关联
	RightJoin   EntityLinkType = "right join"   // 以右表为主表关联
	InnerJoin   EntityLinkType = "inner join"   // 按照对应行关联
	FullJoin    EntityLinkType = "full join"    // 所有行关联
	LeftParent  EntityLinkType = "left parent"  // 以左表为父维度关联
	RightParent EntityLinkType = "right parent" // 以右表为父维度关联
)

// FieldRelation 复用之前汇总模型的关联格式，减少变动
type FieldRelation struct {
	Left            string `json:"left"`
	LeftDateFormat  int32  `json:"left_date_format"`
	Right           string `json:"right"`
	RightDateFormat int32  `json:"right_date_format"`
	LogicalRelation string `json:"logical_relation"`
	Operator        string `json:"operator"`
}

// EntityLink 定义view_content的格式
type EntityLink struct {
	FromID           string                   `json:"from_id"`            // 左表code
	ToID             string                   `json:"to_id"`              // 右表code
	JoinType         EntityLinkType           `json:"join_type"`          // 关联类型
	JoinFields       []FieldRelation          `json:"join_fields"`        // 关联字段
	JoinWheres       []rpc_call.Condition     `json:"join_wheres"`        // 关联where
	JoinWhereSql     string                   `json:"join_where_sql"`     // 关联where生成的sql
	JoinQuantityType rpc_call.RelationBizType `json:"join_quantity_type"` // 关联数量类型
}

type ViewContent struct {
	Link []EntityLink `json:"link"`
	Node []Node       `json:"node"`
}

type RelationOperator string

const (
	EQ RelationOperator = "="
	GT RelationOperator = ">"
	GE RelationOperator = ">="
	LT RelationOperator = "<"
	LE RelationOperator = "<="
)

type Field struct {
	IsHide               bool   `json:"is_hide"`
	Name                 string `json:"name"`
	NameCn               string `json:"name_cn"`
	FieldType            string `json:"field_type"`
	AliasName            string `json:"alias_name"`
	Description          string `json:"description"`
	BusinessModelFieldID string `json:"business_model_field_id"`
	PhysicalModelFieldID string `json:"physical_model_field_id"`
	SystemFieldType      string `json:"system_field_type"`
}

type Node struct {
	TableName string  `json:"table_name"`
	Fields    []Field `json:"fields"`
	Id        string  `json:"id"`
	CodeId    string  `json:"code_id"`
	Category  string  `json:"category"`
	Name      string  `json:"name"`
}
