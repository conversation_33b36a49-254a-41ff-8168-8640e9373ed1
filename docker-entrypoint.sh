#!/bin/bash
set -ex
cd $APP_HOME

if [ "${LOCAL_CONFIG}" = "true" ]; then
  echo "[entrypoint] skip dmp-agent, use local config"
else
  echo "[entrypoint] starting dmp-agent"
  ./dmp-agent
fi

if [ "${ENABLE_NACOS_ENCRYPT}" = "false" ]; then
  export SKYLINE_NACOS_ENCRYPTION_ALGORITHM=""
fi

if [ "$1" = 'bigdata-dimensional-modeling' -a "$(id -u)" = '0' ]; then
	echo "[entrypoint] gosu run app"
	exec gosu app "$@"
fi

exec "$@"

